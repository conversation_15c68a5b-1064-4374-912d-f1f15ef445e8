import React, { useState, useEffect } from 'react';
import {
    StyleSheet,
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    Dimensions,
    Alert,
    SafeAreaView
} from 'react-native';
import { Button } from 'react-native-elements';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../common/theme';
import { fonts } from '../common/font';
import { MAIN_COLOR } from '../common/sharedFunctions';
import { useSelector, useDispatch } from 'react-redux';
import { api } from 'common';

const { width, height } = Dimensions.get('window');

export default function ChecklistScreen({ route, navigation }) {
    const { booking, bookingId } = route.params;
    const dispatch = useDispatch();
    const { updateBooking } = api;

    const [checkedItems, setCheckedItems] = useState({});
    const [checklistProgress, setChecklistProgress] = useState(0);

    // Debug logs para diagnosticar el problema
    console.log('=== CHECKLIST SCREEN DEBUG ===');
    console.log('booking:', booking);
    console.log('bookingId:', bookingId);
    console.log('booking.products:', booking?.products);
    console.log('products length:', booking?.products?.length);
    console.log('booking keys:', booking ? Object.keys(booking) : 'no booking');
    console.log('=== END DEBUG ===');

    // Inicializar checklist
    useEffect(() => {
        const products = getProductsToShow();
        if (booking && products) {
            // Inicializar estado del checklist desde la base de datos si existe
            if (booking.checklistStatus) {
                setCheckedItems(booking.checklistStatus);
                const checkedCount = Object.values(booking.checklistStatus).filter(Boolean).length;
                setChecklistProgress((checkedCount / products.length) * 100);
            } else {
                resetChecklist();
            }
        }
    }, [booking]);

    const toggleItem = (productId) => {
        setCheckedItems(prev => {
            const newCheckedItems = {
                ...prev,
                [productId]: !prev[productId]
            };

            // Calcular progreso
            const products = getProductsToShow();
            const totalProducts = products?.length || 0;
            const checkedCount = Object.values(newCheckedItems).filter(Boolean).length;
            setChecklistProgress(totalProducts > 0 ? (checkedCount / totalProducts) * 100 : 0);

            return newCheckedItems;
        });
    };

    const markAllAsChecked = () => {
        const products = getProductsToShow();
        if (products) {
            const allChecked = {};
            products.forEach(product => {
                allChecked[product.id] = true;
            });
            setCheckedItems(allChecked);
            setChecklistProgress(100);
        }
    };

    const resetChecklist = () => {
        setCheckedItems({});
        setChecklistProgress(0);
    };

    const saveChecklistToDatabase = async () => {
        try {
            console.log('Checklist guardado:', checkedItems);
            
            // Actualizar el booking con el estado del checklist
            if (booking) {
                const updatedBooking = {
                    ...booking,
                    checklistStatus: checkedItems,
                    checklistCompleted: checklistProgress === 100
                };
                dispatch(updateBooking(updatedBooking));
            }
            
            Alert.alert(
                'Éxito',
                'Checklist guardado correctamente',
                [{ text: 'OK', onPress: () => navigation.goBack() }]
            );
        } catch (error) {
            console.error('Error al guardar checklist:', error);
            Alert.alert('Error', 'No se pudo guardar el checklist');
        }
    };

    // Extraer todos los productos de todas las órdenes
    const getProductsToShow = () => {
        // Caso 1: Estructura antigua con productos directos
        if (booking?.products && booking.products.length > 0) {
            console.log('Usando productos del booking (estructura antigua):', booking.products);
            return booking.products.map((product, index) => ({
                ...product,
                id: product.id || `PROD-${index + 1}`,
                customerInfo: null
            }));
        }

        // Caso 2: Nueva estructura con orders que contienen products
        if (booking?.orders && Array.isArray(booking.orders)) {
            console.log('Procesando nueva estructura con orders:', booking.orders);
            const allProducts = [];

            booking.orders.forEach((order, orderIndex) => {
                if (order.products && Array.isArray(order.products)) {
                    order.products.forEach((product, productIndex) => {
                        allProducts.push({
                            ...product,
                            id: product.id || `ORDER-${orderIndex + 1}-PROD-${productIndex + 1}`,
                            customerInfo: {
                                customerName: order.customerName,
                                deliveryAddress: order.deliveryAddress?.address,
                                notes: order.notes
                            }
                        });
                    });
                }
            });

            if (allProducts.length > 0) {
                console.log('Productos extraídos de orders:', allProducts);
                return allProducts;
            }
        }

        // Caso 3: Parsear orderDetails (estructura muy antigua)
        if (booking?.orderDetails) {
            console.log('Parseando orderDetails:', booking.orderDetails);
            const orderText = booking.orderDetails;
            const products = [];

            const matches = orderText.match(/(\d+)x?\s*([^,]+)/g);
            if (matches) {
                matches.forEach((match, index) => {
                    const parts = match.match(/(\d+)x?\s*(.+)/);
                    if (parts) {
                        products.push({
                            id: `PARSED-${index + 1}`,
                            name: parts[2].trim(),
                            quantity: parseInt(parts[1]),
                            sku: `PARSED-${index + 1}`,
                            description: `Extraído de: ${match}`,
                            weight: 0.5,
                            customerInfo: null
                        });
                    }
                });
            }

            if (products.length > 0) {
                console.log('Productos parseados de orderDetails:', products);
                return products;
            }
        }

        console.log('No se encontraron productos en ninguna estructura');
        return null;
    };

    const productsToShow = getProductsToShow();

    if (!booking || !productsToShow || productsToShow.length === 0) {
        return (
            <SafeAreaView style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity onPress={() => navigation.goBack()}>
                        <Ionicons name="arrow-back" size={24} color={colors.BLACK} />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Checklist de Productos</Text>
                    <View style={{ width: 24 }} />
                </View>
                <View style={styles.emptyContainer}>
                    <Ionicons name="cube-outline" size={80} color={colors.GREY} />
                    <Text style={styles.emptyText}>No hay productos para verificar</Text>
                    <Text style={styles.debugText}>
                        Debug: {booking ? 'Booking existe' : 'No booking'} |
                        OrderDetails: {booking?.orderDetails ? 'Sí' : 'No'}
                    </Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.goBack()}>
                    <Ionicons name="arrow-back" size={24} color={colors.BLACK} />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Checklist de Productos</Text>
                <View style={{ width: 24 }} />
            </View>

            <View style={styles.progressContainer}>
                <Text style={styles.progressText}>
                    Progreso: {Math.round(checklistProgress)}%
                </Text>
                <View style={styles.progressBarContainer}>
                    <View style={[styles.progressBar, { width: `${checklistProgress}%` }]} />
                </View>
                <Text style={styles.progressDetails}>
                    {Object.values(checkedItems).filter(Boolean).length} de {productsToShow.length} productos verificados
                </Text>
            </View>

            <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
                {productsToShow.map((product, index) => (
                    <View key={product.id} style={styles.productItem}>
                        <TouchableOpacity
                            style={styles.checkbox}
                            onPress={() => toggleItem(product.id)}
                        >
                            <Ionicons 
                                name={checkedItems[product.id] ? "checkmark-circle" : "ellipse-outline"} 
                                size={32} 
                                color={checkedItems[product.id] ? colors.GREEN : MAIN_COLOR} 
                            />
                        </TouchableOpacity>
                        <View style={styles.productInfo}>
                            <Text style={styles.productName}>{product.name}</Text>
                            <Text style={styles.productDetails}>
                                Cantidad: {product.quantity}
                                {product.sku && ` | SKU: ${product.sku}`}
                            </Text>
                            {product.description && (
                                <Text style={styles.productDescription}>
                                    {product.description}
                                </Text>
                            )}
                            {product.weight > 0 && (
                                <Text style={styles.productWeight}>
                                    Peso: {product.weight} kg
                                </Text>
                            )}
                            {product.customerInfo && (
                                <View style={styles.customerInfo}>
                                    <Text style={styles.customerName}>
                                        👤 {product.customerInfo.customerName}
                                    </Text>
                                    <Text style={styles.customerAddress}>
                                        📍 {product.customerInfo.deliveryAddress}
                                    </Text>
                                    {product.customerInfo.notes && (
                                        <Text style={styles.customerNotes}>
                                            📝 {product.customerInfo.notes}
                                        </Text>
                                    )}
                                </View>
                            )}
                        </View>
                    </View>
                ))}
            </ScrollView>

            <View style={styles.buttonContainer}>
                <View style={styles.actionButtons}>
                    <Button
                        title="Marcar Todo"
                        onPress={markAllAsChecked}
                        buttonStyle={[styles.actionButton, { backgroundColor: colors.GREEN }]}
                        titleStyle={styles.buttonText}
                    />
                    <Button
                        title="Reiniciar"
                        onPress={resetChecklist}
                        buttonStyle={[styles.actionButton, { backgroundColor: colors.RED }]}
                        titleStyle={styles.buttonText}
                    />
                </View>
                
                <Button
                    title="Guardar y Volver"
                    onPress={saveChecklistToDatabase}
                    buttonStyle={[styles.saveButton, { backgroundColor: MAIN_COLOR }]}
                    titleStyle={styles.saveButtonText}
                />
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.WHITE,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    headerTitle: {
        fontSize: 18,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
    },
    progressContainer: {
        padding: 20,
        backgroundColor: '#f8f9fa',
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    progressText: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 10,
    },
    progressBarContainer: {
        height: 8,
        backgroundColor: '#e0e0e0',
        borderRadius: 4,
        marginBottom: 8,
    },
    progressBar: {
        height: '100%',
        backgroundColor: MAIN_COLOR,
        borderRadius: 4,
    },
    progressDetails: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.GREY,
    },
    scrollView: {
        flex: 1,
        paddingHorizontal: 20,
    },
    productItem: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        paddingVertical: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    checkbox: {
        marginRight: 15,
        marginTop: 2,
    },
    productInfo: {
        flex: 1,
    },
    productName: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 4,
    },
    productDetails: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginBottom: 4,
    },
    productDescription: {
        fontSize: 13,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        marginBottom: 2,
        fontStyle: 'italic',
    },
    productWeight: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        opacity: 0.7,
    },
    buttonContainer: {
        padding: 20,
        borderTopWidth: 1,
        borderTopColor: '#eee',
    },
    actionButtons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 15,
    },
    actionButton: {
        paddingHorizontal: 30,
        paddingVertical: 12,
        borderRadius: 8,
        minWidth: 120,
    },
    buttonText: {
        fontFamily: fonts.Bold,
        fontSize: 14,
    },
    saveButton: {
        paddingVertical: 15,
        borderRadius: 8,
    },
    saveButtonText: {
        fontFamily: fonts.Bold,
        fontSize: 16,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    emptyText: {
        fontSize: 16,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginTop: 20,
    },
    debugText: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginTop: 10,
        textAlign: 'center',
    },
    customerInfo: {
        marginTop: 8,
        paddingTop: 8,
        borderTopWidth: 1,
        borderTopColor: '#f0f0f0',
    },
    customerName: {
        fontSize: 13,
        fontFamily: fonts.Bold,
        color: MAIN_COLOR,
        marginBottom: 2,
    },
    customerAddress: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        marginBottom: 2,
    },
    customerNotes: {
        fontSize: 11,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        fontStyle: 'italic',
    },
});
