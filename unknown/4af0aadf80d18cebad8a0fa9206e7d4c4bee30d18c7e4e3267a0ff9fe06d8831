{"bookings": {"-OWIWAh4GfUAlpzZap3F": {"bookLater": true, "bookingDate": 1753747864341, "booking_from_web": false, "booking_type_admin": false, "carImage": "https://cdn.pixabay.com/photo/2016/03/31/17/53/color-1293979_640.png", "carType": "PICKUP TRUCK", "car_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/cars%2F-OWIVJ3eB_ob-TYoQaPj?alt=media&token=5f852ec9-ade8-4120-ad64-140df63660a8", "cardPaymentAmount": 0, "cashPaymentAmount": 0, "commission_rate": 15, "commission_type": "percentage", "convenience_fees": "4.77", "coords": [{"latitude": 20.6858928, "longitude": -103.312911}, {"latitude": 20.6814784, "longitude": -103.3107589}, {"latitude": 20.6752755, "longitude": -103.3143675}], "customer": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customer_contact": "+************", "customer_email": "<EMAIL>", "customer_image": "", "customer_name": "<PERSON>", "customer_paid": "36.60", "customer_token": "ExponentPushToken[7hDoiEKvr5_aTo3yEu0eAC]", "deliver_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/bookings%2F-OWIWAh4GfUAlpzZap3F%2Fdeliver_image?alt=media&token=b95daf7d-a3c9-4ca0-80d1-ff2f673e84dc", "deliveryInstructions": "", "deliveryType": "individual_delivery", "deliveryWithBid": false, "discount": "0.00", "distance": "2.25", "driver": "Mo6GG7QnH1fs42t4JtxtoQavRpE2", "driverDeviceId": "id1742389345653", "driverRating": "0", "driver_arrive_time": "1753748039929", "driver_contact": "+523313036516", "driver_image": "", "driver_name": "<PERSON>", "driver_share": "31.83", "driver_token": "ExponentPushToken[7hDoiEKvr5_aTo3yEu0eAC]", "drop": {"add": "Parroquia de San Juan Bosco, Calle Industria, San Juan Bosco, Guadalajara, Jalisco, Mexico", "lat": 20.6752755, "lng": -103.3143675}, "dropAddress": "Parroquia de San Juan Bosco, Calle Industria, San Juan Bosco, Guadalajara, Jalisco, Mexico", "endTime": 1753749143121, "estimate": "36.60", "estimateDistance": "2.25", "estimateTime": 408, "feedback": "", "fleetCommission": "0", "fleet_admin_comission": 5, "fleetadmin": "", "folio": "ENT-2025-001", "id": "-OWIWAh4GfUAlpzZap3F", "optionIndex": 0, "optionSelected": {"amount": 10, "description": "Below 5 KG"}, "orderDetails": "Productos: 4x Monitor, 1x Funda, 4x Teclado", "orderIndex": 1, "originalIndex": 0, "otherPerson": "", "otherPersonPhone": "", "otp": false, "parcelTypeIndex": 0, "parcelTypeSelected": {"amount": 0, "description": "Document"}, "payableAmount": "36.60", "payment_mode": "wallet", "pickUpInstructions": "", "pickup": {"add": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jal., Mexico", "lat": 20.6858928, "lng": -103.312911}, "pickupAddress": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jal., Mexico", "pickup_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/bookings%2F-OWIWAh4GfUAlpzZap3F%2Fpickup_image?alt=media&token=9564f29c-f599-4d14-81ea-5b8e0b19a17c", "prepaid": true, "promo_applied": false, "rating": 5, "reference": "AHHJCO", "roundoff": "0.40", "roundoffCost": "37.00", "startTime": 1753749039344, "status": "COMPLETE", "tableData": {"id": 0}, "total_trip_time": 104, "trip_cost": "36.60", "trip_end_time": "18:32:23", "trip_start_time": "18:30:39", "tripdate": 1753834200000, "usedWalletMoney": "36.60", "vehicleMake": "Ram 500", "vehicleModel": "2024", "vehicle_number": "ERP1234", "waypoints": [{"add": "Oxxo <PERSON>, Calle Juan de Di<PERSON> Robledo, La Huerta, Guadalajara, Jalisco, Mexico", "lat": 20.6814784, "lng": -103.3107589, "source": "search"}]}, "-OWQSBJB9Bce5-smNN9j": {"bookLater": true, "bookingDate": 1753881036025, "booking_from_web": true, "booking_type_admin": true, "carImage": "", "carType": "SEDAN", "car_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/cars%2F-OWQTZzGK18-kJkqXSnA?alt=media&token=f08cf84b-6cfa-40a7-af73-399146c37925", "cardPaymentAmount": 0, "cashPaymentAmount": 0, "convenience_fees": 4.42, "coords": [{"latitude": 20.6858654, "longitude": -103.3129226}, {"latitude": 20.6752755, "longitude": -103.3143675}], "customer": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customer_business_name": "", "customer_contact": "+************", "customer_email": "<EMAIL>", "customer_image": "", "customer_name": "<PERSON>", "customer_paid": "33.91", "customer_token": " ", "deliver_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/bookings%2F-OWQSBJB9Bce5-smNN9j%2Fdeliver_image?alt=media&token=0c53c3cf-ed24-4bd9-9c5a-d5132859d2b8", "deliveryWithBid": false, "discount": "0.00", "distance": 1.79, "driver": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "driverDeviceId": "id1742389345653", "driverRating": "0", "driver_contact": "+523313036516", "driver_image": "", "driver_name": "<PERSON>", "driver_share": "29.49", "driver_token": "ExponentPushToken[7hDoiEKvr5_aTo3yEu0eAC]", "drop": {"add": "Parroquia de San Juan Bosco, Calle Industria, San Juan Bosco, Guadalajara, Jal., México", "lat": 20.6752755, "lng": -103.3143675}, "dropAddress": "Parroquia de San Juan Bosco, Calle Industria, San Juan Bosco, Guadalajara, Jal., México", "endTime": 1753881659518, "estimate": "33.91", "estimateDistance": "1.79", "estimateTime": 378, "fleetCommission": "0", "fleet_admin_comission": 0, "fleetadmin": "", "flightInfo": {"airline": "", "arrivalTime": "", "flightNumber": "", "isAirportTrip": false}, "id": "-OWQSBJB9Bce5-smNN9j", "nombreComercial": "", "numPassengers": 1, "optionIndex": 0, "optionSelected": {"amount": 10, "description": "Below 5 KG"}, "parcelTypeIndex": 0, "parcelTypeSelected": {"amount": 0, "description": "Document"}, "passengerName": "", "payableAmount": "33.91", "paymentType": "", "payment_mode": "wallet", "pickup": {"add": "Calle Álvarez <PERSON> Castillo 890, Santa María, Guadalajara, Jal., México", "lat": 20.6858654, "lng": -103.3129226}, "pickupAddress": "Calle Álvarez <PERSON> Castillo 890, Santa María, Guadalajara, Jal., México", "pickup_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/bookings%2F-OWQSBJB9Bce5-smNN9j%2Fpickup_image?alt=media&token=aab405c3-fb52-443f-b806-f4c6321e29d8", "promo_applied": false, "reference": "KWFVKL", "startTime": 1753881596128, "status": "COMPLETE", "tableData": {"id": 0}, "total_trip_time": 63, "tripInstructions": "", "trip_cost": 33.91, "trip_cost_details": {"base_fare": 0, "convenience_fee_type": "flat", "convenience_fees": 0, "distance_fare": 0, "driver_share": 0, "fleet_admin_fee": 0, "sub_total": 0, "time_fare": 0, "total": 33.91}, "trip_end_time": "7:20:59", "trip_start_time": "7:19:56", "tripdate": 1753967220000, "usedWalletMoney": "33.91", "vehicleMake": "RAM 2500", "vehicleModel": "2025", "vehicle_number": "ERP2025", "voucherNumber": ""}, "-OWQUowLYK9ljrxuBkkI": {"bookLater": true, "bookingDate": 1753881727278, "booking_from_web": true, "booking_type_admin": true, "carImage": "", "carType": "SEDAN", "car_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/cars%2F-OWQTZzGK18-kJkqXSnA?alt=media&token=f08cf84b-6cfa-40a7-af73-399146c37925", "cardPaymentAmount": 0, "cashPaymentAmount": 0, "convenience_fees": 7.17, "coords": [{"latitude": 20.6504855, "longitude": -103.4033887}, {"latitude": 20.6771497, "longitude": -103.3390651}], "customer": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customer_business_name": "", "customer_contact": "+************", "customer_email": "<EMAIL>", "customer_image": "", "customer_name": "<PERSON>", "customer_paid": "57.38", "customer_token": "", "deliver_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/bookings%2F-OWQUowLYK9ljrxuBkkI%2Fdeliver_image?alt=media&token=e98988f4-82ee-4f2c-beb4-1df53d32f44e", "discount": "0.00", "distance": 7.319341118851822, "driver": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "driverDeviceId": "id1742389345653", "driverRating": "0", "driver_contact": "+523313036516", "driver_image": "", "driver_name": "<PERSON>", "driver_share": "NaN", "driver_token": "ExponentPushToken[7hDoiEKvr5_aTo3yEu0eAC]", "drop": {"add": "Centro Joyero, Guadalajara, Jalisco", "lat": 20.6771497, "lng": -103.3390651}, "dropAddress": "Centro Joyero, Guadalajara, Jalisco", "endTime": 1753882237129, "fleetCommission": "0", "fleet_admin_comission": 2.39, "fleetadmin": "", "id": "-OWQUowLYK9ljrxuBkkI", "nombreComercial": "", "numPassengers": 4, "payableAmount": "57.38", "payment_mode": "wallet", "pickup": {"add": "Hotel Presidente InterContinental, Guadalajara", "lat": 20.6504855, "lng": -103.4033887}, "pickupAddress": "Hotel Presidente InterContinental, Guadalajara", "promo_applied": false, "reference": "PUQGBG", "solicitante": "<PERSON>", "startTime": 1753882128136, "status": "COMPLETE", "tableData": {"id": 0}, "total_trip_time": 109, "tripInstructions": "Reunión de negocios importante", "trip_cost": 57.38, "trip_cost_details": {"base_fare": 10, "calculation_details": {"distance": 7.32, "duration_estimate": 0.24, "rate_per_hour": 5, "rate_per_km": 5, "total_wait_time": 0, "waypoints_count": 0}, "convenience_fee_type": "percentage", "convenience_fees": 7.17, "distance_fare": 36.6, "driver_share": 38.25, "fleet_admin_fee": 2.39, "sub_total": 47.82, "time_fare": 1.22, "total": 57.38}, "trip_end_time": "7:30:37", "trip_start_time": "7:28:48", "tripdate": 1755680400000, "usedWalletMoney": "57.38", "vehicleMake": "RAM 2500", "vehicleModel": "2025", "vehicle_number": "ERP2025"}, "-OWRiIIKbyD9kNlnTY0h": {"bookLater": false, "bookingDate": 1753902298335, "booking_from_web": false, "booking_type_admin": true, "carImage": "", "carType": "CAMIONETA", "car_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/cars%2F-OWQTZzGK18-kJkqXSnA?alt=media&token=f08cf84b-6cfa-40a7-af73-399146c37925", "cardPaymentAmount": 0, "cashPaymentAmount": "300.00", "commission_rate": 15, "commission_type": "percentage", "convenience_fees": 0, "coords": [{"latitude": 20.6858654, "longitude": -103.3129226}, {"latitude": 20.6524042, "longitude": -103.4014461}], "customer": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customer_business_name": "", "customer_contact": "+5233111917239", "customer_email": "<EMAIL>", "customer_image": "", "customer_name": "<PERSON>", "customer_paid": "300.00", "customer_token": "", "deliver_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/bookings%2F-OWRiIIKbyD9kNlnTY0h%2Fdeliver_image?alt=media&token=fc91cae0-d4a3-47ab-a15e-82a4cad1f0a1", "deliveryWithBid": false, "delivery_folio": "POSTMAN-TEST-001", "delivery_orders": [{"customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "deliveryAddress": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "orderId": "ORD-MDQC56M2-4MCJOR", "pickupAddress": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "productCount": 3}, {"customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "deliveryAddress": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "orderId": "ORD-MDQC56M3-Z111BX", "pickupAddress": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "productCount": 2}, {"customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "deliveryAddress": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "orderId": "ORD-MDQC56M3-YRD3SZ", "pickupAddress": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "productCount": 2}], "delivery_route_id": "-OWRiIHoWzt5gr9IgFav", "delivery_type": "MULTIPLE_ORDERS", "discount": "0.00", "distance": 15, "driver": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "driverDeviceId": "id1742389345653", "driverRating": "0", "driver_contact": "+523313036516", "driver_image": "", "driver_name": "<PERSON>", "driver_share": "300.00", "driver_token": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]", "drop": {"add": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461, "waypoints": [{"add": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "customerName": "<PERSON>", "instructions": "Entregar en recepción del edificio", "lat": 20.7094478, "lng": -103.4097295, "orderId": "ORD-MDQC56M2-4MCJOR"}, {"add": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "customerName": "Cliente 2 - <PERSON>", "instructions": "<PERSON><PERSON><PERSON> antes de llegar", "lat": 20.674917, "lng": -103.3636746, "orderId": "ORD-MDQC56M3-Z111BX"}]}, "dropAddress": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "endTime": 1753902419127, "estimate": 300, "estimateDistance": 15, "estimateTime": 60, "feedback": "", "fleetCommission": "0", "fleet_admin_comission": 0, "fleetadmin": "", "id": "-OWRiIIKbyD9kNlnTY0h", "nombreComercial": "", "numPassengers": 1, "order_count": 3, "payableAmount": "300.00", "payment_mode": "cash", "pickup": {"add": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "pickupAddress": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "promo_applied": false, "rating": 5, "reference": "QJRYIO", "roundTrip": false, "startTime": 1753902368750, "status": "COMPLETE", "tableData": {"id": 0}, "totalPieces": 18, "totalSKUs": 7, "totalWeight": 16, "total_trip_time": 50, "tripInstructions": "Ruta de prueba con nuevos campos de productos", "trip_cost": 300, "trip_cost_details": {"base_fare": 0, "convenience_fee_type": "flat", "convenience_fees": 0, "distance_fare": 0, "driver_share": 0, "fleet_admin_fee": 0, "sub_total": 0, "time_fare": 0, "total": 300}, "trip_end_time": "13:6:59", "trip_start_time": "13:6:8", "tripdate": 1753902298335, "usedWalletMoney": 0, "vehicleMake": "RAM 2500", "vehicleModel": "2025", "vehicle_number": "ERP2025", "waypoints": [{"add": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "customerName": "<PERSON>", "lat": 20.7094478, "lng": -103.4097295, "orderId": "ORD-MDQC56M2-4MCJOR"}, {"add": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "customerName": "Cliente 2 - <PERSON>", "lat": 20.674917, "lng": -103.3636746, "orderId": "ORD-MDQC56M3-Z111BX"}]}, "-OWS1_ihVcsdlneEMz6y": {"bookLater": false, "bookingDate": 1753907616697, "booking_from_web": false, "booking_type_admin": true, "carImage": "", "carType": "CAMIONETA", "car_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/cars%2F-OWQTZzGK18-kJkqXSnA?alt=media&token=f08cf84b-6cfa-40a7-af73-399146c37925", "cardPaymentAmount": 0, "cashPaymentAmount": "300.00", "commission_rate": 15, "commission_type": "percentage", "convenience_fees": 0, "coords": [{"latitude": 20.6858654, "longitude": -103.3129226}, {"latitude": 20.6524042, "longitude": -103.4014461}], "customer": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customer_business_name": "", "customer_contact": "+5233111917239", "customer_email": "<EMAIL>", "customer_image": "", "customer_name": "<PERSON>", "customer_paid": "300.00", "customer_token": "", "deliver_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/bookings%2F-OWS1_ihVcsdlneEMz6y%2Fdeliver_image?alt=media&token=118e6891-eccc-4db2-9784-8f99049c6054", "deliveryWithBid": false, "delivery_folio": "POSTMAN-TEST-001", "delivery_orders": [{"customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "deliveryAddress": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "orderId": "ORD-MDQFB6A9-UGA7HA", "pickupAddress": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "productCount": 3}, {"customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "deliveryAddress": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "orderId": "ORD-MDQFB6AA-XD67VF", "pickupAddress": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "productCount": 2}, {"customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "deliveryAddress": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "orderId": "ORD-MDQFB6AA-S04OIV", "pickupAddress": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "productCount": 2}], "delivery_route_id": "-OWS1_iBIeZc99taKTqd", "delivery_type": "MULTIPLE_ORDERS", "discount": "0.00", "distance": 15, "driver": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "driverDeviceId": "id1742389345653", "driverRating": "0", "driver_contact": "+523313036516", "driver_image": "", "driver_name": "<PERSON>", "driver_share": "300.00", "driver_token": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]", "drop": {"add": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461, "waypoints": [{"add": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "customerName": "<PERSON>", "instructions": "Entregar en recepción del edificio", "lat": 20.7094478, "lng": -103.4097295, "orderId": "ORD-MDQFB6A9-UGA7HA"}, {"add": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "customerName": "Cliente 2 - <PERSON>", "instructions": "<PERSON><PERSON><PERSON> antes de llegar", "lat": 20.674917, "lng": -103.3636746, "orderId": "ORD-MDQFB6AA-XD67VF"}]}, "dropAddress": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "endTime": 1753907679324, "estimate": 300, "estimateDistance": 15, "estimateTime": 60, "feedback": "", "fleetCommission": "0", "fleet_admin_comission": 0, "fleetadmin": "", "id": "-OWS1_ihVcsdlneEMz6y", "nombreComercial": "", "numPassengers": 1, "order_count": 3, "payableAmount": "300.00", "payment_mode": "cash", "pickup": {"add": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "pickupAddress": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "promo_applied": false, "rating": 5, "reference": "MLQFMO", "roundTrip": false, "startTime": 1753907653436, "status": "COMPLETE", "tableData": {"id": 0}, "totalPieces": 18, "totalSKUs": 7, "totalWeight": 16, "total_trip_time": 26, "tripInstructions": "Ruta de prueba con nuevos campos de productos", "trip_cost": 300, "trip_cost_details": {"base_fare": 0, "convenience_fee_type": "flat", "convenience_fees": 0, "distance_fare": 0, "driver_share": 0, "fleet_admin_fee": 0, "sub_total": 0, "time_fare": 0, "total": 300}, "trip_end_time": "14:34:39", "trip_start_time": "14:34:13", "tripdate": 1753907616697, "usedWalletMoney": 0, "vehicleMake": "RAM 2500", "vehicleModel": "2025", "vehicle_number": "ERP2025", "waypoints": [{"add": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "customerName": "<PERSON>", "lat": 20.7094478, "lng": -103.4097295, "orderId": "ORD-MDQFB6A9-UGA7HA"}, {"add": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "customerName": "Cliente 2 - <PERSON>", "lat": 20.674917, "lng": -103.3636746, "orderId": "ORD-MDQFB6AA-XD67VF"}]}, "-OWakG7-xeaEluS_dLQ1": {"bookLater": false, "bookingDate": 1754070585871, "booking_from_web": false, "booking_type_admin": true, "carImage": "", "carType": "CAMION", "cardPaymentAmount": 0, "cashPaymentAmount": "300", "commission_rate": 15, "commission_type": "percentage", "convenience_fees": 0, "coords": [{"latitude": 20.6858654, "longitude": -103.3129226}, {"latitude": 20.7811243, "longitude": -105.5288272}, {"latitude": 20.6548594, "longitude": -105.2006002}, {"latitude": 20.7811243, "longitude": -105.5288272}], "customer": "-OWafMFZ67ttomBjW9r_", "customer_contact": "", "customer_email": "<EMAIL>", "customer_image": "", "customer_name": "STONE CASAS Y CONSTRUCCIONES", "customer_token": "", "deliveryWithBid": false, "delivery_folio": "OE-19", "delivery_orders": [{"customerId": "-OWafMFZ67ttomBjW9r_", "customerName": "STONE CASAS Y CONSTRUCCIONES", "deliveryAddress": "VIALIDAD INTERNA  6 Int 6, CONDOMINIO MAESTRO EL BANCO , PUNTA DE MITA , NAYARIT, México", "orderId": "ORD-MDT4C63N-G9Y0OB", "pickupAddress": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "productCount": 4}, {"customerId": "-OWae-bPbNufC7-CdQNd", "customerName": "IMPULSORA Y CONSTRUCTORA HS", "deliveryAddress": "av federacion  604, EJIDO IXTAPA, puerto vallarta, JALISCO,", "orderId": "ORD-MDT4C63O-294GIU", "pickupAddress": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "productCount": 1}, {"customerId": "-OWaf3QnCYTcmhCadqiW", "customerName": "SCC ADMINISTRACION Y CONSTRUCCION", "deliveryAddress": "CLUSTER RANCHOS  S/N Int 7, PUNTA DE MITA , NAYARIT, NAYARIT, México", "orderId": "ORD-MDT4C63O-OVNGK1", "pickupAddress": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "productCount": 4}], "delivery_route_id": "-OWakG6PIrEdQUyVAyBW", "delivery_type": "MULTIPLE_ORDERS", "distance": 15, "driver": "-OWSeRH6x8kaAt-WxtfH", "driver_contact": "+523314344857", "driver_name": "JUAN FRANCISCO JAVIER GIL JUAREZ", "driver_share": 300, "driver_token": "", "drop": {"add": "CLUSTER RANCHOS  S/N Int 7, PUNTA DE MITA , NAYARIT, NAYARIT, México", "lat": 20.7811243, "lng": -105.5288272, "waypoints": [{"add": "VIALIDAD INTERNA  6 Int 6, CONDOMINIO MAESTRO EL BANCO , PUNTA DE MITA , NAYARIT, México", "customerName": "STONE CASAS Y CONSTRUCCIONES", "lat": 20.7811243, "lng": -105.5288272, "orderId": "ORD-MDT4C63N-G9Y0OB"}, {"add": "av federacion  604, EJIDO IXTAPA, puerto vallarta, JALISCO,", "customerName": "IMPULSORA Y CONSTRUCTORA HS", "lat": 20.6548594, "lng": -105.2006002, "orderId": "ORD-MDT4C63O-294GIU"}]}, "estimate": 300, "estimateDistance": 15, "estimateTime": 60, "order_count": 3, "payment_mode": "cash", "pickup": {"add": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "reference": "BMUGYB", "roundTrip": false, "status": "NEW", "totalPieces": 2768.88, "totalSKUs": 9, "totalWeight": 7575103.508, "tripInstructions": "Ruta de delivery con 3 órdenes. Folio: OE-19", "trip_cost": 300, "tripdate": 1754070585871, "waypoints": [{"add": "VIALIDAD INTERNA  6 Int 6, CONDOMINIO MAESTRO EL BANCO , PUNTA DE MITA , NAYARIT, México", "customerName": "STONE CASAS Y CONSTRUCCIONES", "lat": 20.7811243, "lng": -105.5288272, "orderId": "ORD-MDT4C63N-G9Y0OB"}, {"add": "av federacion  604, EJIDO IXTAPA, puerto vallarta, JALISCO,", "customerName": "IMPULSORA Y CONSTRUCTORA HS", "lat": 20.6548594, "lng": -105.2006002, "orderId": "ORD-MDT4C63O-294GIU"}]}}, "cancel_reason": [{"label": "Unable to Contact Driver", "value": 0}, {"label": "Vehicle is not moving in my direction", "value": 1}, {"label": "My reason is not listed", "value": 2}, {"label": "Driver denied duty", "value": 3}, {"label": "Driver is taking long time", "value": 4}], "cars": {"-OWQTZzGK18-kJkqXSnA": {"active": true, "approved": true, "carType": "CAMIONETA", "car_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/cars%2F-OWQTZzGK18-kJkqXSnA?alt=media&token=f08cf84b-6cfa-40a7-af73-399146c37925", "createdAt": 1753881398016, "driver": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "id": "-OWQTZzGK18-kJkqXSnA", "other_info": "", "vehicleMake": "RAM 2500", "vehicleModel": "2025", "vehicleNumber": "ERP2025"}}, "cartypes": {"type1": {"base_fare": 10, "cancelSlab": [{"amount": 10, "minsDelayed": 2}, {"amount": 15, "minsDelayed": 4}], "convenience_fee_type": "percentage", "convenience_fees": 15, "extra_info": "Capacidad:", "fleet_admin_fee": 5, "id": "type1", "image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/cartypes%2Ftype1?alt=media&token=057d01c2-9e5a-4f20-81b7-7b419b0cf56c", "min_fare": 10, "name": "CAMIONETA", "pos": 1, "rate_per_hour": 5, "rate_per_unit_distance": 5, "tableData": {"id": 0}}, "type2": {"base_fare": 12, "cancelSlab": [{"amount": 15, "minsDelayed": 2}, {"amount": 20, "minsDelayed": 4}], "convenience_fee_type": "percentage", "convenience_fees": 15, "extra_info": "Capacidad:", "fleet_admin_fee": 10, "id": "type2", "image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/cartypes%2Ftype2?alt=media&token=c398d84e-bd5d-4a9d-9e75-33caeafded92", "min_fare": 20, "name": "CARGA", "pos": 2, "rate_per_hour": 6, "rate_per_unit_distance": 8, "tableData": {"id": 1}}, "type3": {"base_fare": 15, "cancelSlab": [{"amount": 20, "minsDelayed": 2}, {"amount": 25, "minsDelayed": 4}], "convenience_fee_type": "percentage", "convenience_fees": 15, "extra_info": "Capacidad", "fleet_admin_fee": 15, "id": "type3", "image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/cartypes%2Ftype3?alt=media&token=86e124b1-fbe7-4f86-b854-c57e3469a2d0", "min_fare": 30, "name": "CAMION", "pos": 3, "rate_per_hour": 8, "rate_per_unit_distance": 10, "tableData": {"id": 2}}}, "delivery_orders": {"-OWQf_9CELp-df3uBD1M": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "folio": "JAL-2024-GDL-001", "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ1QARX-ZEO9O8", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWQf_6LHnTsAwFerkwS", "status": "ASSIGNED", "timestamps": {"assigned": 1753884807890, "created": 1753884807890}}, "-OWQf_9MtHQRUBZGIpl5": {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "folio": "JAL-2024-GDL-001", "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ1QARY-QUXCY9", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "routeId": "-OWQf_6LHnTsAwFerkwS", "status": "ASSIGNED", "timestamps": {"assigned": 1753884807892, "created": 1753884807892}}, "-OWQgPiVdVcrW9-6nT0f": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "folio": "JAL-2024-GDL-001", "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ1V07I-VGOAEM", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWQgPiLjCpVfQ4ffLz9", "status": "ASSIGNED", "timestamps": {"assigned": 1753885027291, "created": 1753885027291}}, "-OWQgPiW8W4ctHu6GSnA": {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "folio": "JAL-2024-GDL-001", "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ1V07J-5NYMUC", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "routeId": "-OWQgPiLjCpVfQ4ffLz9", "status": "ASSIGNED", "timestamps": {"assigned": 1753885027293, "created": 1753885027293}}, "-OWR-V91acqTbY3Nue00": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "folio": "JAL-2024-GDL-001", "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ4ZUP8-1HP0O4", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWR-V7YEg_psd8mlffe", "status": "ASSIGNED", "timestamps": {"assigned": 1753890292368, "created": 1753890292368}}, "-OWR-V93YAw7jQQ5xRKv": {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "folio": "JAL-2024-GDL-001", "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ4ZUP9-56SFJE", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "routeId": "-OWR-V7YEg_psd8mlffe", "status": "ASSIGNED", "timestamps": {"assigned": 1753890292371, "created": 1753890292371}}, "-OWR48fRs8-tfwnd7bar": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "folio": "JAL-2024-GDL-001", "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ5PZ2O-RHUARG", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWR48f6hzlrxJ1lTpFK", "status": "ASSIGNED", "timestamps": {"assigned": 1753891511017, "created": 1753891511017}}, "-OWR48fTc4yOqn0ka1Ad": {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "folio": "JAL-2024-GDL-001", "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ5PZ2O-9BLJLU", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "routeId": "-OWR48f6hzlrxJ1lTpFK", "status": "ASSIGNED", "timestamps": {"assigned": 1753891511019, "created": 1753891511019}}, "-OWR4NC09w4ffKsC_20H": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "folio": "JAL-2024-GDL-001", "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ5R8ZH-IGD070", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWR4NBrRu2dkHJaco89", "status": "ASSIGNED", "timestamps": {"assigned": 1753891570510, "created": 1753891570510}}, "-OWR4NC1aHlWb62N-nNl": {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "folio": "JAL-2024-GDL-001", "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ5R8ZL-9KS93S", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "routeId": "-OWR4NBrRu2dkHJaco89", "status": "ASSIGNED", "timestamps": {"assigned": 1753891570511, "created": 1753891570511}}, "-OWR54bBkqvQxp9Y6DtX": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "folio": "JAL-2024-GDL-001", "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ5V8I3-14TM5Z", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWR54b3TUaFquAa0MKo", "status": "ASSIGNED", "timestamps": {"assigned": 1753891756505, "created": 1753891756505}}, "-OWR54bCg8D1e0uPMUvs": {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "folio": "JAL-2024-GDL-001", "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ5V8I5-ZKRXCB", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "routeId": "-OWR54b3TUaFquAa0MKo", "status": "ASSIGNED", "timestamps": {"assigned": 1753891756506, "created": 1753891756506}}, "-OWR5FxgvIbktJOFU2fF": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "folio": "JAL-2024-GDL-001", "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ5W8B9-AFUPS7", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWR5FxYVpfck472TQFw", "status": "ASSIGNED", "timestamps": {"assigned": 1753891803000, "created": 1753891803000}}, "-OWR5FxhtuKsf2b1QtA2": {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "folio": "JAL-2024-GDL-001", "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ5W8B9-7WGJ56", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "routeId": "-OWR5FxYVpfck472TQFw", "status": "ASSIGNED", "timestamps": {"assigned": 1753891803002, "created": 1753891803002}}, "-OWR6w_FopSNKTRxnQJ-": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "folio": "JAL-2024-GDL-001", "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ65OFN-RBZADG", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWR6w_3gH93_NAsxkkw", "status": "ASSIGNED", "timestamps": {"assigned": 1753892243803, "created": 1753892243803}}, "-OWR6w_GqUvBZbtRwY-d": {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "folio": "JAL-2024-GDL-001", "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ65OFO-UV4ZC3", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "routeId": "-OWR6w_3gH93_NAsxkkw", "status": "ASSIGNED", "timestamps": {"assigned": 1753892243804, "created": 1753892243804}}, "-OWR7D4Ymn-bDCUYD7vx": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "folio": "JAL-2024-GDL-001", "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ677TW-6MHGKN", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWR7D4QuBSfDPx6R5YY", "status": "ASSIGNED", "timestamps": {"assigned": 1753892315504, "created": 1753892315504}}, "-OWR7D4_NfUb1xWK3TLr": {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "folio": "JAL-2024-GDL-001", "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ677TW-DBRYPD", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "routeId": "-OWR7D4QuBSfDPx6R5YY", "status": "ASSIGNED", "timestamps": {"assigned": 1753892315505, "created": 1753892315505}}, "-OWRAWLcct_W_O3kKdn5": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "folio": "JAL-2024-GDL-001", "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ6PRI0-H9QIKP", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWRAWK5X6NfMnTge0T9", "status": "ASSIGNED", "timestamps": {"assigned": 1753893180894, "created": 1753893180894}}, "-OWRAWLdj7HP2MpJ-b21": {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "folio": "JAL-2024-GDL-001", "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ6PRI1-WUE0GK", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "routeId": "-OWRAWK5X6NfMnTge0T9", "status": "ASSIGNED", "timestamps": {"assigned": 1753893180896, "created": 1753893180896}}, "-OWRCKjAooxlfqGceDus": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON>v. <PERSON> 567, <PERSON><PERSON><PERSON><PERSON>, Jalisco", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5.2, "estimatedFare": 150, "estimatedTime": 15, "folio": "TEST-2025-001", "notes": "", "orderId": "ORD-MDQ6ZZCT-TGWECN", "pickupAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ6ZZCT-DOGI", "name": "Pizza Margherita", "quantity": 2, "sku": "PIZZA-001", "status": "PENDING"}], "routeId": "-OWRCKi9FHJ5_g9nEoVB", "status": "ASSIGNED", "timestamps": {"assigned": 1753893657602, "created": 1753893657602}}, "-OWRFeuelYbipqaif7aa": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON>v. <PERSON> 567, <PERSON><PERSON><PERSON><PERSON>, Jalisco", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5.2, "estimatedFare": 150, "estimatedTime": 15, "folio": "TEST-2025-001", "notes": "", "orderId": "ORD-MDQ7IP4I-C74MKC", "pickupAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ7IP4I-GBKY", "name": "Pizza Margherita", "quantity": 2, "sku": "PIZZA-001", "status": "PENDING"}], "routeId": "-OWRFetzuMM9-SwuLczh", "status": "ASSIGNED", "timestamps": {"assigned": 1753894530785, "created": 1753894530785}}, "-OWRG93Y6O4-lqUzcqrm": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON>v. <PERSON> 567, <PERSON><PERSON><PERSON><PERSON>, Jalisco", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5.2, "estimatedFare": 150, "estimatedTime": 15, "folio": "TEST-2025-001", "notes": "", "orderId": "ORD-MDQ7LFM6-0AAJHV", "pickupAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ7LFM5-GXE2", "name": "Pizza Margherita", "quantity": 2, "sku": "PIZZA-001", "status": "PENDING"}], "routeId": "-OWRG93PMVu0zS0SVDjN", "status": "ASSIGNED", "timestamps": {"assigned": 1753894658482, "created": 1753894658482}}, "-OWRHGSRgG3eZ95QsZQI": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON>v. <PERSON> 567, <PERSON><PERSON><PERSON><PERSON>, Jalisco", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 0, "estimatedFare": 0, "estimatedTime": 0, "folio": "TEST-2025-001", "notes": "", "orderId": "ORD-MDQ7RP8L-02XJE5", "pickupAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ7RP8L-5P6J", "name": "Pizza Margherita", "quantity": 2, "sku": "PIZZA-001", "status": "PENDING"}], "routeId": "-OWRHGSHCH-FGefFMrws", "status": "ASSIGNED", "timestamps": {"assigned": 1753894950801, "created": 1753894950801}}, "-OWRJRPAbbUPUxRCUMLp": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "folio": "TEST-2025-002", "notes": "", "orderId": "ORD-MDQ83WAY-R3I0ZF", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ83WAY-WPVI", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}], "routeId": "-OWRJRNg_tPoZRJtdCqw", "status": "ASSIGNED", "timestamps": {"assigned": 1753895519917, "created": 1753895519917}}, "-OWRJRPCWICc_vfo6uFE": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "folio": "TEST-2025-002", "notes": "", "orderId": "ORD-MDQ83WAY-UH0M7W", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "", "id": "PROD-MDQ83WAY-HHLY", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWRJRNg_tPoZRJtdCqw", "status": "ASSIGNED", "timestamps": {"assigned": 1753895519919, "created": 1753895519919}}, "-OWRK3aDiIyCmCQhWdEZ": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "folio": "TEST-2025-002", "notes": "", "orderId": "ORD-MDQ87FDT-UV316J", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ87FDT-6VUO", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}], "routeId": "-OWRK3a2McQ6DG4gDqgh", "status": "ASSIGNED", "timestamps": {"assigned": 1753895684528, "created": 1753895684528}}, "-OWRK3aF-V0CiHCYlDR0": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "folio": "TEST-2025-002", "notes": "", "orderId": "ORD-MDQ87FDT-4EJZBE", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "", "id": "PROD-MDQ87FDT-0ZS6", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWRK3a2McQ6DG4gDqgh", "status": "ASSIGNED", "timestamps": {"assigned": 1753895684532, "created": 1753895684532}}, "-OWRLhcM9oHp6BvmFIdV": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "folio": "TEST-2025-002", "notes": "", "orderId": "ORD-MDQ8GNAV-UD0GMA", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ8GNAU-22NQ", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}], "routeId": "-OWRLhaxIJttHHsk3Aiy", "status": "ASSIGNED", "timestamps": {"assigned": 1753896114776, "created": 1753896114776}}, "-OWRLhcQ5I5NEt18fvx2": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "folio": "TEST-2025-002", "notes": "", "orderId": "ORD-MDQ8GNAV-7ABUPG", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "", "id": "PROD-MDQ8GNAV-NLKJ", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWRLhaxIJttHHsk3Aiy", "status": "ASSIGNED", "timestamps": {"assigned": 1753896114778, "created": 1753896114778}}, "-OWRNkYpW0l1nMOQf3jO": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "folio": "TEST-2025-002", "notes": "", "orderId": "ORD-MDQ8S52S-TJX734", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ8S52S-USOM", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}], "routeId": "-OWRNkXM1y1MNDfFUs0n", "status": "ASSIGNED", "timestamps": {"assigned": 1753896651031, "created": 1753896651031}}, "-OWRNkYrf99YjWEHrA-E": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "folio": "TEST-2025-002", "notes": "", "orderId": "ORD-MDQ8S52S-JJU8T5", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "", "id": "PROD-MDQ8S52S-PMV0", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWRNkXM1y1MNDfFUs0n", "status": "ASSIGNED", "timestamps": {"assigned": 1753896651032, "created": 1753896651032}}, "-OWRR2EVnvd8MzqOYBr8": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "folio": "TEST-2025-002", "notes": "", "orderId": "ORD-MDQ9AMX6-WI5KZ5", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ9AMX6-HOGJ", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}], "routeId": "-OWRR2D36MappFTGUwHP", "status": "ASSIGNED", "timestamps": {"assigned": 1753897513964, "created": 1753897513964}}, "-OWRR2EW_xv5adRcdZgc": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "folio": "TEST-2025-002", "notes": "", "orderId": "ORD-MDQ9AMX6-UESHB5", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "", "id": "PROD-MDQ9AMX6-4SUS", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "routeId": "-OWRR2D36MappFTGUwHP", "status": "ASSIGNED", "timestamps": {"assigned": 1753897513965, "created": 1753897513965}}, "-OWRVaApQ5T7P6BPkA7s": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "GEOCODE-TEST-001", "notes": "", "orderId": "ORD-MDQA06G7-N9HILT", "pickupAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "products": [{"description": "", "id": "PROD-MDQA06G7-ZO9O", "name": "Documentos Cliente 1", "quantity": 1, "sku": "", "status": "PENDING"}], "routeId": "-OWRVa9OX1jpVpnCGiRI", "status": "ASSIGNED", "timestamps": {"assigned": 1753898705673, "created": 1753898705673}}, "-OWRVaAsVGj6AsTy7xZq": {"customerEmail": "<EMAIL>", "customerId": "customer2_id", "customerName": "Cliente 2 - María", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "GEOCODE-TEST-001", "notes": "", "orderId": "ORD-MDQA06G8-X1DJ8C", "pickupAddress": {"address": "Plaza del Sol, Guadalajara, Jalisco", "lat": 20.6505195, "lng": -103.4013333}, "products": [{"description": "", "id": "PROD-MDQA06G8-4MDP", "name": "Paquete Cliente 2", "quantity": 2, "sku": "", "status": "PENDING"}], "routeId": "-OWRVa9OX1jpVpnCGiRI", "status": "ASSIGNED", "timestamps": {"assigned": 1753898705675, "created": 1753898705675}}, "-OWRWMOvywAH5bfacBvg": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "GEOCODE-TEST-001", "notes": "", "orderId": "ORD-MDQA4I2G-0CG7EX", "pickupAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "products": [{"description": "", "id": "PROD-MDQA4I2G-VZ0H", "name": "Documentos Cliente 1", "quantity": 1, "sku": "", "status": "PENDING"}], "routeId": "-OWRWMOen6BsQhWTMZvb", "status": "ASSIGNED", "timestamps": {"assigned": 1753898907277, "created": 1753898907277}}, "-OWRWMOwMndV-uaE2a_R": {"customerEmail": "<EMAIL>", "customerId": "customer2_id", "customerName": "Cliente 2 - María", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "GEOCODE-TEST-001", "notes": "", "orderId": "ORD-MDQA4I2H-DW5SD9", "pickupAddress": {"address": "Plaza del Sol, Guadalajara, Jalisco", "lat": 20.6505195, "lng": -103.4013333}, "products": [{"description": "", "id": "PROD-MDQA4I2H-7WCX", "name": "Paquete Cliente 2", "quantity": 2, "sku": "", "status": "PENDING"}], "routeId": "-OWRWMOen6BsQhWTMZvb", "status": "ASSIGNED", "timestamps": {"assigned": 1753898907279, "created": 1753898907279}}, "-OWR_ljCBqHZnf7sEekR": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "EMPRESA-ORIGEN-001", "notes": "", "orderId": "ORD-MDQATADB-TB4V43", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDQATADB-158L", "name": "Documentos Cliente 1", "quantity": 1, "sku": "", "status": "PENDING"}], "routeId": "-OWR_litoeX0_uneMLHK", "status": "ASSIGNED", "timestamps": {"assigned": 1753900063707, "created": 1753900063707}}, "-OWR_ljF9dBvwAYhhRDn": {"customerEmail": "<EMAIL>", "customerId": "customer2_id", "customerName": "Cliente 2 - María", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "EMPRESA-ORIGEN-001", "notes": "", "orderId": "ORD-MDQATADC-4MFXP0", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDQATADC-XZGP", "name": "Paquete Cliente 2", "quantity": 2, "sku": "", "status": "PENDING"}], "routeId": "-OWR_litoeX0_uneMLHK", "status": "ASSIGNED", "timestamps": {"assigned": 1753900063708, "created": 1753900063708}}, "-OWRckCvqZzXTQPvHLgk": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "EMPRESA-ORIGEN-001", "notes": "", "orderId": "ORD-MDQBA0EE-D0TAUQ", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDQBA0EE-2S8V", "name": "Documentos Cliente 1", "quantity": 1, "sku": "", "status": "PENDING"}], "routeId": "-OWRckCiFDrcoEN2bp_w", "status": "ASSIGNED", "timestamps": {"assigned": 1753900843929, "created": 1753900843929}}, "-OWRckCxaMHcWcTVUbEU": {"customerEmail": "<EMAIL>", "customerId": "customer2_id", "customerName": "Cliente 2 - María", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "EMPRESA-ORIGEN-001", "notes": "", "orderId": "ORD-MDQBA0EG-1A1EAW", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDQBA0EG-D38C", "name": "Paquete Cliente 2", "quantity": 2, "sku": "", "status": "PENDING"}], "routeId": "-OWRckCiFDrcoEN2bp_w", "status": "ASSIGNED", "timestamps": {"assigned": 1753900843932, "created": 1753900843932}}, "-OWRiII1K2CaIIB2Kcol": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQC56M2-4MCJOR", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQC56M2-PSZ4", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQC56M2-0MAE", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQC56M2-JPKI", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "routeId": "-OWRiIHoWzt5gr9IgFav", "status": "ASSIGNED", "timestamps": {"assigned": 1753902298316, "created": 1753902298316}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, "-OWRiII2mDkHNeJi1NO3": {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQC56M3-Z111BX", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQC56M3-8OS6", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQC56M3-IOSQ", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "routeId": "-OWRiIHoWzt5gr9IgFav", "status": "ASSIGNED", "timestamps": {"assigned": 1753902298319, "created": 1753902298319}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, "-OWRiII4zLjBzY0LVecn": {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQC56M3-YRD3SZ", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQC56M3-VBGG", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQC56M3-ESH7", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "routeId": "-OWRiIHoWzt5gr9IgFav", "status": "ASSIGNED", "timestamps": {"assigned": 1753902298320, "created": 1753902298320}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}, "-OWS1_iNZhuTpXz03iN3": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQFB6A9-UGA7HA", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQFB6A9-G4QR", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQFB6A9-GFU5", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQFB6A9-MWI7", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "routeId": "-OWS1_iBIeZc99taKTqd", "status": "ASSIGNED", "timestamps": {"assigned": 1753907616673, "created": 1753907616673}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, "-OWS1_iPPNjokvt_gmKe": {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQFB6AA-XD67VF", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQFB6AA-E9VQ", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQFB6AA-W4OH", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "routeId": "-OWS1_iBIeZc99taKTqd", "status": "ASSIGNED", "timestamps": {"assigned": 1753907616677, "created": 1753907616677}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, "-OWS1_iQnocPzyRAdQ4B": {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQFB6AA-S04OIV", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQFB6AA-C51W", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQFB6AA-4CN4", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "routeId": "-OWS1_iBIeZc99taKTqd", "status": "ASSIGNED", "timestamps": {"assigned": 1753907616678, "created": 1753907616678}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}, "-OWSc-pV9eTuBguYPMiC": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQL5G0O-32VVSA", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQL5G0O-ZYW9", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQL5G0O-3TUA", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQL5G0O-K6RS", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "routeId": "-OWSc-pJ-Xky7ZGz0jsO", "status": "ASSIGNED", "timestamps": {"assigned": 1753917427057, "created": 1753917427057}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, "-OWSc-pcQfAKM27aviEZ": {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQL5G0P-8YRX7N", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQL5G0P-76A6", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQL5G0P-1ZZ9", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "routeId": "-OWSc-pJ-Xky7ZGz0jsO", "status": "ASSIGNED", "timestamps": {"assigned": 1753917427062, "created": 1753917427062}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, "-OWSc-pdCRYoULXT7MDl": {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQL5G0P-90M4G1", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQL5G0P-CSZJ", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQL5G0P-NHQL", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "routeId": "-OWSc-pJ-Xky7ZGz0jsO", "status": "ASSIGNED", "timestamps": {"assigned": 1753917427063, "created": 1753917427063}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}, "-OWSgPP7itb66rJyQ0sH": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQLU5XD-BTDIWF", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQLU5XD-I6U2", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQLU5XD-CIH0", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQLU5XD-LQTO", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "routeId": "-OWSgPOvYNCWwajhUNqN", "status": "ASSIGNED", "timestamps": {"assigned": 1753918580370, "created": 1753918580370}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, "-OWSgPP9Bf6I9gjJv0Gk": {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQLU5XD-UTO382", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQLU5XD-IINI", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQLU5XD-NSU4", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "routeId": "-OWSgPOvYNCWwajhUNqN", "status": "ASSIGNED", "timestamps": {"assigned": 1753918580373, "created": 1753918580373}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, "-OWSgPPAWOiUp8rDk6Wh": {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQLU5XE-UDLR30", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQLU5XE-HO3O", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQLU5XE-1YHZ", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "routeId": "-OWSgPOvYNCWwajhUNqN", "status": "ASSIGNED", "timestamps": {"assigned": 1753918580374, "created": 1753918580374}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}, "-OWShKgSiFRq-AeccDYx": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQLZD8L-PYE4R9", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQLZD8L-PU06", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQLZD8L-GS0P", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQLZD8L-EYCF", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "routeId": "-OWShKezLM2UmrY7Ysbo", "status": "ASSIGNED", "timestamps": {"assigned": 1753918823212, "created": 1753918823212}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, "-OWShKgYRAL7VLETR8Pw": {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQLZD8N-ZPA01I", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQLZD8M-IBYX", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQLZD8M-X7TF", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "routeId": "-OWShKezLM2UmrY7Ysbo", "status": "ASSIGNED", "timestamps": {"assigned": 1753918823218, "created": 1753918823218}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, "-OWShKg_2mqYuZRRRvyg": {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQLZD8N-MVFAZD", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQLZD8N-2161", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQLZD8N-EIZH", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "routeId": "-OWShKezLM2UmrY7Ysbo", "status": "ASSIGNED", "timestamps": {"assigned": 1753918823219, "created": 1753918823219}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}, "-OWShSJ9UoA0i5Y3ofVA": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQM01E1-KSK75L", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQM01E1-VM7R", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQM01E1-KCCR", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQM01E1-TQN1", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "routeId": "-OWShSJ17oECnuQ_5GxD", "status": "ASSIGNED", "timestamps": {"assigned": 1753918854420, "created": 1753918854420}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, "-OWShSJBqm8zZlc5tI7s": {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQM01E2-F6KDGD", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQM01E2-MLNR", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQM01E2-C32B", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "routeId": "-OWShSJ17oECnuQ_5GxD", "status": "ASSIGNED", "timestamps": {"assigned": 1753918854504, "created": 1753918854504}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, "-OWShSJC32itXg7bz9Zt": {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQM01E2-CPICJ7", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQM01E2-Q1BZ", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQM01E2-12V0", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "routeId": "-OWShSJ17oECnuQ_5GxD", "status": "ASSIGNED", "timestamps": {"assigned": 1753918854506, "created": 1753918854506}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}, "-OWShUnTtZjblV3maKO2": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQM0973-NBUT98", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQM0973-ZCS8", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQM0973-81QB", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQM0973-K8GQ", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "routeId": "-OWShUnJTPRhYHQBeR6o", "status": "ASSIGNED", "timestamps": {"assigned": 1753918864616, "created": 1753918864616}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, "-OWShUnUxlDtPHnr3MOG": {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQM0973-QFPGGW", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQM0973-62LC", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQM0973-82AC", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "routeId": "-OWShUnJTPRhYHQBeR6o", "status": "ASSIGNED", "timestamps": {"assigned": 1753918864618, "created": 1753918864618}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, "-OWShUnVGd6mZRh2nmwu": {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQM0998-N4N4DL", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQM0998-2OX6", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQM0998-TBTW", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "routeId": "-OWShUnJTPRhYHQBeR6o", "status": "ASSIGNED", "timestamps": {"assigned": 1753918864620, "created": 1753918864620}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}, "-OWSqKR0vTWK_6HrsMJA": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQNDX2P-EK7PCT", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQNDX2P-GCVE", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQNDX2P-2JDJ", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQNDX2P-18A5", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "routeId": "-OWSqKPg_Ciz3-oQxguH", "status": "ASSIGNED", "timestamps": {"assigned": 1753921181711, "created": 1753921181711}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, "-OWSqKR2tJe0Qlv3HtqL": {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQNDX2Q-BDG2YR", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQNDX2Q-XIM2", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQNDX2Q-XE1E", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "routeId": "-OWSqKPg_Ciz3-oQxguH", "status": "ASSIGNED", "timestamps": {"assigned": 1753921181715, "created": 1753921181715}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, "-OWSqKR3MDDQZcjDWEWP": {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQNDX2Q-F27KL4", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQNDX2Q-VV9D", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQNDX2Q-L0HU", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "routeId": "-OWSqKPg_Ciz3-oQxguH", "status": "ASSIGNED", "timestamps": {"assigned": 1753921181717, "created": 1753921181717}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}, "-OWaGPwWFpVr2Qjwg7jG": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDSZIUQG-ILJOKV", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDSZIUQG-GPZ7", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDSZIUQG-5KP9", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDSZIUQG-S5BF", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "routeId": "-OWaGPwI258sOfxPz--H", "status": "ASSIGNED", "timestamps": {"assigned": 1754062499627, "created": 1754062499627}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, "-OWaGPwYeX8GJ_NTAzfh": {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDSZIUQH-88R30E", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDSZIUQH-8VX3", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDSZIUQH-7PCU", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "routeId": "-OWaGPwI258sOfxPz--H", "status": "ASSIGNED", "timestamps": {"assigned": 1754062499632, "created": 1754062499632}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, "-OWaGPw_PA-ZzYCO4DnG": {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en horario de oficina", "orderId": "ORD-MDSZIUQH-F8R5HR", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDSZIUQH-19P1", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDSZIUQH-PCRX", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "routeId": "-OWaGPwI258sOfxPz--H", "status": "ASSIGNED", "timestamps": {"assigned": 1754062499633, "created": 1754062499633}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}, "-OWakG6e7HjiTuqOJRqR": {"customerEmail": "<EMAIL>", "customerId": "-OWafMFZ67ttomBjW9r_", "customerName": "STONE CASAS Y CONSTRUCCIONES", "customerPhone": "", "customerToken": "", "deliveryAddress": {"address": "VIALIDAD INTERNA  6 Int 6, CONDOMINIO MAESTRO EL BANCO , PUNTA DE MITA , NAYARIT, México", "lat": 20.7811243, "lng": -105.5288272}, "driverId": "-OWSeRH6x8kaAt-WxtfH", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "OE-19", "notes": "", "orderId": "ORD-MDT4C63N-G9Y0OB", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDT4C63N-9PPQ", "name": "RECOCIDO CAL. 16", "quantity": 300, "sku": "AC26", "status": "PENDING", "weight": 0}, {"description": "", "id": "PROD-MDT4C63N-OWTJ", "name": "CLAVO ESTANDAR 2 1/2", "quantity": 250, "sku": "FRT458", "status": "PENDING", "weight": 0}, {"description": "", "id": "PROD-MDT4C63N-2V34", "name": "CLAVO ESTANDAR 4", "quantity": 154, "sku": "FRT459", "status": "PENDING", "weight": 154}, {"description": "", "id": "PROD-MDT4C63N-JXKK", "name": "VARILLA A MEDIDA 1/4 G-6000 A 6.00 MTS", "quantity": 300, "sku": "AC35", "status": "PENDING", "weight": 450}], "routeId": "-OWakG6PIrEdQUyVAyBW", "status": "ASSIGNED", "timestamps": {"assigned": 1754070585848, "created": 1754070585848}, "totalPieces": 1004, "totalSKUs": 4, "totalWeight": 158716}, "-OWakG6gZhd-jZZ5ePuV": {"customerEmail": "<EMAIL>", "customerId": "-OWae-bPbNufC7-CdQNd", "customerName": "IMPULSORA Y CONSTRUCTORA HS", "customerPhone": "************", "customerToken": "", "deliveryAddress": {"address": "av federacion  604, EJIDO IXTAPA, puerto vallarta, JALISCO,", "lat": 20.6548594, "lng": -105.2006002}, "driverId": "-OWSeRH6x8kaAt-WxtfH", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "OE-19", "notes": "", "orderId": "ORD-MDT4C63O-294GIU", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDT4C63O-U58R", "name": "RECOCIDO CAL. 16", "quantity": 400, "sku": "AC26", "status": "PENDING", "weight": 0}], "routeId": "-OWakG6PIrEdQUyVAyBW", "status": "ASSIGNED", "timestamps": {"assigned": 1754070585851, "created": 1754070585851}, "totalPieces": 400, "totalSKUs": 1, "totalWeight": 0}, "-OWakG6hygDOftJfv9_l": {"customerEmail": "<EMAIL>", "customerId": "-OWaf3QnCYTcmhCadqiW", "customerName": "SCC ADMINISTRACION Y CONSTRUCCION", "customerPhone": "", "customerToken": "", "deliveryAddress": {"address": "CLUSTER RANCHOS  S/N Int 7, PUNTA DE MITA , NAYARIT, NAYARIT, México", "lat": 20.7811243, "lng": -105.5288272}, "driverId": "-OWSeRH6x8kaAt-WxtfH", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "OE-19", "notes": "", "orderId": "ORD-MDT4C63O-OVNGK1", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDT4C63O-XBD3", "name": "RECOCIDO CAL. 16", "quantity": 100, "sku": "AC26", "status": "PENDING", "weight": 0}, {"description": "", "id": "PROD-MDT4C63O-2O0O", "name": "CLAVO ESTANDAR 2 1/2", "quantity": 25, "sku": "FRT458", "status": "PENDING", "weight": 0}, {"description": "", "id": "PROD-MDT4C63O-6I3A", "name": "VARILLA G-42 CORRUGADA 3/8", "quantity": 1001.28, "sku": "AC33", "status": "PENDING", "weight": 6728.6}, {"description": "", "id": "PROD-MDT4C63O-CAUG", "name": "VARILLA G-42 CORRUGADA 1/2", "quantity": 238.6, "sku": "AC29", "status": "PENDING", "weight": 2846.5}], "routeId": "-OWakG6PIrEdQUyVAyBW", "status": "ASSIGNED", "timestamps": {"assigned": 1754070585852, "created": 1754070585852}, "totalPieces": 1364.8799999999999, "totalSKUs": 4, "totalWeight": 7416387.508}, "-OWbRHQ8wYpeHLgWYHVi": {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "ExponentPushToken[qJ5acWCKrVMR-mMlb7GlCr]", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDTB7I65-725AQ2", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDTB7I65-Z2P4", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDTB7I65-7RB1", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDTB7I65-4XDZ", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "routeId": "-OWbRHPtbSWQhUJFbDhG", "status": "ASSIGNED", "timestamps": {"assigned": 1754082125523, "created": 1754082125523}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, "-OWbRHQB5DygcVEnrwD5": {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDTB7I66-HPY8QO", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDTB7I66-TF34", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDTB7I66-J9YD", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "routeId": "-OWbRHPtbSWQhUJFbDhG", "status": "ASSIGNED", "timestamps": {"assigned": 1754082125527, "created": 1754082125527}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, "-OWbRHQCBa6AIzr6vv6z": {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "lastKnownLocation": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "latitude": 20.6596988, "longitude": -103.3496092}, "notes": "Entregar en horario de oficina", "orderId": "ORD-MDTB7I67-D3ZCQT", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDTB7I67-X81F", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDTB7I67-M2B9", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "routeId": "-OWbRHPtbSWQhUJFbDhG", "status": "DELIVERED", "statusNotes": "Entrega completada exitosamente. Cliente satisfecho.", "timestamps": {"assigned": 1754082125528, "created": 1754082125528, "delivered": 1704067200000}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}, "-OWbilPzcdm-gv8ZVvHl": {"customerEmail": "<EMAIL>", "customerId": "customer1_id_example", "customerName": "<PERSON>", "customerPhone": "+5233111917249", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDTE3D15-Q5LKYK", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDTE3D14-02TZ", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDTE3D15-S3XG", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDTE3D15-IDJS", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "routeId": "-OWbilPjyVYVCuCBMikl", "status": "ASSIGNED", "timestamps": {"assigned": 1754086971085, "created": 1754086971085}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, "-OWbilQ2sCyV5qq6ucl-": {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDTE3D15-24LB5O", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDTE3D15-FQ7G", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDTE3D15-DN9O", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "routeId": "-OWbilPjyVYVCuCBMikl", "status": "ASSIGNED", "timestamps": {"assigned": 1754086971091, "created": 1754086971091}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, "-OWbilQ4WgQVr_VsjfWp": {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "driverId": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "folio": "POSTMAN-TEST-001", "notes": "Entregar en horario de oficina", "orderId": "ORD-MDTE3D15-VP3UQ3", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDTE3D15-EPJT", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDTE3D15-5IQX", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "routeId": "-OWbilPjyVYVCuCBMikl", "status": "ASSIGNED", "timestamps": {"assigned": 1754086971093, "created": 1754086971093}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}}, "delivery_routes": {"-OWQf_6LHnTsAwFerkwS": {"deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[7hDoiEKvr5_aTo3yEu0eAC]"}, "estimatedRoute": [{"description": "Inicio - López <PERSON>, Guadalajara", "lat": 20.6597, "lng": -103.3496}, {"description": "Primera entrega - Puerta de Hierro, Zapopan", "lat": 20.6736, "lng": -103.337}, {"description": "Segunda entrega - Plaza Américas, Providencia", "lat": 20.6668, "lng": -103.3918}], "folio": "JAL-2024-GDL-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ1QARX-ZEO9O8", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753884807795, "created": 1753884807795}}, {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ1QARY-QUXCY9", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753884807795, "created": 1753884807795}}], "priority": "HIGH", "routeId": "-OWQf_6LHnTsAwFerkwS", "specialInstructions": "Ruta de delivery en zona metropolitana de Guadalajara - Evitar horarios pico (7-9 AM y 6-8 PM)", "status": "ASSIGNED", "timestamps": {"assigned": 1753884807795, "created": 1753884807795, "lastUpdated": 1753884807795}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle-jalisco.jpg", "plate": "JAL-456-ABC", "type": "CAMIONETA"}}, "-OWQgPiLjCpVfQ4ffLz9": {"deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[7hDoiEKvr5_aTo3yEu0eAC]"}, "estimatedRoute": [{"description": "Inicio - López <PERSON>, Guadalajara", "lat": 20.6597, "lng": -103.3496}, {"description": "Primera entrega - Puerta de Hierro, Zapopan", "lat": 20.6736, "lng": -103.337}, {"description": "Segunda entrega - Plaza Américas, Providencia", "lat": 20.6668, "lng": -103.3918}], "folio": "JAL-2024-GDL-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ1V07I-VGOAEM", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753885027284, "created": 1753885027284}}, {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ1V07J-5NYMUC", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753885027284, "created": 1753885027284}}], "priority": "HIGH", "routeId": "-OWQgPiLjCpVfQ4ffLz9", "specialInstructions": "Ruta de delivery en zona metropolitana de Guadalajara - Evitar horarios pico (7-9 AM y 6-8 PM)", "status": "ASSIGNED", "timestamps": {"assigned": 1753885027284, "created": 1753885027284, "lastUpdated": 1753885027284}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle-jalisco.jpg", "plate": "JAL-456-ABC", "type": "CAMIONETA"}}, "-OWR-V7YEg_psd8mlffe": {"deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[7hDoiEKvr5_aTo3yEu0eAC]"}, "estimatedRoute": [{"description": "Inicio - López <PERSON>, Guadalajara", "lat": 20.6597, "lng": -103.3496}, {"description": "Primera entrega - Puerta de Hierro, Zapopan", "lat": 20.6736, "lng": -103.337}, {"description": "Segunda entrega - Plaza Américas, Providencia", "lat": 20.6668, "lng": -103.3918}], "folio": "JAL-2024-GDL-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ4ZUP8-1HP0O4", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753890292357, "created": 1753890292357}}, {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ4ZUP9-56SFJE", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753890292357, "created": 1753890292357}}], "priority": "HIGH", "routeId": "-OWR-V7YEg_psd8mlffe", "specialInstructions": "Ruta de delivery en zona metropolitana de Guadalajara - Evitar horarios pico (7-9 AM y 6-8 PM)", "status": "ASSIGNED", "timestamps": {"assigned": 1753890292357, "created": 1753890292357, "lastUpdated": 1753890292357}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle-jalisco.jpg", "plate": "JAL-456-ABC", "type": "CAMIONETA"}}, "-OWR48f6hzlrxJ1lTpFK": {"bookingId": "-OWR48glMZJUp0Jx32Vs", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[7hDoiEKvr5_aTo3yEu0eAC]"}, "estimatedRoute": [{"description": "Inicio - López <PERSON>, Guadalajara", "lat": 20.6597, "lng": -103.3496}, {"description": "Primera entrega - Puerta de Hierro, Zapopan", "lat": 20.6736, "lng": -103.337}, {"description": "Segunda entrega - Plaza Américas, Providencia", "lat": 20.6668, "lng": -103.3918}], "folio": "JAL-2024-GDL-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ5PZ2O-RHUARG", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753891511005, "created": 1753891511005}}, {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ5PZ2O-9BLJLU", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753891511005, "created": 1753891511005}}], "priority": "HIGH", "routeId": "-OWR48f6hzlrxJ1lTpFK", "specialInstructions": "Ruta de delivery en zona metropolitana de Guadalajara - Evitar horarios pico (7-9 AM y 6-8 PM)", "status": "ASSIGNED", "timestamps": {"assigned": 1753891511005, "created": 1753891511005, "lastUpdated": 1753891511005}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle-jalisco.jpg", "plate": "JAL-456-ABC", "type": "CAMIONETA"}}, "-OWR4NBrRu2dkHJaco89": {"bookingId": "-OWR4NDKQARP6rw7YNN8", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[7hDoiEKvr5_aTo3yEu0eAC]"}, "estimatedRoute": [{"description": "Inicio - López <PERSON>, Guadalajara", "lat": 20.6597, "lng": -103.3496}, {"description": "Primera entrega - Puerta de Hierro, Zapopan", "lat": 20.6736, "lng": -103.337}, {"description": "Segunda entrega - Plaza Américas, Providencia", "lat": 20.6668, "lng": -103.3918}], "folio": "JAL-2024-GDL-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ5R8ZH-IGD070", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753891570502, "created": 1753891570502}}, {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ5R8ZL-9KS93S", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753891570502, "created": 1753891570502}}], "priority": "HIGH", "routeId": "-OWR4NBrRu2dkHJaco89", "specialInstructions": "Ruta de delivery en zona metropolitana de Guadalajara - Evitar horarios pico de 7-9 AM y 6-8 PM", "status": "ASSIGNED", "timestamps": {"assigned": 1753891570502, "created": 1753891570502, "lastUpdated": 1753891570502}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle-jalisco.jpg", "plate": "JAL-456-ABC", "type": "CAMIONETA"}}, "-OWR54b3TUaFquAa0MKo": {"bookingId": "-OWR54chax_-_MMJTomB", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[7hDoiEKvr5_aTo3yEu0eAC]"}, "estimatedRoute": [{"description": "Inicio - López <PERSON>, Guadalajara", "lat": 20.6597, "lng": -103.3496}, {"description": "Primera entrega - Puerta de Hierro, Zapopan", "lat": 20.6736, "lng": -103.337}, {"description": "Segunda entrega - Plaza Américas, Providencia", "lat": 20.6668, "lng": -103.3918}], "folio": "JAL-2024-GDL-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ5V8I3-14TM5Z", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753891756497, "created": 1753891756497}}, {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ5V8I5-ZKRXCB", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753891756497, "created": 1753891756497}}], "priority": "HIGH", "routeId": "-OWR54b3TUaFquAa0MKo", "specialInstructions": "Ruta de delivery en zona metropolitana de Guadalajara - Evitar horarios pico (7-9 AM y 6-8 PM)", "status": "ASSIGNED", "timestamps": {"assigned": 1753891756497, "created": 1753891756497, "lastUpdated": 1753891756497}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle-jalisco.jpg", "plate": "JAL-456-ABC", "type": "CAMIONETA"}}, "-OWR5FxYVpfck472TQFw": {"bookingId": "-OWR5Fxxr6XZz_aSczVP", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[7hDoiEKvr5_aTo3yEu0eAC]"}, "estimatedRoute": [{"description": "Inicio - López <PERSON>, Guadalajara", "lat": 20.6597, "lng": -103.3496}, {"description": "Primera entrega - Puerta de Hierro, Zapopan", "lat": 20.6736, "lng": -103.337}, {"description": "Segunda entrega - Plaza Américas, Providencia", "lat": 20.6668, "lng": -103.3918}], "folio": "JAL-2024-GDL-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ5W8B9-AFUPS7", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753891802993, "created": 1753891802993}}, {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ5W8B9-7WGJ56", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753891802993, "created": 1753891802993}}], "priority": "HIGH", "routeId": "-OWR5FxYVpfck472TQFw", "specialInstructions": "Ruta de delivery en zona metropolitana de Guadalajara - Evitar horarios pico (7-9 AM y 6-8 PM)", "status": "ASSIGNED", "timestamps": {"assigned": 1753891802993, "created": 1753891802993, "lastUpdated": 1753891802993}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle-jalisco.jpg", "plate": "JAL-456-ABC", "type": "CAMIONETA"}}, "-OWR6w_3gH93_NAsxkkw": {"bookingId": "-OWR6wan7UZGnX9ipFA1", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "estimatedRoute": [{"description": "Inicio - López <PERSON>, Guadalajara", "lat": 20.6597, "lng": -103.3496}, {"description": "Primera entrega - Puerta de Hierro, Zapopan", "lat": 20.6736, "lng": -103.337}, {"description": "Segunda entrega - Plaza Américas, Providencia", "lat": 20.6668, "lng": -103.3918}], "folio": "JAL-2024-GDL-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ65OFN-RBZADG", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753892243795, "created": 1753892243795}}, {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ65OFO-UV4ZC3", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753892243795, "created": 1753892243795}}], "priority": "HIGH", "routeId": "-OWR6w_3gH93_NAsxkkw", "specialInstructions": "Ruta de delivery en zona metropolitana de Guadalajara - Evitar horarios pico (7-9 AM y 6-8 PM)", "status": "ASSIGNED", "timestamps": {"assigned": 1753892243795, "created": 1753892243795, "lastUpdated": 1753892243795}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle-jalisco.jpg", "plate": "JAL-456-ABC", "type": "CAMIONETA"}}, "-OWR7D4QuBSfDPx6R5YY": {"bookingId": "-OWR7D4mnGBbgExSyano", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "estimatedRoute": [{"description": "Inicio - López <PERSON>, Guadalajara", "lat": 20.6597, "lng": -103.3496}, {"description": "Primera entrega - Puerta de Hierro, Zapopan", "lat": 20.6736, "lng": -103.337}, {"description": "Segunda entrega - Plaza Américas, Providencia", "lat": 20.6668, "lng": -103.3918}], "folio": "JAL-2024-GDL-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ677TW-6MHGKN", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753892315495, "created": 1753892315495}}, {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ677TW-DBRYPD", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753892315495, "created": 1753892315495}}], "priority": "HIGH", "routeId": "-OWR7D4QuBSfDPx6R5YY", "specialInstructions": "Ruta de delivery en zona metropolitana de Guadalajara - Evitar horarios pico (7-9 AM y 6-8 PM)", "status": "ASSIGNED", "timestamps": {"assigned": 1753892315495, "created": 1753892315495, "lastUpdated": 1753892315495}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle-jalisco.jpg", "plate": "JAL-456-ABC", "type": "CAMIONETA"}}, "-OWRAWK5X6NfMnTge0T9": {"bookingId": "-OWRAWN9_AEALwz_EdVN", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "estimatedRoute": [{"description": "Inicio - López <PERSON>, Guadalajara", "lat": 20.6597, "lng": -103.3496}, {"description": "Primera entrega - Puerta de Hierro, Zapopan", "lat": 20.6736, "lng": -103.337}, {"description": "Segunda entrega - Plaza Américas, Providencia", "lat": 20.6668, "lng": -103.3918}], "folio": "JAL-2024-GDL-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "jalisco_push_token_001", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 8.5, "estimatedFare": 180, "estimatedTime": 35, "notes": "Entrega en zona corporativa Zapopan - manejar con cuidado", "orderId": "ORD-MDQ6PRI0-H9QIKP", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "Paquete de documentos importantes para oficinas", "id": "PROD-JAL-001", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}, {"description": "Material publicitario para evento corporativo", "id": "PROD-JAL-002", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753893180885, "created": 1753893180885}}, {"customerEmail": "<EMAIL>", "customerId": "jalisco_customer_002", "customerName": "<PERSON>", "customerPhone": "+************", "customerToken": "jalisco_push_token_002", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 6.8, "estimatedFare": 150, "estimatedTime": 25, "notes": "Entrega en Plaza Américas - horario comercial", "orderId": "ORD-MDQ6PRI1-WUE0GK", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "Medicamentos especializados para farmacia", "id": "PROD-JAL-003", "name": "Productos Farmacéuticos", "quantity": 3, "sku": "FARM-003-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753893180885, "created": 1753893180885}}], "priority": "HIGH", "routeId": "-OWRAWK5X6NfMnTge0T9", "specialInstructions": "Ruta de delivery en zona metropolitana de Guadalajara - Evitar horarios pico (7-9 AM y 6-8 PM)", "status": "ASSIGNED", "timestamps": {"assigned": 1753893180885, "created": 1753893180885, "lastUpdated": 1753893180885}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle-jalisco.jpg", "plate": "JAL-456-ABC", "type": "CAMIONETA"}}, "-OWRCKi9FHJ5_g9nEoVB": {"bookingId": "-OWRCKjPdPuLp11enfxu", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "TEST-2025-001", "orderCount": 1, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON>v. <PERSON> 567, <PERSON><PERSON><PERSON><PERSON>, Jalisco", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 5.2, "estimatedFare": 150, "estimatedTime": 15, "notes": "", "orderId": "ORD-MDQ6ZZCT-TGWECN", "pickupAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ6ZZCT-DOGI", "name": "Pizza Margherita", "quantity": 2, "sku": "PIZZA-001", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753893657585, "created": 1753893657585}}], "priority": "NORMAL", "routeId": "-OWRCKi9FHJ5_g9nEoVB", "specialInstructions": "Entregar en recepción", "status": "ASSIGNED", "timestamps": {"assigned": 1753893657585, "created": 1753893657585, "lastUpdated": 1753893657585}, "totalDistance": 5.2, "totalEstimatedFare": 150, "totalEstimatedTime": 15, "vehicle": {"image": "https://example.com/moto.jpg", "plate": "ABC-123", "type": "CAMIONETA"}}, "-OWRFetzuMM9-SwuLczh": {"bookingId": "-OWRFeursFdAobhQS5Yj", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "TEST-2025-001", "orderCount": 1, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON>v. <PERSON> 567, <PERSON><PERSON><PERSON><PERSON>, Jalisco", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 5.2, "estimatedFare": 150, "estimatedTime": 15, "notes": "", "orderId": "ORD-MDQ7IP4I-C74MKC", "pickupAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ7IP4I-GBKY", "name": "Pizza Margherita", "quantity": 2, "sku": "PIZZA-001", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753894530742, "created": 1753894530742}}], "priority": "NORMAL", "routeId": "-OWRFetzuMM9-SwuLczh", "specialInstructions": "Entregar en recepción", "status": "ASSIGNED", "timestamps": {"assigned": 1753894530742, "created": 1753894530742, "lastUpdated": 1753894530742}, "totalDistance": 5.2, "totalEstimatedFare": 150, "totalEstimatedTime": 15, "vehicle": {"image": "https://example.com/moto.jpg", "plate": "ABC-123", "type": "CAMIONETA"}}, "-OWRG93PMVu0zS0SVDjN": {"bookingId": "-OWRG958gj59WBNS8b-E", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "TEST-2025-001", "orderCount": 1, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON>v. <PERSON> 567, <PERSON><PERSON><PERSON><PERSON>, Jalisco", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 5.2, "estimatedFare": 150, "estimatedTime": 15, "notes": "", "orderId": "ORD-MDQ7LFM6-0AAJHV", "pickupAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ7LFM5-GXE2", "name": "Pizza Margherita", "quantity": 2, "sku": "PIZZA-001", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753894658387, "created": 1753894658387}}], "priority": "NORMAL", "routeId": "-OWRG93PMVu0zS0SVDjN", "specialInstructions": "Entregar en recepción", "status": "ASSIGNED", "timestamps": {"assigned": 1753894658387, "created": 1753894658387, "lastUpdated": 1753894658387}, "totalDistance": 5.2, "totalEstimatedFare": 150, "totalEstimatedTime": 15, "vehicle": {"image": "https://example.com/moto.jpg", "plate": "ABC-123", "type": "CAMIONETA"}}, "-OWRHGSHCH-FGefFMrws": {"bookingId": "-OWRHGTpYtW_5W-9JXy6", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "TEST-2025-001", "orderCount": 1, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON>v. <PERSON> 567, <PERSON><PERSON><PERSON><PERSON>, Jalisco", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 0, "estimatedFare": 0, "estimatedTime": 0, "notes": "", "orderId": "ORD-MDQ7RP8L-02XJE5", "pickupAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ7RP8L-5P6J", "name": "Pizza Margherita", "quantity": 2, "sku": "PIZZA-001", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753894950792, "created": 1753894950792}}], "priority": "NORMAL", "routeId": "-OWRHGSHCH-FGefFMrws", "specialInstructions": "Entregar en recepción", "status": "ASSIGNED", "timestamps": {"assigned": 1753894950792, "created": 1753894950792, "lastUpdated": 1753894950792}, "totalDistance": 5.2, "totalEstimatedFare": 0, "totalEstimatedTime": 15, "vehicle": {"image": "https://example.com/moto.jpg", "plate": "ABC-123", "type": "CAMIONETA"}}, "-OWRJRNg_tPoZRJtdCqw": {"bookingId": "-OWRJRPUQ00G5wWjiTFD", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "TEST-2025-002", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "notes": "", "orderId": "ORD-MDQ83WAY-R3I0ZF", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ83WAY-WPVI", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753895519830, "created": 1753895519830}}, {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "notes": "", "orderId": "ORD-MDQ83WAY-UH0M7W", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "", "id": "PROD-MDQ83WAY-HHLY", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753895519830, "created": 1753895519830}}], "priority": "NORMAL", "routeId": "-OWRJRNg_tPoZRJtdCqw", "specialInstructions": "Prueba sin campos individuales - Solo totales generales", "status": "ASSIGNED", "timestamps": {"assigned": 1753895519830, "created": 1753895519830, "lastUpdated": 1753895519830}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cartypes%2Ftype1?alt=media&token=164e9ddb-4017-43c8-95ea-a218d55dfcda", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWRK3a2McQ6DG4gDqgh": {"bookingId": "-OWRK3bjQpuOPOim1MKB", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "TEST-2025-002", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "notes": "", "orderId": "ORD-MDQ87FDT-UV316J", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ87FDT-6VUO", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753895684520, "created": 1753895684520}}, {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "notes": "", "orderId": "ORD-MDQ87FDT-4EJZBE", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "", "id": "PROD-MDQ87FDT-0ZS6", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753895684520, "created": 1753895684520}}], "priority": "NORMAL", "routeId": "-OWRK3a2McQ6DG4gDqgh", "specialInstructions": "Prueba sin campos individuales - Solo totales generales", "status": "ASSIGNED", "timestamps": {"assigned": 1753895684520, "created": 1753895684520, "lastUpdated": 1753895684520}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cartypes%2Ftype1?alt=media&token=164e9ddb-4017-43c8-95ea-a218d55dfcda", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWRLhaxIJttHHsk3Aiy": {"bookingId": "-OWRLhdow-e8kcL3IqXl", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "TEST-2025-002", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "notes": "", "orderId": "ORD-MDQ8GNAV-UD0GMA", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ8GNAU-22NQ", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753896114765, "created": 1753896114765}}, {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "notes": "", "orderId": "ORD-MDQ8GNAV-7ABUPG", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "", "id": "PROD-MDQ8GNAV-NLKJ", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753896114765, "created": 1753896114765}}], "priority": "NORMAL", "routeId": "-OWRLhaxIJttHHsk3Aiy", "specialInstructions": "Prueba sin campos individuales - Solo totales generales", "status": "ASSIGNED", "timestamps": {"assigned": 1753896114765, "created": 1753896114765, "lastUpdated": 1753896114765}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cartypes%2Ftype1?alt=media&token=164e9ddb-4017-43c8-95ea-a218d55dfcda", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWRNkXM1y1MNDfFUs0n": {"bookingId": "-OWRNk_CUeOTUg9RcNVZ", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "TEST-2025-002", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "notes": "", "orderId": "ORD-MDQ8S52S-TJX734", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ8S52S-USOM", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753896651021, "created": 1753896651021}}, {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "notes": "", "orderId": "ORD-MDQ8S52S-JJU8T5", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "", "id": "PROD-MDQ8S52S-PMV0", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753896651021, "created": 1753896651021}}], "priority": "NORMAL", "routeId": "-OWRNkXM1y1MNDfFUs0n", "specialInstructions": "Prueba sin campos individuales - Solo totales generales", "status": "ASSIGNED", "timestamps": {"assigned": 1753896651021, "created": 1753896651021, "lastUpdated": 1753896651021}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cartypes%2Ftype1?alt=media&token=164e9ddb-4017-43c8-95ea-a218d55dfcda", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWRR2D36MappFTGUwHP": {"bookingId": "-OWRR2Eozr4gLkgkbyTd", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "TEST-2025-002", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "notes": "", "orderId": "ORD-MDQ9AMX6-WI5KZ5", "pickupAddress": {"address": "Av<PERSON> <PERSON> 2375, <PERSON><PERSON><PERSON> del Country, 44210 Guadalajara, Jal.", "lat": 20.6597, "lng": -103.3496}, "products": [{"description": "", "id": "PROD-MDQ9AMX6-HOGJ", "name": "Documentos Empresariales", "quantity": 2, "sku": "DOC-001-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753897513876, "created": 1753897513876}}, {"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "estimatedDistance": 7.65, "estimatedFare": 165, "estimatedTime": 30, "notes": "", "orderId": "ORD-MDQ9AMX6-UESHB5", "pickupAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, 45116 <PERSON>, <PERSON><PERSON>.", "lat": 20.6736, "lng": -103.337}, "products": [{"description": "", "id": "PROD-MDQ9AMX6-4SUS", "name": "Material Promocional", "quantity": 1, "sku": "PROMO-002-GDL", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753897513876, "created": 1753897513876}}], "priority": "NORMAL", "routeId": "-OWRR2D36MappFTGUwHP", "specialInstructions": "Prueba sin campos individuales - Solo totales generales", "status": "ASSIGNED", "timestamps": {"assigned": 1753897513876, "created": 1753897513876, "lastUpdated": 1753897513876}, "totalDistance": 15.3, "totalEstimatedFare": 330, "totalEstimatedTime": 60, "vehicle": {"image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cartypes%2Ftype1?alt=media&token=164e9ddb-4017-43c8-95ea-a218d55dfcda", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWRVa9OX1jpVpnCGiRI": {"bookingId": "-OWRVaCUXDCNM-fx-soe", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "GEOCODE-TEST-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "", "orderId": "ORD-MDQA06G7-N9HILT", "pickupAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "products": [{"description": "", "id": "PROD-MDQA06G7-ZO9O", "name": "Documentos Cliente 1", "quantity": 1, "sku": "", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753898705662, "created": 1753898705662}}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id", "customerName": "Cliente 2 - María", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "", "orderId": "ORD-MDQA06G8-X1DJ8C", "pickupAddress": {"address": "Plaza del Sol, Guadalajara, Jalisco", "lat": 20.6505195, "lng": -103.4013333}, "products": [{"description": "", "id": "PROD-MDQA06G8-4MDP", "name": "Paquete Cliente 2", "quantity": 2, "sku": "", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753898705662, "created": 1753898705662}}], "priority": "NORMAL", "routeId": "-OWRVa9OX1jpVpnCGiRI", "specialInstructions": "", "status": "ASSIGNED", "timestamps": {"assigned": 1753898705662, "created": 1753898705662, "lastUpdated": 1753898705662}, "totalDistance": 10, "totalEstimatedFare": 200, "totalEstimatedTime": 40, "vehicle": {"image": "", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWRWMOen6BsQhWTMZvb": {"bookingId": "-OWRWMQGvXueC1XP199x", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "GEOCODE-TEST-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "", "orderId": "ORD-MDQA4I2G-0CG7EX", "pickupAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "products": [{"description": "", "id": "PROD-MDQA4I2G-VZ0H", "name": "Documentos Cliente 1", "quantity": 1, "sku": "", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753898907263, "created": 1753898907263}}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id", "customerName": "Cliente 2 - María", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "", "orderId": "ORD-MDQA4I2H-DW5SD9", "pickupAddress": {"address": "Plaza del Sol, Guadalajara, Jalisco", "lat": 20.6505195, "lng": -103.4013333}, "products": [{"description": "", "id": "PROD-MDQA4I2H-7WCX", "name": "Paquete Cliente 2", "quantity": 2, "sku": "", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753898907263, "created": 1753898907263}}], "priority": "NORMAL", "routeId": "-OWRWMOen6BsQhWTMZvb", "specialInstructions": "", "status": "ASSIGNED", "timestamps": {"assigned": 1753898907263, "created": 1753898907263, "lastUpdated": 1753898907263}, "totalDistance": 10, "totalEstimatedFare": 200, "totalEstimatedTime": 40, "vehicle": {"image": "", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWR_litoeX0_uneMLHK": {"bookingId": "-OWR_lkXuOp4SSH6rtvJ", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "EMPRESA-ORIGEN-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "", "orderId": "ORD-MDQATADB-TB4V43", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDQATADB-158L", "name": "Documentos Cliente 1", "quantity": 1, "sku": "", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753900063694, "created": 1753900063694}}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id", "customerName": "Cliente 2 - María", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "", "orderId": "ORD-MDQATADC-4MFXP0", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDQATADC-XZGP", "name": "Paquete Cliente 2", "quantity": 2, "sku": "", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753900063694, "created": 1753900063694}}], "priority": "NORMAL", "routeId": "-OWR_litoeX0_uneMLHK", "specialInstructions": "", "status": "ASSIGNED", "timestamps": {"assigned": 1753900063694, "created": 1753900063694, "lastUpdated": 1753900063694}, "totalDistance": 10, "totalEstimatedFare": 200, "totalEstimatedTime": 40, "vehicle": {"image": "", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWRckCiFDrcoEN2bp_w": {"bookingId": "-OWRckDxc0h6Gw2WLiBK", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "EMPRESA-ORIGEN-001", "orderCount": 2, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "", "orderId": "ORD-MDQBA0EE-D0TAUQ", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDQBA0EE-2S8V", "name": "Documentos Cliente 1", "quantity": 1, "sku": "", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753900843920, "created": 1753900843920}}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id", "customerName": "Cliente 2 - María", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "", "orderId": "ORD-MDQBA0EG-1A1EAW", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDQBA0EG-D38C", "name": "Paquete Cliente 2", "quantity": 2, "sku": "", "status": "PENDING"}], "status": "ASSIGNED", "timestamps": {"assigned": 1753900843920, "created": 1753900843920}}], "priority": "NORMAL", "routeId": "-OWRckCiFDrcoEN2bp_w", "specialInstructions": "", "status": "ASSIGNED", "timestamps": {"assigned": 1753900843920, "created": 1753900843920, "lastUpdated": 1753900843920}, "totalDistance": 10, "totalEstimatedFare": 200, "totalEstimatedTime": 40, "vehicle": {"image": "", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWRiIHoWzt5gr9IgFav": {"bookingId": "-OWRiIIKbyD9kNlnTY0h", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "POSTMAN-TEST-001", "orderCount": 3, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQC56M2-4MCJOR", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQC56M2-PSZ4", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQC56M2-0MAE", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQC56M2-JPKI", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "status": "ASSIGNED", "timestamps": {"assigned": 1753902298307, "created": 1753902298307}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQC56M3-Z111BX", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQC56M3-8OS6", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQC56M3-IOSQ", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753902298307, "created": 1753902298307}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQC56M3-YRD3SZ", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQC56M3-VBGG", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQC56M3-ESH7", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753902298307, "created": 1753902298307}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}], "priority": "NORMAL", "routeId": "-OWRiIHoWzt5gr9IgFav", "routeTotalPieces": 18, "routeTotalSKUs": 7, "routeTotalWeight": 16, "specialInstructions": "Ruta de prueba con nuevos campos de productos", "status": "ASSIGNED", "timestamps": {"assigned": 1753902298307, "created": 1753902298307, "lastUpdated": 1753902298307}, "totalDistance": 15, "totalEstimatedFare": 300, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle.jpg", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWS1_iBIeZc99taKTqd": {"bookingId": "-OWS1_ihVcsdlneEMz6y", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[d0oNy5PMfdgRlcgzKEZNEp]"}, "folio": "POSTMAN-TEST-001", "orderCount": 3, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQFB6A9-UGA7HA", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQFB6A9-G4QR", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQFB6A9-GFU5", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQFB6A9-MWI7", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "status": "ASSIGNED", "timestamps": {"assigned": 1753907616666, "created": 1753907616666}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQFB6AA-XD67VF", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQFB6AA-E9VQ", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQFB6AA-W4OH", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753907616666, "created": 1753907616666}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQFB6AA-S04OIV", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQFB6AA-C51W", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQFB6AA-4CN4", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753907616666, "created": 1753907616666}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}], "priority": "NORMAL", "routeId": "-OWS1_iBIeZc99taKTqd", "routeTotalPieces": 18, "routeTotalSKUs": 7, "routeTotalWeight": 16, "specialInstructions": "Ruta de prueba con nuevos campos de productos", "status": "ASSIGNED", "timestamps": {"assigned": 1753907616666, "created": 1753907616666, "lastUpdated": 1753907616666}, "totalDistance": 15, "totalEstimatedFare": 300, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle.jpg", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWSc-pJ-Xky7ZGz0jsO": {"bookingId": "-OWSc-puYsqTGgbUSw79", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[xEpiK7Hs4MK13KhYaZELBI]"}, "folio": "POSTMAN-TEST-001", "orderCount": 3, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQL5G0O-32VVSA", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQL5G0O-ZYW9", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQL5G0O-3TUA", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQL5G0O-K6RS", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "status": "ASSIGNED", "timestamps": {"assigned": 1753917427042, "created": 1753917427042}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQL5G0P-8YRX7N", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQL5G0P-76A6", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQL5G0P-1ZZ9", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753917427042, "created": 1753917427042}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQL5G0P-90M4G1", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQL5G0P-CSZJ", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQL5G0P-NHQL", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753917427042, "created": 1753917427042}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}], "priority": "NORMAL", "routeId": "-OWSc-pJ-Xky7ZGz0jsO", "routeTotalPieces": 18, "routeTotalSKUs": 7, "routeTotalWeight": 16, "specialInstructions": "Ruta de prueba con nuevos campos de productos", "status": "ASSIGNED", "timestamps": {"assigned": 1753917427042, "created": 1753917427042, "lastUpdated": 1753917427042}, "totalDistance": 15, "totalEstimatedFare": 300, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle.jpg", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWSgPOvYNCWwajhUNqN": {"bookingId": "-OWSgPPO2V--j_OU6ZbJ", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[xEpiK7Hs4MK13KhYaZELBI]"}, "folio": "POSTMAN-TEST-001", "orderCount": 3, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQLU5XD-BTDIWF", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQLU5XD-I6U2", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQLU5XD-CIH0", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQLU5XD-LQTO", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "status": "ASSIGNED", "timestamps": {"assigned": 1753918580361, "created": 1753918580361}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQLU5XD-UTO382", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQLU5XD-IINI", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQLU5XD-NSU4", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753918580361, "created": 1753918580361}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQLU5XE-UDLR30", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQLU5XE-HO3O", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQLU5XE-1YHZ", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753918580361, "created": 1753918580361}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}], "priority": "NORMAL", "routeId": "-OWSgPOvYNCWwajhUNqN", "routeTotalPieces": 18, "routeTotalSKUs": 7, "routeTotalWeight": 16, "specialInstructions": "Ruta de prueba con nuevos campos de productos", "status": "ASSIGNED", "timestamps": {"assigned": 1753918580361, "created": 1753918580361, "lastUpdated": 1753918580361}, "totalDistance": 15, "totalEstimatedFare": 300, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle.jpg", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWShKezLM2UmrY7Ysbo": {"bookingId": "-OWShKgrU3vZAk7cn1XK", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[xEpiK7Hs4MK13KhYaZELBI]"}, "folio": "POSTMAN-TEST-001", "orderCount": 3, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQLZD8L-PYE4R9", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQLZD8L-PU06", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQLZD8L-GS0P", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQLZD8L-EYCF", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "status": "ASSIGNED", "timestamps": {"assigned": 1753918823116, "created": 1753918823116}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQLZD8N-ZPA01I", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQLZD8M-IBYX", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQLZD8M-X7TF", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753918823116, "created": 1753918823116}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQLZD8N-MVFAZD", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQLZD8N-2161", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQLZD8N-EIZH", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753918823116, "created": 1753918823116}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}], "priority": "NORMAL", "routeId": "-OWShKezLM2UmrY7Ysbo", "routeTotalPieces": 18, "routeTotalSKUs": 7, "routeTotalWeight": 16, "specialInstructions": "Ruta de prueba con nuevos campos de productos", "status": "ASSIGNED", "timestamps": {"assigned": 1753918823116, "created": 1753918823116, "lastUpdated": 1753918823116}, "totalDistance": 15, "totalEstimatedFare": 300, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle.jpg", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWShSJ17oECnuQ_5GxD": {"bookingId": "-OWShSKgLxpZPum_uiUC", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[xEpiK7Hs4MK13KhYaZELBI]"}, "folio": "POSTMAN-TEST-001", "orderCount": 3, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQM01E1-KSK75L", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQM01E1-VM7R", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQM01E1-KCCR", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQM01E1-TQN1", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "status": "ASSIGNED", "timestamps": {"assigned": 1753918854414, "created": 1753918854414}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQM01E2-F6KDGD", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQM01E2-MLNR", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQM01E2-C32B", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753918854414, "created": 1753918854414}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQM01E2-CPICJ7", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQM01E2-Q1BZ", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQM01E2-12V0", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753918854414, "created": 1753918854414}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}], "priority": "NORMAL", "routeId": "-OWShSJ17oECnuQ_5GxD", "routeTotalPieces": 18, "routeTotalSKUs": 7, "routeTotalWeight": 16, "specialInstructions": "Ruta de prueba con nuevos campos de productos", "status": "ASSIGNED", "timestamps": {"assigned": 1753918854414, "created": 1753918854414, "lastUpdated": 1753918854414}, "totalDistance": 15, "totalEstimatedFare": 300, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle.jpg", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWShUnJTPRhYHQBeR6o": {"bookingId": "-OWShUnim0XKm_LzFaQj", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[xEpiK7Hs4MK13KhYaZELBI]"}, "folio": "POSTMAN-TEST-001", "orderCount": 3, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQM0973-NBUT98", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQM0973-ZCS8", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQM0973-81QB", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQM0973-K8GQ", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "status": "ASSIGNED", "timestamps": {"assigned": 1753918864609, "created": 1753918864609}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQM0973-QFPGGW", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQM0973-62LC", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQM0973-82AC", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753918864609, "created": 1753918864609}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQM0998-N4N4DL", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQM0998-2OX6", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQM0998-TBTW", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753918864609, "created": 1753918864609}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}], "priority": "NORMAL", "routeId": "-OWShUnJTPRhYHQBeR6o", "routeTotalPieces": 18, "routeTotalSKUs": 7, "routeTotalWeight": 16, "specialInstructions": "Ruta de prueba con nuevos campos de productos", "status": "ASSIGNED", "timestamps": {"assigned": 1753918864609, "created": 1753918864609, "lastUpdated": 1753918864609}, "totalDistance": 15, "totalEstimatedFare": 300, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle.jpg", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWSqKPg_Ciz3-oQxguH": {"bookingId": "-OWSqKSqbi490roXZsRG", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[xEpiK7Hs4MK13KhYaZELBI]"}, "folio": "POSTMAN-TEST-001", "orderCount": 3, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDQNDX2P-EK7PCT", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDQNDX2P-GCVE", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDQNDX2P-2JDJ", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDQNDX2P-18A5", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "status": "ASSIGNED", "timestamps": {"assigned": 1753921181631, "created": 1753921181631}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDQNDX2Q-BDG2YR", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDQNDX2Q-XIM2", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDQNDX2Q-XE1E", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753921181631, "created": 1753921181631}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en horario de oficina", "orderId": "ORD-MDQNDX2Q-F27KL4", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDQNDX2Q-VV9D", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDQNDX2Q-L0HU", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "status": "ASSIGNED", "timestamps": {"assigned": 1753921181631, "created": 1753921181631}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}], "priority": "NORMAL", "routeId": "-OWSqKPg_Ciz3-oQxguH", "routeTotalPieces": 18, "routeTotalSKUs": 7, "routeTotalWeight": 16, "specialInstructions": "Ruta de prueba con nuevos campos de productos", "status": "ASSIGNED", "timestamps": {"assigned": 1753921181631, "created": 1753921181631, "lastUpdated": 1753921181631}, "totalDistance": 15, "totalEstimatedFare": 300, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle.jpg", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWaGPwI258sOfxPz--H": {"bookingId": "-OWaGPwz7Mkh7O-9Z8d1", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[qJ5acWCKrVMR-mMlb7GlCr]"}, "folio": "POSTMAN-TEST-001", "orderCount": 3, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDSZIUQG-ILJOKV", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDSZIUQG-GPZ7", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDSZIUQG-5KP9", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDSZIUQG-S5BF", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "status": "ASSIGNED", "timestamps": {"assigned": 1754062499619, "created": 1754062499619}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDSZIUQH-88R30E", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDSZIUQH-8VX3", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDSZIUQH-7PCU", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "status": "ASSIGNED", "timestamps": {"assigned": 1754062499619, "created": 1754062499619}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en horario de oficina", "orderId": "ORD-MDSZIUQH-F8R5HR", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDSZIUQH-19P1", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDSZIUQH-PCRX", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "status": "ASSIGNED", "timestamps": {"assigned": 1754062499619, "created": 1754062499619}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}], "priority": "NORMAL", "routeId": "-OWaGPwI258sOfxPz--H", "routeTotalPieces": 18, "routeTotalSKUs": 7, "routeTotalWeight": 16, "specialInstructions": "Ruta de prueba con nuevos campos de productos", "status": "ASSIGNED", "timestamps": {"assigned": 1754062499619, "created": 1754062499619, "lastUpdated": 1754062499619}, "totalDistance": 15, "totalEstimatedFare": 300, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle.jpg", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWakG6PIrEdQUyVAyBW": {"bookingId": "-OWakG7-xeaEluS_dLQ1", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "-OWSeRH6x8kaAt-WxtfH", "name": "JUAN FRANCISCO JAVIER GIL JUAREZ", "phone": "+523314344857", "pushToken": ""}, "folio": "OE-19", "orderCount": 3, "orders": [{"customerEmail": "<EMAIL>", "customerId": "-OWafMFZ67ttomBjW9r_", "customerName": "STONE CASAS Y CONSTRUCCIONES", "customerPhone": "", "customerToken": "", "deliveryAddress": {"address": "VIALIDAD INTERNA  6 Int 6, CONDOMINIO MAESTRO EL BANCO , PUNTA DE MITA , NAYARIT, México", "lat": 20.7811243, "lng": -105.5288272}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "", "orderId": "ORD-MDT4C63N-G9Y0OB", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDT4C63N-9PPQ", "name": "RECOCIDO CAL. 16", "quantity": 300, "sku": "AC26", "status": "PENDING", "weight": 0}, {"description": "", "id": "PROD-MDT4C63N-OWTJ", "name": "CLAVO ESTANDAR 2 1/2", "quantity": 250, "sku": "FRT458", "status": "PENDING", "weight": 0}, {"description": "", "id": "PROD-MDT4C63N-2V34", "name": "CLAVO ESTANDAR 4", "quantity": 154, "sku": "FRT459", "status": "PENDING", "weight": 154}, {"description": "", "id": "PROD-MDT4C63N-JXKK", "name": "VARILLA A MEDIDA 1/4 G-6000 A 6.00 MTS", "quantity": 300, "sku": "AC35", "status": "PENDING", "weight": 450}], "status": "ASSIGNED", "timestamps": {"assigned": 1754070585838, "created": 1754070585838}, "totalPieces": 1004, "totalSKUs": 4, "totalWeight": 158716}, {"customerEmail": "<EMAIL>", "customerId": "-OWae-bPbNufC7-CdQNd", "customerName": "IMPULSORA Y CONSTRUCTORA HS", "customerPhone": "************", "customerToken": "", "deliveryAddress": {"address": "av federacion  604, EJIDO IXTAPA, puerto vallarta, JALISCO,", "lat": 20.6548594, "lng": -105.2006002}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "", "orderId": "ORD-MDT4C63O-294GIU", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDT4C63O-U58R", "name": "RECOCIDO CAL. 16", "quantity": 400, "sku": "AC26", "status": "PENDING", "weight": 0}], "status": "ASSIGNED", "timestamps": {"assigned": 1754070585838, "created": 1754070585838}, "totalPieces": 400, "totalSKUs": 1, "totalWeight": 0}, {"customerEmail": "<EMAIL>", "customerId": "-OWaf3QnCYTcmhCadqiW", "customerName": "SCC ADMINISTRACION Y CONSTRUCCION", "customerPhone": "", "customerToken": "", "deliveryAddress": {"address": "CLUSTER RANCHOS  S/N Int 7, PUNTA DE MITA , NAYARIT, NAYARIT, México", "lat": 20.7811243, "lng": -105.5288272}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "", "orderId": "ORD-MDT4C63O-OVNGK1", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "", "id": "PROD-MDT4C63O-XBD3", "name": "RECOCIDO CAL. 16", "quantity": 100, "sku": "AC26", "status": "PENDING", "weight": 0}, {"description": "", "id": "PROD-MDT4C63O-2O0O", "name": "CLAVO ESTANDAR 2 1/2", "quantity": 25, "sku": "FRT458", "status": "PENDING", "weight": 0}, {"description": "", "id": "PROD-MDT4C63O-6I3A", "name": "VARILLA G-42 CORRUGADA 3/8", "quantity": 1001.28, "sku": "AC33", "status": "PENDING", "weight": 6728.6}, {"description": "", "id": "PROD-MDT4C63O-CAUG", "name": "VARILLA G-42 CORRUGADA 1/2", "quantity": 238.6, "sku": "AC29", "status": "PENDING", "weight": 2846.5}], "status": "ASSIGNED", "timestamps": {"assigned": 1754070585838, "created": 1754070585838}, "totalPieces": 1364.8799999999999, "totalSKUs": 4, "totalWeight": 7416387.508}], "priority": "NORMAL", "routeId": "-OWakG6PIrEdQUyVAyBW", "routeTotalPieces": 2768.88, "routeTotalSKUs": 9, "routeTotalWeight": 7575103.508, "specialInstructions": "", "status": "ASSIGNED", "timestamps": {"assigned": 1754070585838, "created": 1754070585838, "lastUpdated": 1754070585838}, "totalDistance": 15, "totalEstimatedFare": 300, "totalEstimatedTime": 60, "vehicle": {"image": "", "plate": "JX98526", "type": "CAMION"}}, "-OWbRHPtbSWQhUJFbDhG": {"bookingId": "-OWbRHQQ7QVOdsNwZcNG", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": ""}, "folio": "POSTMAN-TEST-001", "orderCount": 3, "orders": [{"customerEmail": "<EMAIL>", "customerId": "pb9R4TC3Ocba5ivcYT5C1rlRG862", "customerName": "<PERSON>", "customerPhone": "+5233111917239", "customerToken": "ExponentPushToken[qJ5acWCKrVMR-mMlb7GlCr]", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDTB7I65-725AQ2", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDTB7I65-Z2P4", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDTB7I65-7RB1", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDTB7I65-4XDZ", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "status": "ASSIGNED", "timestamps": {"assigned": 1754082125514, "created": 1754082125514}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDTB7I66-HPY8QO", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDTB7I66-TF34", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDTB7I66-J9YD", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "status": "ASSIGNED", "timestamps": {"assigned": 1754082125514, "created": 1754082125514}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "lastKnownLocation": {"address": "Av. <PERSON> 1234, Guadalajara, Jalisco", "latitude": 20.6596988, "longitude": -103.3496092}, "notes": "Entregar en horario de oficina", "orderId": "ORD-MDTB7I67-D3ZCQT", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDTB7I67-X81F", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDTB7I67-M2B9", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "status": "DELIVERED", "statusNotes": "Entrega completada exitosamente. Cliente satisfecho.", "timestamps": {"assigned": 1754082125514, "created": 1754082125514, "delivered": 1704067200000}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}], "priority": "NORMAL", "routeId": "-OWbRHPtbSWQhUJFbDhG", "routeTotalPieces": 18, "routeTotalSKUs": 7, "routeTotalWeight": 16, "specialInstructions": "Ruta de prueba con nuevos campos de productos", "status": "ASSIGNED", "timestamps": {"assigned": 1754082125514, "created": 1754082125514, "lastUpdated": 1754083488936}, "totalDistance": 15, "totalEstimatedFare": 300, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle.jpg", "plate": "eydg2638", "type": "CAMIONETA"}}, "-OWbilPjyVYVCuCBMikl": {"bookingId": "-OWbilQORNvQArJRdF9e", "deliveryType": "MULTIPLE_ORDERS", "driver": {"email": "<EMAIL>", "id": "j3NYf7YtOLNcJUTlhUAbzkUOdUM2", "name": "<PERSON>", "phone": "+523313036516", "pushToken": "ExponentPushToken[KGv5TdCx8WNHvVepJvPNMN]"}, "folio": "POSTMAN-TEST-001", "orderCount": 3, "orders": [{"customerEmail": "<EMAIL>", "customerId": "customer1_id_example", "customerName": "<PERSON>", "customerPhone": "+5233111917249", "customerToken": "", "deliveryAddress": {"address": "<PERSON><PERSON><PERSON> 1891, <PERSON><PERSON><PERSON>, Zapopan, Jalisco", "lat": 20.7094478, "lng": -103.4097295}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en recepción del edificio", "orderId": "ORD-MDTE3D15-Q5LKYK", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Contratos y documentos importantes", "id": "PROD-MDTE3D14-02TZ", "name": "Documentos Legales", "quantity": 2, "sku": "DOC-001", "status": "PENDING", "weight": 0.5}, {"description": "Medicamentos para tratamiento", "id": "PROD-MDTE3D15-S3XG", "name": "Medicamentos", "quantity": 1, "sku": "MED-002", "status": "PENDING", "weight": 1.2}, {"description": "Correspondencia certificada", "id": "PROD-MDTE3D15-IDJS", "name": "Sobres Certificados", "quantity": 3, "sku": "SOBRE-003", "status": "PENDING", "weight": 0.1}], "status": "ASSIGNED", "timestamps": {"assigned": 1754086971075, "created": 1754086971075}, "totalPieces": 6, "totalSKUs": 3, "totalWeight": 2.5}, {"customerEmail": "<EMAIL>", "customerId": "customer2_id_example", "customerName": "Cliente 2 - <PERSON>", "customerPhone": "+5233111917240", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 1234, Col. <PERSON>, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "<PERSON><PERSON><PERSON> antes de llegar", "orderId": "ORD-MDTE3D15-24LB5O", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Tablets y accesorios", "id": "PROD-MDTE3D15-FQ7G", "name": "Dispositivos Electrónicos", "quantity": 2, "sku": "ELEC-004", "status": "PENDING", "weight": 2.5}, {"description": "Cables USB y adaptadores", "id": "PROD-MDTE3D15-DN9O", "name": "Cables y Adaptadores", "quantity": 5, "sku": "CABLE-005", "status": "PENDING", "weight": 0.3}], "status": "ASSIGNED", "timestamps": {"assigned": 1754086971075, "created": 1754086971075}, "totalPieces": 7, "totalSKUs": 2, "totalWeight": 6.5}, {"customerEmail": "<EMAIL>", "customerId": "customer3_id_example", "customerName": "Cliente 3 - <PERSON>", "customerPhone": "+5233111917241", "customerToken": "", "deliveryAddress": {"address": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}, "estimatedDistance": 5, "estimatedFare": 100, "estimatedTime": 20, "notes": "Entregar en horario de oficina", "orderId": "ORD-MDTE3D15-VP3UQ3", "pickupAddress": {"address": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jalisco", "lat": 20.6858654, "lng": -103.3129226}, "products": [{"description": "Material educativo", "id": "PROD-MDTE3D15-EPJT", "name": "Libros y Manuales", "quantity": 4, "sku": "BOOK-006", "status": "PENDING", "weight": 1}, {"description": "Suministros de oficina", "id": "PROD-MDTE3D15-5IQX", "name": "Material de Oficina", "quantity": 1, "sku": "OFFICE-007", "status": "PENDING", "weight": 3}], "status": "ASSIGNED", "timestamps": {"assigned": 1754086971075, "created": 1754086971075}, "totalPieces": 5, "totalSKUs": 2, "totalWeight": 7}], "priority": "NORMAL", "routeId": "-OWbilPjyVYVCuCBMikl", "routeTotalPieces": 18, "routeTotalSKUs": 7, "routeTotalWeight": 16, "specialInstructions": "Ruta de prueba con nuevos campos de productos", "status": "ASSIGNED", "timestamps": {"assigned": 1754086971075, "created": 1754086971075, "lastUpdated": 1754086971075}, "totalDistance": 15, "totalEstimatedFare": 300, "totalEstimatedTime": 60, "vehicle": {"image": "https://example.com/vehicle.jpg", "plate": "eydg2638", "type": "CAMIONETA"}}}, "languages": {"-OWIOMLB-X7vcthm2icn": {"createdAt": 1753743859387, "dateLocale": "es-mx", "default": true, "id": "-OWIOMLB-X7vcthm2icn", "keyValuePairs": {"ACCEPTED": "ACEPTADO", "ARRIVED": "LLEGÓ", "AppName": "Nombre de la aplicación", "AppleStoreLink": "Enlace de Apple Store", "Balance": "Balance", "CANCELLED": "CANCELADO", "COMPLETE": "COMPLETO", "CardPaymentAmount": "<PERSON><PERSON> por tarjeta -", "CashPaymentAmount": "Pagado por efectivo -", "CompanyName": "nombre de empresa", "CompanyWebsite": "Sitio web de la empresa", "Customer_paid": "Cliente pagado", "Discounts": "Descuentos", "FacebookHandle": "Enlace de la página de Facebook", "Gross_trip_cost": "Costo de viaje total", "InstagramHandle": "Enlace de la página de Instagram", "NEW": "NUEVO", "PAID": "PAGADO", "PAYMENT_PENDING": "Pago pendiente", "PENDING": "PENDIENTE", "PlayStoreLink": "Play Store Link", "Profit": "Ganancia", "REACHED": "ALCANZÓ", "STARTED": "COMENZÓ", "TwitterHandle": "Enlace de la página de Twitter", "WalletPayment": "<PERSON><PERSON> por billetera", "Withdraw_title": "<PERSON><PERSON><PERSON>", "about_us": "Sobre nosotros", "about_us_content1": "Somos la plataforma de movilidad más grande y una de las mayores proveedores de servicios en línea en línea del mundo.", "about_us_content2": "Administre reservas, solicite cotizaciones o reserve un servicio en línea con nuestro sistema de reservas en línea simple y rápido. Somos una compañía de servicios a pedido que permite a los huéspedes reservar fácilmente varios servicios en línea. Ofrecemos los mejores servicios del país.", "about_us_menu": "Sobre nosotros", "accept": "ACEPTAR", "accept_booking_request": "aceptó su solicitud de reserva.", "accepted_booking": "Tu reserva es aceptada", "account_approve": "Cuenta aprobada", "account_create_successfully": "Cuenta creada con éxito", "actions": "Comportamiento", "active_booking": "Reserva activa", "active_car": "Coche activo", "active_car_delete": "El auto activo no se puede eliminar.", "active_driver": "Repartidores activos", "active_status": "Estado activo", "add": "AGREGAR", "addMoneyTextInputPlaceholder": "Agregar cantidad", "add_admin": "Agregar administrador", "add_admin_title": "Agregar administrador", "add_booking_title": "Agregar reservas", "add_car": "Agregar coche", "add_carType": "Agregar tipo de vehículo", "add_carType_title": "Agregar tipo de coche", "add_car_title": "Agregar coche", "add_cartype_title": "Agregar ve<PERSON>", "add_customer": "Agregar cliente", "add_customer_title": "Agregar cliente", "add_driver": "Agregar repartidor", "add_driver_title": "Agregar repartidor", "add_fleetadmin": "Agregar administrador de flota", "add_fleetadmin_title": "Agregar administrador de flota", "add_language": "<PERSON><PERSON>", "add_money": "Agregar dinero", "add_notification": "Agregar notificaciones", "add_notification_title": "Agregar notificación", "add_promo_title": "Agregar promoción", "add_to_review": "Agregar a la revisión", "add_to_wallet": "Agregar a la billetera", "add_to_wallet_title": "Agregar a la billetera", "addbookinglable": "Agregar reservas", "admin": "Administración", "admin_contact": "Póngase en contacto con su administrador para aprobar la cuenta", "advance_settings": "Configuración avanzada", "alert": "<PERSON><PERSON><PERSON>", "alert_text": "<PERSON><PERSON><PERSON>", "all": "Todo", "alladmin": "Todos los administradores", "alladmins_title": "Todos los administradores", "allow_del_final_img": "Permitir una imagen de destino de entrega", "allow_del_pkp_img": "Permit<PERSON> de selección de entrega", "allow_location": "Permitir ubicación para el mapa en tiempo real", "allow_multi_country": "Permitir la selección de múltiples países", "allow_only": "Necesidades de ubicación mientras usa la aplicación", "always_on": "La ubicación necesita siempre en", "amount": "Cantidad", "amount_must_be_gereater_than_100": "Porque la cantidad de slickpay debe ser mayor que 100", "android": "<PERSON><PERSON>", "apiUrl": "URL API", "app_info": "Información de la aplicación", "app_info_title": "Información de la aplicación", "app_link": "Enlace de la aplicación:", "app_store_deception": "Su negocio debe estar en manos seguras en todo momento. Nos aseguramos de que nunca se quede sin clientes y no se ejecute con pérdida. Más de 500 empresas nos confiaron para ofrecer campañas de marketing de calidad utilizando marketing digital", "app_store_deception1": "La aplicación y para el cliente y el repartidor, no para el inicio de sesión del administrador. Tome 2 teléfonos e instale en cada uno. Registre a un cliente en uno y registre un repartidor en uno. Para el repartidor, siempre seleccione la ubicación Siempre permita iniciar sesión. <PERSON><PERSON>, creando una reserva de un teléfono y recibiendo en el otro dónde inició sesión el repartidor. Use el inicio de sesión de usuario proporcionado en el enlace web para ver el lado administrativo de las cosas.", "apple_signin_error": "Apple Signin no está configurado en desarrollador.appple.com o ya ha utilizado el mismo correo electrónico para registrarse.", "apply": "APLICAR", "apply_promo": "Aplicar promoción", "approve_status": "Aprobar el estado", "approved": "Aprobado", "assign": "ASIGNAR", "assign_driver": "<PERSON><PERSON><PERSON> coductor", "auth_error": "Error de autenticación: verifique sus credenciales ingresadas", "authorization": "Autorización", "auto_dispatch": "Envío automá<PERSON>o", "back": "Volver a iniciar sesión", "bankAccount": "Cuenta bancaria", "bankCode": "<PERSON><PERSON><PERSON> ban<PERSON>", "bankDetails": "<PERSON><PERSON> ban<PERSON>", "bankName": "Nombre del banco", "bank_fields": "Campos regulares bancarios", "base_fare": "Tarifa base", "base_fare_required": "Elija la tarifa base", "best_experience": "Mejor experiencia", "best_service_provider": "El mejor servicio proporcionado", "best_services": "Mejores servicios", "bid": "Licitación", "bill_details": "Detalles de la factura", "bill_details_title": "Detalles de la factura", "blank_message": "No hay registros para mostrar", "body": "<PERSON><PERSON><PERSON>", "book": "Reserva", "book_later": "Reservar más tarde", "book_later_button": "Reservar más tarde", "book_now": "<PERSON><PERSON><PERSON> ahora", "book_now_button": "<PERSON><PERSON><PERSON> ahora", "book_ride": "Reserve su viaje", "book_your_ride_menu": "Hacer una reserva", "book_your_title": "Reserve su servicio", "booked_cab_title": "Gestionar la reserva", "bookingPayment": "Pago de reserva", "booking_cancelled": "La reserva se cancela. IDENTIFICACIÓN :", "booking_chart": "Tabla de reserva", "booking_confirm": "Tu reserva ha sido confirmada", "booking_count": "Recuento de reservas", "booking_date": "<PERSON><PERSON> de reserva", "booking_date_time": "Tiempo de datos de reserva", "booking_details": "Detalles de la reserva", "booking_flow": "Flujo de reserva", "booking_history": "Historia de reserva", "booking_id": "ID de reserva", "booking_is": "Tu reserva es", "booking_ref": "Referencia de reserva", "booking_request": "Solicitudes de reserva", "booking_status": "Estado de reserva", "booking_status_web": "Estado de reserva", "booking_success": "Reserva exitosa. ID de reserva:", "booking_successful": "Reserva exitosa", "booking_title": "Mis reservas", "booking_type": "Tipo de reserva", "bookings_table": "Mesa de reservas", "bookings_table_title": "Mesa de reservas", "camera": "<PERSON><PERSON><PERSON>", "camera_permission_error": "Error de permiso de <PERSON>ámara", "cancel": "CANCELAR", "cancelSlab": "Motivos de cancelación", "cancel_booking": "Cancelar la reserva", "cancel_confirm": "¿Quieres cancelar realmente?", "cancel_messege1": "Tu trabajo con la identificación de reserva", "cancel_messege2": "ha sido cancelado con éxito", "cancel_reason_modal_title": "Razón", "cancel_ride": "Cancelar la reserva", "cancellationFee": "Tarifa de cancelación", "cancellation_reason": "Razón de cancelación", "cancellation_reasons": "Razones de cancelación", "cancellation_reasons_title": "Razón de cancelación", "cancelled_bookings": "Cancelar la reserva", "cancelled_incomplete_booking": "Reserva incompleta cancelada", "carApproved_by_admin": "CAR activo no aprobado por Admin.", "carType_required": "Se requiere coche", "car_add": "Agregar coche para aceptar la reserva", "car_approval": "Aprobación de coche", "car_details_title": "Detalles del vehículo", "car_horn_repeat": "Repita el sonido en el nuevo viaje", "car_image": "Imagen de coche", "car_no_not_found": "Número de vehículo no asignado", "car_type": "Tipo de vehículo", "car_type_blank_error": "Seleccione el tipo de vehículo", "car_type_title": "Tipo de vehículo", "car_view_horizontal": "Vista de la lista de autos horizontal", "card": "Tarjeta", "card_payment_amount": "Monto del pago de la tarjeta", "cars": "Coches", "cars_title": "Coches", "cash": "<PERSON><PERSON>", "cash_booking_false": "El administrador no puede crear una reserva cuando el pago en efectivo se desactiva en el sistema.", "cash_on_delivery": "Efectivo en la entrega", "cash_payment_amount": "Monto de pago en efectivo", "chat_blank": "Por favor escriba algo ...", "chat_input_title": "Escriba algo agradable ...", "chat_not_found": "No se encuentra la historia de chat", "chat_requested": ": <PERSON><PERSON><PERSON>", "chat_title": "<PERSON><PERSON><PERSON>", "check_approve_status": "Verifique el estado de aprobación", "check_email": "Revise su bandeja de entrada de correo electrónico", "check_mobile": "Marque su casilla de masaje", "choose_image_first": "Elija la imagen primero.", "close": "CERRAR", "code": "Código", "code_already_avilable": "Esta promoción ya disponible", "code_colon": "Código:", "company_phone": "Teléfono de la compañía", "complain": "<PERSON><PERSON><PERSON>", "complain_date": "<PERSON><PERSON>", "complain_title": "QUEJARSE", "complete_payment": "<PERSON>go completo", "complete_ride": "Trabajo completo", "completed_bookings": "Reserva completa", "confirm": "CONFIRMAR", "confirm_booking": "Confirmar la reserva", "confirm_password": "<PERSON><PERSON>", "confirm_password_not_match_err": "Confirmar que la contraseña no coincida", "contact": "Contacto", "contact_email": "Correo electrónico de contacto", "contact_input_error": "Ingrese un correo electrónico o número de teléfono móvil válido.", "contact_placeholder": "Número de teléfono móvil o correo electrónico", "contact_us": "Cont<PERSON><PERSON><PERSON>s", "contentType": "Tipo de contenido", "convenience_fee": "Tarifas de conveniencia", "convenience_fee_required": "Elija tarifas de conveniencia", "convenience_fee_type": "Tipo de tarifa de conveniencia", "convenience_fee_type_required": "Elija el tipo de tarifa de conveniencia", "convert_button": "Convertir", "convert_to_driver": "Convertir a Repartidor ", "convert_to_mile": "Convertir a milla", "convert_to_rider": "Convertirse en cliente", "cost": "COSTO", "country_1": "<PERSON><PERSON>", "country_blank_error": "Seleccione el país", "country_restriction": "Restricción de país de autocompletado", "create_driver": "<PERSON><PERSON><PERSON> repartidor ", "create_new_user": "Creando un nuevo usuario, espere ...", "create_rider": "Crear cliente", "createdAt": "<PERSON><PERSON><PERSON> fecha", "credited": "Acreditado", "currency_code": "Código de divisas", "currency_settings": "Configuración de divisas", "currency_symbol": "Símbolo de moneda", "customMobileOTP": "Habilitar OTP móvil personalizado", "customer": "Cliente", "customer_contact": "Contacto con el cliente", "customer_email": "Correo electrónico del cliente", "customer_id": "ID de cliente", "customer_info": "Información del cliente", "customer_name": "Nombre del cliente", "daily": "A DIARIO", "dashboard_text": "Panel", "date": "<PERSON><PERSON>", "dateLocale": "Fecha local", "date_time": "<PERSON><PERSON>", "debited": "Debitado", "delete": "Bo<PERSON>r", "delete_account_lebel": "Eliminar cuenta", "delete_account_modal_subtitle": "¿Quieres eliminar tu cuenta?", "delete_account_modal_title": "Confirmación", "delete_account_msg": "El usuario puede eliminar todos sus datos del sistema eliminando la cuenta de la página de perfil en la aplicación móvil.", "delete_account_para1": "Todos sus datos se purgarán desde el sistema. Su imagen de perfil, correo electrónico, número de teléfono, inicios de sesión sociales, incluido el inicio de sesión de Google y todo el historial de reservas, todo se eliminará permanentemente.", "delete_account_para2": "Los datos y la cuenta eliminados del usuario son irrecuperables.", "delete_account_subheading": "Una vez que elimine su cuenta:", "delete_message": "¿Estás seguro de que quieres eliminar esta fila?", "delete_your_car": "¿Quieres eliminar tu coche?", "deliveryDetailMissing": "Por favor ingrese el nombre de la persona receptora", "deliveryInstructions": "Instrucciones de reserva", "deliveryPerson": "Nombre de la persona receptora", "deliveryPersonPhone": "Recibir persona por teléfono no", "delivery_information": "Información de reserva", "demo_mode": "Restringido en la aplicación de demostración.", "description": "Descripción", "device_id": "ID de dispositivo", "device_type": "Tipo de dispositivo", "disable_cash": "Deshabilitar los pagos en efectivo", "disable_online": "Deshabilitar los pagos en línea", "disclaimer": "Descargo de responsabilidad", "disclaimer_text": "Esta aplicación recopila datos de ubicación para habilitar la 'ubicación del vehículo de búsqueda', 'ruta de viaje y navegación', 'movimiento del vehículo en tiempo real', incluso cuando la aplicación está cerrada o no en uso.", "discount": "Descuento", "discount_ammount": "Cantidad de descuento", "distance": "DISTANCIA", "distance_web": "Distancia", "documents": "Documentos", "documents_title": "Documentos", "done": "HECHO", "dont_cancel_text": "No canceles", "drag_map": "Rasta el mapa para seleccionar la dirección", "driver": "Repartidor", "driverRadius": "Radio del repartidor", "driver_accept_low_cost_capacity_booking": "El repartidor acepta la reserva de bajo costo/capacidad", "driver_active": "Estado activo del repartidor", "driver_active_staus_choose": "Estado activo del repartidor Elija", "driver_assign_messege": "El repartidor se asignará pronto.", "driver_cancelled_booking": "Reserva cancelada del repartidor", "driver_completed_ride": "Has llegado al destino. Complete el pago.", "driver_contact": "Contacto del repartidor", "driver_distance": "El repartidor llegará a", "driver_earning": "Historial de ganancias del repartidor", "driver_earning_title": "Historial de ganancias del repartidor", "driver_email": "Correo electrónico del repartidor", "driver_finding_alert": "Encontrar repartidores para ti", "driver_id": "ID de repartidor", "driver_info": "Información del repartidor", "driver_journey_msg": "El repartidor ha comenzado. Tu identificación de reserva es", "driver_name": "Nombre del repartidor", "driver_near": "Repartidor cerca de ti", "driver_required": "Repartidor re<PERSON>", "driver_setting": "Configuración del repartidor", "driver_share": "Compartir el repartidor", "driver_threshold": "Umbral del repartidor", "drivers": "Repartidores", "drivers_title": "Repartidores", "driving_license_back": "Licencia de conducir de regreso", "driving_license_font": "Fuente de licencia de conducir", "drop_address": "Dirección", "drop_address_from_map": "Seleccione la dirección de soltar desde el mapa", "drop_location": "Ubicación de destino", "drop_location_blank_error": "Seleccionar ubicación de destino", "earning_amount": "Cantidad de ganancias", "earning_reports": "Informes de ganancias", "earning_reports_title": "Informes de ganancias", "edit": "<PERSON><PERSON>", "edit_json": "<PERSON><PERSON>", "editcar": "Editar coche", "email": "Correo electrónico", "email_id": "Ingrese ID de correo electrónico", "email_login": "Inicio de sesión de correo electrónico", "email_msg": "Esperamos que hayas disfrutado tu viaje con nosotros.", "email_or_mobile_issue": "Verifique su identificación de correo electrónico o número de teléfono móvil. Nota: El número de móvil debe estar en formato internacional, p.  9199998888877", "email_placeholder": "Correo electrónico", "email_send": "Envío de correo electrónico", "email_use": "Modo de autenticación de correo electrónico", "emergency": "Emergencia", "end_date": "Fecha final", "end_time": "Tiempo de finalización", "enter_code": "Ingrese el código", "estimate_fare_text": "Esta es solo su tarifa estimada", "export": "Exportar", "extra_info": "Información adicional", "facebook_login_auth_error": "Error de inicio de sesión de Facebook:", "feedback": "Comentario", "fill_email_first": "Agregue el correo electrónico primero y luego tome la imagen del documento", "first_admin_deleted": "El primer administrador no se puede eliminar", "first_name": "Nombre", "first_name_placeholder": "Nombre", "first_page_tooltip": "Primera página", "firstname": "Nombre", "fix": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat": "Departamento", "flat_minorder_maxorder": "En el tipo de promoción de plano, su valor de pedido mínimo no puede exceder el descuento máximo.", "fleet_admin_comission": "Comisión Fleetadmin", "fleet_admin_fee": "Tarifa de administración de la flota", "fleet_admin_fee_required": "Se requiere tarifa de administración de la flota", "fleetadmin_earning_reports": "<PERSON><PERSON><PERSON> G<PERSON>", "fleetadmin_id": "Identificación de Fleetadmin", "fleetadmin_name": "Nombre de flota", "fleetadmins": "Administradores de la flota", "fleetadmins_title": "Administradores de la flota", "follow_facebook": "Síguenos en Facebook", "follow_instagram": "Síguenos en Instagram", "follow_twitter": "Síguenos en Twitter", "for_other_person": "Reserva para otra persona", "force_end": "Force End", "forgot_password": "¿Has olvidado tu contraseña?", "fromEmail": "Desde el correo electrónico", "general_settings": "Configuración general", "general_settings_title": "Configuración general", "get_estimate": "Obtener una estimación", "get_started": "Empezar", "go_back": "Volver", "go_online_to_accepting_jobs": "Empiece a aceptar trabajos.", "go_to_booking": "Ir a la reserva", "go_to_home": "Ir a casa", "google_places_error": "ID de lugar al error de ubicación", "goto_home": "GOTO a casa", "gross_earning": "Ganancias Totales", "hidden_demo": "Oculto para la demostración", "home": "<PERSON><PERSON>o", "host": "<PERSON><PERSON><PERSON><PERSON>", "hours": "<PERSON><PERSON>", "how_your_trip": "¿Cómo es tu viaje?", "id": "IDENTIFICACIÓN", "ignore_job_title": "¿Quieres ignorar este trabajo?", "ignore_text": "IGNORAR", "image": "Imagen", "image_size_warning": "(Tamaño de la imagen: Max 2 MB)", "image_upload_error": "Error de carga de imágenes", "in_demo_mobile_login": "El inicio de sesión móvil está desactivado en modo de demostración", "incomeText": "Mis ganancias", "incomplete_user": "El perfil de usuario está incompleto.", "info": "Información", "ios": "iOS", "km": "km", "landing_slogan": "Mejores servicios <PERSON>", "lang": "Idioma", "lang1": "Idioma:", "langLocale": "Roce de idiomas", "langName": "Nombre del idioma", "language": "Idiomas", "language_cap": "Idiomas", "last_name": "Apellido", "last_name_placeholder": "Apellido", "last_page_tooltip": "Última página", "lastname": "Apellido", "license_image": "Imagen de licencia", "license_image_back": "La imagen de la licencia retrocede", "license_image_front": "Frente de la imagen de la licencia", "license_image_required": "Imagen de licencia requerida", "loading": "Cargando...", "locationServiveBody": "La ubicación de fondo se está ejecutando ...", "locationServiveTitle": "Actualización de ubicación", "location_fetch_error": "Error de búsqueda de ubicación", "location_permission_error": "Error de permiso de ubicación", "login": "Acceso", "login_error": "Error de autenticación", "login_otp": "Inicie sesión con OTP", "login_settings": "Configuración de inicio de sesión", "login_signup": "Iniciar <PERSON> / Registrarse", "logout": "Cierre de sesión", "make_active": "Activar", "make_changes_to_update": "Hacer cambios en la actualización", "make_default": "<PERSON><PERSON><PERSON> predeterminado", "map_screen_drop_input_text": "<PERSON><PERSON>", "map_screen_where_input_text": "Origen", "marker_title_1": "Levantar", "marker_title_2": "<PERSON><PERSON>", "marker_title_3": "Parada", "max_limit": "Descuento máximo permitido", "max_promo_discount_value_error": "Valor de descuento de promoción de relleno máximo", "medialibrary": "Biblioteca de medios", "message": "Escribe tu mensaje", "message_text": "Men<PERSON><PERSON>", "method": "<PERSON><PERSON><PERSON><PERSON>", "mile": "mi", "min_fare": "<PERSON><PERSON><PERSON>", "min_fare_required": "<PERSON>ja una tarifa mínima", "min_limit": "Valor de pedido mínimo", "min_order_error": "Llenar el valor de pedido mínimo", "min_order_value": "Valor de pedido mínimo", "mins": "minutos", "minsDelayed": "<PERSON><PERSON><PERSON> re<PERSON>", "mobile": "Número de teléfono móvil", "mobile_apps_on_store": "Aplicaciones móviles disponibles en tiendas de aplicaciones", "mobile_login": "Inicio de sesión móvil", "mobile_need_update": "Ingrese su número de teléfono móvil para colocar una reserva.", "mobile_no_blank_error": "Ingrese un número móvil válido.", "mobile_number": "Ingrese el número de móvil", "mobile_or_email_cant_off": "El inicio de sesión móvil y de correo electrónico no se puede deshabilitar a la vez", "modal_close": "Modal ha sido cerrado.", "months": "Meses", "must_login": "Inicie sesión para reservar", "my_rides_menu": "Mis reservas", "my_wallet_menu": "Mi billetera", "my_wallet_tile": "Mi billetera", "my_wallet_title": "BILLETERA", "myaccount": "Mi cuenta", "name": "Nombre", "navigation_available": "La navegación del mapa solo está disponible cuando se acepta o inicia la reserva", "negative_balance": "Permitir el saldo negativo del repartidor", "new_booking_notification": "Tienes una nueva solicitud de reserva", "next_page_tooltip": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "next_slide": "Próximo", "no": "No", "no_active_booking": "Sin reserva activa", "no_active_car": "No hay un coche activo disponible", "no_bookings": "No hay reservas disponibles.", "no_cancel_reason": "No hay razones de cancelación disponibles.", "no_cancelled_booking": "Sin reserva cancelada", "no_car_assign_text": "No hay asignación de vehículos", "no_cars": "No hay autos disponibles.", "no_data_available": "No hay datos disponibles", "no_details_error": "Complete todos los detalles correctamente.", "no_driver_found_alert_OK_button": "DE ACUERDO", "no_driver_found_alert_messege": "Lo siento, no se encuentran los repartidores en este momento. Por favor, inténtelo de nuevo más tarde", "no_driver_found_alert_title": "¡Alerta!", "no_name": "Sin nombre", "no_others_car": "No otro coche disponible", "no_payment_getways": "Sin pasarelas de pago", "no_provider_found": "No se encontraron proveedores de pagos", "no_saved_address": "Sin dirección guardada", "no_tasks": "No se encontraron tareas para el repartidor", "no_user_match": "No hay usuarios emparejados", "not_approved": "No aprobado", "not_available": "Indisponible", "not_call": "No puedes llamar ahora mismo.", "not_chat": "No puedes chatear ahora mismo.", "not_found_text": "Extraviado", "not_logged_in": "No iniciado sesión", "not_on_same_time": "El correo electrónico o las imágenes móviles y de licencia no pueden cambiar al mismo tiempo.", "not_registred": "Este correo electrónico no está registrado", "not_valid_user_type": "Usertype no es válido.", "notification_title": "Tienes una notificación", "of": "de", "off": "APAGADO", "offline": "Estás fuera de línea", "ok": "DE ACUERDO", "on": "EN", "on_duty": "De servicio", "online": "Estás en línea", "options": "Opción", "order_id": "Ordenado", "order_no": "Pedido no:", "other": "<PERSON><PERSON>", "otherPerson": "Nombre de otra persona", "otherPersonDetailMissing": "Por favor ingrese el nombre de otra persona", "otherPersonPhone": "Otra persona Teléfono", "otherPerson_title": "Detalles de otra persona", "other_cars": "Otros autos", "other_info": "Otra información sobre vehículos o repartidores", "otp": "OTP:", "otp_blank_error": "OTP no puede estar en blanco", "otp_here": "Por favor ingrese OTP aquí", "otp_sms": "es el OTP. Gracias.", "otp_validate_error": "OTP no es válido", "page_not_not_found": "Página no encontrada", "page_not_not_found_text": "¡Lo siento! La página que está buscando ya no está disponible o ha sido eliminada.", "panic_num": "Número de dial de pánico", "panic_question": "¿Realmente quieres hacer una llamada de pánico?", "panic_text": "Llamada de pánico", "parcel_option": "Opción de paquete", "parcel_type": "<PERSON><PERSON><PERSON> <PERSON>", "parcel_type_web": "<PERSON><PERSON><PERSON> <PERSON>", "parcel_types": "Tipos de paque<PERSON>", "password": "Contraseña", "password_alphaNumeric_check": "Contraseña requerida alfanumérica", "password_blank_messege": "contraseña no en blanco", "password_complexity_check": "Complejidad de contraseña", "password_must_be_8-16_characters_long": "La contraseña debe tener 8-16 caracteres.", "password_must_contain_at_least_one_digit": "La contraseña debe contener al menos un dígito.", "password_must_have_at_least_one_lowercase_character": "La contraseña debe tener al menos un carácter en minúsculas.", "password_must_have_at_least_one_uppercase_character": "La contraseña debe tener al menos un carácter mayúscula.", "password_must_not_contain_whitespaces": "La contraseña no debe contener espacios en blanco.", "past_booking_error": "Libro más tarde no está disponible para la fecha de fecha pasada o en los próximos 15 minutos.", "payWithCard": "Pagar con tarjeta", "pay_cash": "Pagar en efectivo", "pay_mode": "Modo de pago", "payable_ammount": "<PERSON><PERSON> por pagar", "payaed_ammount": "<PERSON><PERSON> paga<PERSON>", "payment": "Pago", "payment_cancelled": "Pago cancelado", "payment_fail": "Su pago falló", "payment_gateway": "<PERSON><PERSON><PERSON>", "payment_info": "Información de pago", "payment_mode": "Modo de pago", "payment_mode_web": "Modo de pago", "payment_of": "Su pago de", "payment_settings": "Configuración de pago", "payment_status": "Estado de pago", "payment_thanks": "<PERSON><PERSON><PERSON> por su pago.", "paynow_button": "<PERSON><PERSON> ahora", "pending": "Pendiente", "percentage": "Po<PERSON>entaj<PERSON>", "personal_info": "Información personal", "phone": "Teléfono", "phone_error_msg": "*Escriba el número de teléfono con el código de su país. Ej.: 449876543210", "phone_no_update": "Actualice su número de teléfono en la sección Editar perfil antes de hacer una reserva", "pickUpInstructions": "Instrucción de recogida", "pickUpInstructions_web": "Instrucción de recogida", "pickup_address": "Dirección de origen", "pickup_address_from_map": "Seleccione la dirección de origen del mapa", "pickup_location": "Ubicación de origen", "place_to_coords_error": "Lugar para coordinar el error. <PERSON>r favor intente de nuevo.", "port": "Puerto", "position": "Posición de lista", "position_required": "Elija la posición de la lista", "prepaid": "<PERSON><PERSON>", "previous_page_tooltip": "Página anterior", "privacy_policy": "política de privacidad", "privacy_policy_change_privacy_para1": "Podemos actualizar nuestra Política de privacidad de vez en cuando. Por lo tanto, se le aconseja que revise esta página periódicamente para cualquier cambio. Le notificaremos cualquier cambio publicando la nueva Política de privacidad en esta página.", "privacy_policy_change_privacy_para2": "Esta política es efectiva a partir de 2023-12-12", "privacy_policy_children_para1": "Estos servicios no se dirigen a nadie menor de 13 años. No recopilamos a sabiendas información de identificación personal de niños menores de 13 años. En el caso, descubrimos que un niño menor de 13 años nos ha proporcionado información personal, inmediatamente lo eliminamos de nuestros servidores. Si usted es padre o tutor y sabe que su hijo nos ha proporcionado información personal, contáctenos para que podamos realizar las acciones necesarias.", "privacy_policy_contact_para1": "Si tiene alguna pregunta o sugerencia sobre nuestra Política de privacidad, no dude en contactarnos en", "privacy_policy_cookie_para1": "Las cookies son archivos con una pequeña cantidad de datos que se usan comúnmente como identificadores únicos anónimos. Estos se envían a su navegador desde los sitios web que visita y se almacenan en la memoria interna de su dispositivo.", "privacy_policy_cookie_para2": "Este servicio no utiliza estas \"cookies\" explícitamente. Sin embargo, la aplicación puede usar código de terceros y bibliotecas que usan \"cookies\" para recopilar información y mejorar sus servicios. Tiene la opción de aceptar o rechazar estas cookies y saber cuándo se envía una cookie a su dispositivo. Si elige rechazar nuestras cookies, es posible que no pueda usar algunas partes de este servicio.", "privacy_policy_heading_change_privacy": "Cambios a esta Política de privacidad", "privacy_policy_heading_children": "Privacidad de los niños", "privacy_policy_heading_contact": "Cont<PERSON><PERSON><PERSON>s", "privacy_policy_heading_cookie": "GALLETAS", "privacy_policy_heading_info": "Recopilación y uso de información", "privacy_policy_heading_link": "Enlaces a otros sitios", "privacy_policy_heading_log": "Datos de registro", "privacy_policy_heading_security": "SEGURIDAD", "privacy_policy_heading_service": "Proveedores de servicios", "privacy_policy_info_list1": "Nombre", "privacy_policy_info_list2": "Apellido", "privacy_policy_info_list3": "Dirección de correo electrónico", "privacy_policy_info_list4": "Número de teléfono", "privacy_policy_info_list5": "Buscar ubicación del vehículo", "privacy_policy_info_list6": "Ruta de viaje y navegación", "privacy_policy_info_list7": "Movimiento del vehículo en tiempo real", "privacy_policy_info_para1": "Para una mejor experiencia, mientras usa nuestro servicio, requeriremos que nos proporcione cierta información de identificación personal, incluida, entre otros,", "privacy_policy_info_para2": "La información que solicitemos será retenida por nosotros y utilizada como se describe en esta Política de privacidad.", "privacy_policy_info_para3": "Recopilamos la siguiente información confidencial cuando usa o registra en nuestra aplicación.", "privacy_policy_info_para4": "Recopilamos sus datos de ubicación para habilitar", "privacy_policy_info_para5": "La aplicación utiliza servicios de terceros para el inicio de sesión social, como Google y Apple, el inicio de sesión que recopila información utilizada para identificarlo. Capturamos el correo electrónico del usuario y el nombre del inicio de sesión social si el usuario ha elegido para que se revele mientras realiza un inicio de sesión social.", "privacy_policy_link_para1": "Este servicio puede contener enlaces a otros sitios. Si hace clic en un enlace de terceros, se le dirigirá a ese sitio. Tenga en cuenta que estos sitios externos no son operados por nosotros. Por lo tanto, le recomendamos encarecidamente que revise la política de privacidad de estos sitios web. No tenemos control y no asumimos ninguna responsabilidad por el contenido, las políticas de privacidad o las prácticas de ningún sitio o servicios de terceros.", "privacy_policy_log_para1": "Queremos informarle que cada vez que usa nuestro servicio, en un caso de un error en la aplicación, recopilamos datos e información (a través de productos de terceros) en su teléfono llamado Log Data. Estos datos de registro pueden incluir información como la dirección de su protocolo de Internet (\"IP\"), el nombre del dispositivo, la versión del sistema operativo, la configuración de la aplicación al utilizar nuestro servicio, la hora y la fecha de su uso del servicio y otras estadísticas.", "privacy_policy_para1": "Esta página se utiliza para informar a los visitantes sobre nuestras políticas con la recopilación, uso y divulgación de información personal si alguien decidió usar cualquiera de nuestro servicio.", "privacy_policy_para2": "Si elige usar nuestro servicio, entonces acepta la recopilación y el uso de información en relación con esta política. La información personal que recopilamos se utiliza para proporcionar y mejorar el servicio. No usaremos ni compartiremos su información con nadie, excepto como se describe en esta Política de privacidad.", "privacy_policy_para3": "Los términos utilizados en esta Política de privacidad tienen los mismos significados que en nuestros Términos y condiciones, que es accesible en todas nuestras aplicaciones a menos que se define lo contrario en esta Política de privacidad.", "privacy_policy_security_para1": "%20 Valor%20 our%20Trust%20in%20Provising%20US%20 our%20personal%20información,%20thus%20we%20e%20Strining%20Se 20use%20Mercialmente%20aceptable%20Means%20Of%20 PROTECTIVE%20IT. A%20NO%20metod%20Of%20transmission%20ver%20The%20internet,%20or%20metod%20Of%20Electrónico%20Storage%20IS%20100 %% 20 SECUESTRA%20 y%20RIVIABLEDE,%20 y%20 Ve 20 Cannot%20 garantía%20ITS%20Bsolute%20sCurity.", "privacy_policy_service_list1": "Para facilitar nuestro servicio;", "privacy_policy_service_list2": "Para proporcionar el servicio en nuestro nombre;", "privacy_policy_service_list3": "Para realizar servicios relacionados con el servicio; o", "privacy_policy_service_list4": "Para ayudarnos a analizar cómo se utiliza nuestro servicio.", "privacy_policy_service_para1": "Podemos emplear compañías e individuos de terceros debido a las siguientes razones:", "privacy_policy_service_para2": "Queremos informar a los usuarios de este servicio que estos terceros tienen acceso a su información personal. La razón es realizar las tareas que se les asignan en nuestro nombre. Sin embargo, están obligados a no divulgar o usar la información para ningún otro propósito.", "processDate": "Fecha de proceso", "process_withdraw": "Retirar el proceso", "processed": "Procesado", "product_section_1": "Tu experiencia es importante para nosotros. Nunca nos comprometemos en nuestros estándares. Se enfatizan particularmente las regulaciones de seguridad para garantizar la tranquilidad.", "product_section_2": "¡Vea el seguimiento en vivo, las estimaciones de precios e incluso reservar con anticipación! Tenemos todas las vías cubiertas con todas las últimas tecnologías.", "product_section_3": "El repartidor puede agregar muchos tipos de vehículos.", "product_section_4": "Además del efectivo, la tarjeta de crédito, los pagos cumplen con PCI DSS. El sitio web tiene una certificación SSL, y las aplicaciones son revisadas por Apple Store y Google Play.", "product_section_heading": "Información de servicio", "product_section_para": "Brindamos los mejores servicios basados en aplicaciones del país.", "profile": "Perfil", "profile_image": "Imagen de perfil", "profile_incomplete": "Perfil incompleto. Vaya a la configuración del perfil.", "profile_page_subtitle": "Detalles del perfil", "profile_page_title": "Mi perfil", "profile_setting_menu": "Configuración de perfil", "profile_title": "PERFIL", "profile_updated": "Perfil actualizado.", "promo": "Promoción", "promo_apply": "(Aplicar la promoción)", "promo_code": "Código de promoción", "promo_code_error": "Llenar el código de promoción", "promo_code_web": "Código de promoción", "promo_description_error": "Descripción de la promoción de relleno", "promo_discount": "Descuento de promoción", "promo_discount_type_error": "Tipo de descuento de promoción de relleno", "promo_discount_value": "Valor promocional", "promo_discount_value_error": "Valor de descuento de promoción de relleno", "promo_eligiblity": "¡Lo siento! La promoción no es válida ya que el monto de la factura es menor.", "promo_exp": "¡Lo siento! La promoción ha expirado.", "promo_exp_limit": "¡Lo siento! Límite de uso de promoción.", "promo_name": "Nombre promocional", "promo_name_error": "Llenar el nombre de la promoción", "promo_not_found": "Este código de promoción no se encuentra", "promo_offer": "Promoción y ofertas", "promo_offer_title": "Promoción y ofertas", "promo_usage": "Recuento de promociones disponibles", "promo_used": "¡Lo siento! Ya ha usado el código de promoción.", "promo_used_by": "Promoción utilizada por el conteo", "proper_bonus": "Ingrese la bonificación adecuada en los números.", "proper_email": "Ingrese el correo electrónico correctamente.", "proper_input_image": "Subir la imagen adecuada", "proper_input_licenseimage": "Subir la imagen de licencia adecuada", "proper_input_name": "Ingrese el nombre propio", "proper_input_vehicleno": "Ingrese el número adecuado del vehículo", "proper_mobile": "Ingrese el móvil correctamente.", "provider_fetch_error": "No se puede buscar proveedores de pagos", "provider_not_found": "No se encontraron proveedores de pagos.", "pruduct_section_heading_1": "<PERSON><PERSON><PERSON><PERSON>", "pruduct_section_heading_2": "Conveniente", "pruduct_section_heading_3": "Gestión de vehículos", "pruduct_section_heading_4": "<PERSON><PERSON><PERSON>", "push_error_1": "¡No se pudo obtener token de empuje para la notificación push!", "push_error_2": "Debe usar el dispositivo físico para las notificaciones push", "push_notification_title": "Notificaciones de empuje", "push_notifications": "Notificaciones de empuje", "push_notifications_title": "Notificaciones de empuje", "queue": "Ocupado", "rate_for": "Calificar para", "rate_per_hour": "<PERSON><PERSON><PERSON> por hora", "rate_per_hour_required": "<PERSON>ja la tarifa por hora", "rate_per_unit_distance": "Velocidad de distancia por (km o milla)", "rate_per_unit_distance_required": "Elija la tarifa de distancia por (km o milla)", "rate_ride": "Califica a tu crepartidor", "real_time_driver_section_text": "Repartidores en tiempo real", "realtime_drivers": "Ubicación en vivo del repartidor", "reason": "Razón", "receipt": "Recibo", "received_rating": "Recibiste una calificación de estrella X", "refer_earn": "Referirse", "referer_not_found": "ID de referencia no encontrado", "referralId": "ID de referencia", "referral_bonus": "<PERSON><PERSON>", "referral_email_used": "Este correo electrónico ya utilizó una identificación de referencia", "referral_id_placeholder": "ID de referencia (opcional)", "referral_number_used": "Este número ya usó una identificación de referencia", "reg_error": "Error mientras se registra", "register": "Registro", "register_as_driver": "Registrar usuario?", "register_button": "Registrar<PERSON> ahora", "registration_title": "Registro", "remove_promo": "Eliminar la promoción", "report": "Informes", "requestDate": "<PERSON><PERSON>", "request_otp": "Iniciar sesión con OTP", "request_payment": "Solicitar pago", "require_approval": "Su cuenta requiere la aprobación del administrador", "ride_details_page_title": "Detalles de la reserva", "ride_information": "Información de viaje", "ride_list_title": "Mis reservas", "rider_cancel_text": "Reserva Cancelar", "rider_not_here": "No hay solicitudes en este momento", "rider_withdraw": "Retirar el cliente", "riders": "Clientes", "riders_title": "CLIENTES", "rides": "Paseos", "roundTrip": "IDA Y VUELTA", "rows": "Hilera", "save": "GUARDAR", "saved_address": "Dirección guardada", "search": "Buscar", "search_for_an_address": "Buscar una dirección", "searching": "Búsqueda", "secure": "<PERSON><PERSON><PERSON>", "security_title": "Configuración de seguridad", "selectBid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select_car": "Seleccione el tipo de vehículo", "select_country": "<PERSON><PERSON>", "select_date": "Seleccione Fecha", "select_driver": "Seleccionar repartidor", "select_payment_getway": "Seleccione la pasarela de pago", "select_proper": "Seleccione correctamente.", "select_reason": "Seleccione la razón de cancelación", "select_user": "Se<PERSON><PERSON><PERSON>r usuario", "send_button_text": "Enviar", "senderPersonPhone": "Reserva de la persona por teléfono no", "service_off": "El estado de su servicio está 'desactivado'", "service_start_soon": "Nuestro servicio comenzará pronto ...", "set_decimal": "Establecer decimal", "set_link_email": "Restablecer el enlace de contraseña enviar al correo anterior", "settings_label3": "Reserva OTP", "settings_label4": "Aprobación del repartidor", "settings_label5": "Aprobación de coche", "settings_label6": "Verificar ID / Passport", "settings_title": "<PERSON><PERSON><PERSON><PERSON>", "share_msg": "<PERSON><PERSON>, use este código mientras se registra y obtiene una bonificación de", "share_msg_no_bonus": "<PERSON><PERSON>, mira esta aplicación. Me encanta.", "show_in_list": "Mostrar en la lista", "show_live_route": "Mostrar ruta en vivo", "signIn": "In<PERSON><PERSON>", "signup": "Inscribirse", "signup_via_referral": "Registrarse a través de la referencia", "skip": "Saltar", "smssettings": "Configuración de SMS", "smssettings_title": "Configuración de SMS", "smtp_error": "Consulte sus detalles de SMTP", "smtpsettings": "Configuración SMTP", "smtpsettings_title": "Configuración SMTP", "social_login": "Inicio de sesión social", "solved": "<PERSON><PERSON><PERSON><PERSON>", "sos": "Llamada de socorro", "sos_title": "LLAMADA DE SOCORRO", "spacer_message": "O conectarse con", "start_accept_bid": "Has recibido una oferta", "start_trip": "<PERSON><PERSON><PERSON><PERSON>", "stops": "Parada", "subject": "Sujeto", "submit": "GUARDAR", "submit_rating": "Enviar calificación", "success": "Éxito", "success_payment": "Pago realizado con éxito.", "swipe_symbol": "Dirección de símbolo de deslizamiento", "system_price": "Precio del sistema", "system_price_with_bid": "Precio del sistema con oferta", "take_deliver_image": "Imagen de entrega", "take_deliver_image_web": "Imagen de entrega", "take_pickup_image": "Imagen de recogida", "take_pickup_image_web": "Imagen de recogida", "task_list": "<PERSON><PERSON>o", "term_condition": "Térm<PERSON>s", "term_condition_heading1": "Aceptación de los términos:", "term_condition_heading10": "Terminación:", "term_condition_heading11": "Ley de gobierno:", "term_condition_heading12": "Enmiendas:", "term_condition_heading2": "Elegibilidad:", "term_condition_heading3": "Descripción del servicio:", "term_condition_heading4": "Pago :", "term_condition_heading5": "Conducta del usuario:", "term_condition_heading6": "Contenido de usuario:", "term_condition_heading7": "Propiedad intelectual:", "term_condition_heading8": "Descargos de responsabilidad:", "term_condition_heading9": "Limitación de responsabilidad:", "term_condition_para1": "G<PERSON><PERSON> por elegir nuestros servicios de reserva basados en aplicaciones para el taxi, el taxi y las necesidades de entrega. Lea cuidadosamente los siguientes términos y condiciones antes de usar nuestros servicios:", "term_condition_para10": "En ningún caso seremos responsables de daños indirectos, incidentales, especiales o consecuentes que surjan o en relación con el uso de nuestros servicios, ya sea que se nos haya informado o no sobre la posibilidad de tales daños.", "term_condition_para11": "Nos reservamos el derecho de cancelar un acceso a los usuarios a nuestra plataforma en cualquier momento, sin previo aviso, por cualquier motivo.", "term_condition_para12": "Estos Términos y condiciones se regirán e interpretan de acuerdo con las leyes de la jurisdicción donde se basa la Compañía.", "term_condition_para13": "Nos reservamos el derecho de actualizar estos términos y condiciones en cualquier momento, sin previo aviso. Se recomienda a los usuarios que revisen estos términos y condiciones periódicamente para mantenerse informados de cualquier cambio.", "term_condition_para14": "Si tiene alguna pregunta o inquietud sobre estos términos y condiciones, contáctenos en", "term_condition_para2": "Al utilizar nuestros servicios, usted acepta estar sujeto a estos términos y condiciones. Si no está de acuerdo con estos Términos y condiciones, no puede usar nuestros Servicios.", "term_condition_para3": "Debe tener al menos 18 años para usar nuestros servicios. Al usar nuestros servicios, usted representa y garantiza que tiene al menos 18 años.", "term_condition_para4": "Nuestros servicios de reserva basados en aplicaciones proporcionan una plataforma para conectar a los usuarios con proveedores de taxis, taxis y entrega. No poseemos, operamos ni controlamos ninguno de los vehículos o repartidores que usan nuestra plataforma. Nuestros servicios están destinados a ser utilizados solo para uso personal y no comercial.", "term_condition_para5": "Los usuarios deben pagar los servicios proporcionados a través de nuestra plataforma. Aceptamos varias formas de pago, incluidas tarjetas de crédito, tarjetas de débito y métodos de pago electrónico. Todos los pagos realizados a través de nuestra plataforma están sujetos a nuestras políticas de pago, que pueden actualizarse de vez en cuando.", "term_condition_para6": "Se espera que los usuarios usen nuestros servicios de manera responsable y no participen en ningún comportamiento que pueda dañar nuestra plataforma, repartidores u otros usuarios. Los usuarios no pueden usar nuestra plataforma para ningún propósito ilegal o no autorizado. También se les prohíbe a los usuarios interferir con el funcionamiento de nuestra plataforma o participar en cualquier actividad que pueda comprometer la seguridad o la integridad de nuestra plataforma.", "term_condition_para7": "Los usuarios son únicamente responsables del contenido que envían a través de nuestra plataforma. Al enviar contenido, los usuarios nos otorgan una licencia no exclusiva, libre de regalías, transferible y sublicensible para usar, modificar y reproducir el contenido con el fin de proporcionar nuestros servicios.", "term_condition_para8": "Nuestra plataforma, incluida todo el contenido y la propiedad intelectual, es propiedad de nosotros y/o nuestros licenciantes. Los usuarios no pueden copiar, modificar, distribuir o reproducir ninguno de los contenidos o propiedad intelectual sin nuestro consentimiento previo por escrito.", "term_condition_para9": "No garantizamos la disponibilidad, confiabilidad o calidad de los servicios proporcionados por los repartidores que utilizan nuestra plataforma. No somos responsables de ninguna pérdida o daño que pueda resultar del uso de nuestros servicios. Nuestra plataforma se proporciona 'tal cual' y sin ninguna garantía, expresa o implícita.", "term_required": "<PERSON><PERSON><PERSON><PERSON>", "terms": "política de privacidad", "thanks": "<PERSON><PERSON><PERSON> por la calificación", "this_month_text": "<PERSON><PERSON>", "thismonth": "<PERSON>ste mes", "thisyear": "<PERSON>ste año", "time": "TIEMPO", "title": "<PERSON><PERSON><PERSON><PERSON>", "today_text": "Hoy", "total": "Total", "total_cumtomer": "No. de cliente", "total_drivers": "No. de Repartidores", "total_fare": "Tarifa total", "total_time": "Tiempo total", "totalearning": "Ganancias totales", "transaction_history_title": "Historial de transacciones de billetera", "transaction_id": "ID de transacción:", "tripInstructions": "Instrucciones de viaje", "trip_cost": "Costo de viaje", "trip_cost_driver_share": "Compartir el repartidor", "trip_date_time": "Hora de la fecha de viaje", "trip_end_time": "Hora de finalización de viaje", "trip_instruction": "Instrucción de viaje", "trip_start_date": "Fecha de inicio del viaje", "trip_start_time": "Hora de inicio del viaje", "try_again": "Por favor intente de nuevo.", "type": "Tipo", "update_admin": "Actualizar admin", "update_admin_title": "Actualizar admin", "update_any": "No puede actualizar el correo electrónico y el móvil al mismo tiempo.", "update_button": "Actualización ahora", "update_carType_title": "Actualizar el tipo de coche", "update_car_title": "Actualizar coche", "update_customer_title": "Actualizar el cliente", "update_driver_title": "Actualizar repartidor", "update_failed": "La actualización falló", "update_fleetAdmin": "Actualizar admin de flota", "update_fleetAdmin_title": "Actualizar admin de flota", "update_profile_title": "Documento de actualización", "update_promo_title": "Actualizar promoción", "updated": "Actualizado", "upload_car_image": "<PERSON>gar imagen de coche", "upload_driving_license": "Sube tu licencia de conducir", "upload_driving_license_back": "Cargue su licencia de conducir hacia atrás", "upload_driving_license_front": "Sube el frente de tu licencia de conducir", "upload_id_details": "Sube tu identificación / pasaporte", "upload_profile_image": "Imagen de perfil de carga", "upload_verifyIdImage": "Cargue su ID / número de pasaporte", "use_distance_matrix": "Use la API de matriz de distancia", "use_wallet_balance": "Use el efectivo de la billetera (su saldo es", "user": "Usuarios", "user_exists": "El usuario con el mismo correo electrónico o número de teléfono móvil existe.", "user_issue_contact_admin": "No se encontraron datos de perfil. Póngase en contacto con el administrador", "user_type": "Tipo de usuario", "username": "Nombre de usuario", "users_title": "Usuarios", "usertype": "Tipo de usuario", "valid_amount": "Ingrese una cantidad válida", "value_for_money": "Valor por dinero", "vehicleMake_required": "Se requiere una marca del vehículo/marca", "vehicleModel_required": "Requerido el modelo de vehículo", "vehicleNumber_required": "REG DE VEHÍCULO No. requerido", "vehicle_make": "<PERSON><PERSON>", "vehicle_model": "<PERSON><PERSON>", "vehicle_model_name": "Marca del vehículo / marca", "vehicle_model_no": "Modelo de vehículo NO", "vehicle_no": "Número de vehículo", "vehicle_number": "Número", "vehicle_reg_no": "Número de registro del vehículo", "verify_id": "ID / Número de pasaporte", "verify_otp": "Verificar OTP", "verifyid_error": "Lo siento, no tienes identificación / pasaporte", "verifyid_image": "ID / imagen de pasaporte", "wallet": "Bill<PERSON>a", "wallet_bal_low": "Balance de billetera bajo.", "wallet_balance": "Equilibrio de billetera", "wallet_balance_low": "Su equilibrio de billetera es demasiado bajo.", "wallet_balance_threshold_reached": "El umbral de equilibrio de su billetera alcanzó", "wallet_balance_zero": "El saldo de su billetera es 0. Si toma el pago en efectivo, la tarifa de conveniencia del administrador se deducirá de la billetera y su saldo de billetera será negativo después del trabajo. El equilibrio negativo restringirá más trabajos. Debe recargar la billetera o solicitar ayuda a Admin.", "wallet_balance_zero_customer": "Su equilibrio de billetera es negativo. No se le permite reservar con equilibrio negativo", "wallet_ballance": "Equilibrio de billetera", "wallet_booking_alert": "No puedes reservar otro usando billetera. Tienes una reserva de billetera existente.", "wallet_money_field": "Denominaciones de billetera", "wallet_payment_amount": "Monto del pago de la billetera", "wallet_title": "Finanzas", "wallet_updated": "Billetera actualizada con éxito.", "wallet_zero": "Balance de billetera 0.", "was_successful": "fue exitoso", "where_are_you": "Usted está aquí", "withdraw": "RETIRAR", "withdraw_below_zero": "La cantidad de retiro no puede ser menor o igual a 0.", "withdraw_money": "<PERSON><PERSON><PERSON>o", "withdraw_more": "El monto de retiro no puede ser mayor que el saldo de la billetera.", "withdrawn": "RETIRADO", "withdraws": "<PERSON><PERSON><PERSON>", "withdraws_web": "<PERSON><PERSON><PERSON>", "within_min": "<PERSON><PERSON><PERSON>", "work": "Trabajar", "year": "<PERSON><PERSON><PERSON>", "yes": "Sí", "you_are_offline": "Tu deber está apagado", "you_rated_text": "Calificación del repartidor", "your_fare": "Tu tarifa", "your_feedback": "Sus comentarios (opcionales) ...", "your_feedback_test": "Sus comentarios nos ayudarán a mejorar mejor la experiencia de manejo.", "your_promo": "Tu promoción", "your_trip": "<PERSON>"}, "langLocale": "es", "langName": "Español", "tableData": {"id": 0}}, "lang1": {"dateLocale": "en-gb", "default": false, "id": "lang1", "keyValuePairs": {"ACCEPTED": "ACCEPTED", "ARRIVED": "ARRIVED", "AppName": "App Name", "AppleStoreLink": "Apple Store Link", "Balance": "Balance", "CANCELLED": "CANCELLED", "COMPLETE": "COMPLETE", "CardPaymentAmount": "Paid by Card -", "CashPaymentAmount": "Paid by Cash -", "CompanyName": "Company Name", "CompanyWebsite": "Company Website", "Customer_paid": "Customer paid", "Discounts": "Discounts", "FacebookHandle": "Facebook Page Link", "Gross_trip_cost": "Gross trip cost", "InstagramHandle": "Instagram Page Link", "NEW": "NEW", "PAID": "PAID", "PAYMENT_PENDING": "PAYMENT PENDING", "PENDING": "PENDING", "PlayStoreLink": "Play Store Link", "Profit": "Profit", "REACHED": "REACHED", "STARTED": "STARTED", "TwitterHandle": "Twitter Page Link", "WalletPayment": "Paid by <PERSON><PERSON>", "Withdraw_title": "WITHDRAWS", "about_us": "About Us", "about_us_content1": "We are the largest mobility platform and one of the world's largest online on-demand services provider.", "about_us_content2": "Manage bookings, request quotes, or book a online service with our simple and quick online booking system. We are an on-demand services company that allows guests to easily book various services online. We offer the best services in the country. ", "about_us_menu": "About Us", "accept": "ACCEPT", "accept_booking_request": " accepted your booking request.", "accepted_booking": "Your booking is accepted", "account_approve": "Account Approved", "account_create_successfully": "Account created successfully", "actions": "Actions", "active_booking": "ACTIVE BOOKING", "active_car": "Active Car", "active_car_delete": "Active Car cannot be deleted.", "active_driver": "Active Drivers", "active_status": "Active Status", "add": "ADD", "addMoneyTextInputPlaceholder": "Add Amount", "add_admin": "Add Admin", "add_admin_title": "ADD ADMIN", "add_booking_title": "ADD BOOKINGS", "add_car": "Add Car", "add_carType": "Add Vehicle Type", "add_carType_title": "ADD CAR TYPE", "add_car_title": "ADD CAR", "add_cartype_title": "ADD VEHICLE", "add_customer": "Add Customer", "add_customer_title": "ADD CUSTOMER", "add_driver": "Add Driver", "add_driver_title": "ADD DRIVER", "add_fleetadmin": "Add Fleet Admin", "add_fleetadmin_title": "ADD FLEET ADMIN", "add_language": "Edit Language", "add_money": "Add Money", "add_notification": "Add Notifications", "add_notification_title": "ADD NOTIFICATION", "add_promo_title": "ADD PROMO", "add_to_review": "Add to review", "add_to_wallet": "Add to Wallet", "add_to_wallet_title": "ADD TO WALLET", "addbookinglable": "Add Bookings", "admin": "Admin", "admin_contact": "Contact your admin to approve account", "advance_settings": "Advance Settings", "alert": "<PERSON><PERSON>", "alert_text": "<PERSON><PERSON>", "all": "All", "alladmin": "All Admins", "alladmins_title": "ALL ADMINS", "allow_del_final_img": "Allow delivery drop image", "allow_del_pkp_img": "Allow delivery pick image", "allow_location": "Allow Location for the Realtime Map", "allow_multi_country": "Allow Multi Country Selection", "allow_only": "Location needs while using app", "always_on": "Location needs Always On", "amount": "Amount", "amount_must_be_gereater_than_100": "For Slickpay amount must be greater than 100", "android": "Android", "apiUrl": "Api Url", "app_info": "App Information", "app_info_title": "APP INFORMATION", "app_link": "App Link : ", "app_store_deception": "Your business needs to be in safe hands at all times. We ensure you never run out of customers and not run at loss. We are trusted by over 500+ companies to deliver quality marketing campaigns using Digital marketing & Offline marketing channels.", "app_store_deception1": "The App and for Customer and Driver, not for the Admin Login. Take 2 phones and install in each. Register a Customer in One and register a Driver in One. For the Driver always select Location Always Allow when login in. Then creating a booking from One phone and receiving on the other where driver logged in. Use the provided User login on the Web link to see the Admin side of things.", "apple_signin_error": "Apple SignIn is not set up in developer.appple.com or you have used the same email to SignUp already.", "apply": "APPLY", "apply_promo": "Apply Promo", "approve_status": "Approve Status", "approved": "Approved", "assign": "ASSIGN", "assign_driver": "Assign Driver", "auth_error": "Auth Error: Please check your entered credentials", "authorization": "Authorization", "auto_dispatch": "Auto Dispatch", "back": "BACK TO LOGIN", "bankAccount": "Bank Account", "bankCode": "Bank Code", "bankDetails": "BANK DETAILS", "bankName": "Bank Name", "bank_fields": "Bank Reg Fields", "base_fare": "Base Fare", "base_fare_required": "Please Choose Base Fare", "best_experience": "Best Experience", "best_service_provider": "BEST SERVICE PROVIDED", "best_services": "Best Services", "bid": "Bid", "bill_details": "<PERSON>", "bill_details_title": "<PERSON>", "blank_message": "No Records To Display", "body": "Body", "book": "Book", "book_later": "BOOK LATER", "book_later_button": "BOOK LATER", "book_now": "BOOK NOW", "book_now_button": "BOOK NOW", "book_ride": "Book Your Ride", "book_your_ride_menu": "Make a Booking", "book_your_title": "Book your Service", "booked_cab_title": "Manage Booking", "bookingPayment": "Booking Payment", "booking_cancelled": "Booking is cancelled. ID : ", "booking_chart": "Booking Chart", "booking_confirm": "Your booking has been confirmed", "booking_count": "Booking Count", "booking_date": "Booking Date", "booking_date_time": "Booking Data Time", "booking_details": "Booking Details", "booking_flow": "Booking Flow", "booking_history": "Booking History", "booking_id": "Booking ID", "booking_is": "Your booking is ", "booking_ref": "Booking Reference", "booking_request": "Booking Requests", "booking_status": "BOOKING STATUS", "booking_status_web": "Booking Status", "booking_success": "Booking successful. Booking Id : ", "booking_successful": "Booking Successful", "booking_title": "MY BOOKINGS", "booking_type": "Booking Type", "bookings_table": "Bookings Table", "bookings_table_title": "BOOKINGS TABLE", "camera": "Camera", "camera_permission_error": "Camera Permission Error", "cancel": "CANCEL", "cancelSlab": "Cancellation Slabs", "cancel_booking": "Cancel Booking", "cancel_confirm": "Do you want really cancel?", "cancel_messege1": "Your Job with Booking Id", "cancel_messege2": "has been cancelled successfully", "cancel_reason_modal_title": "Reason", "cancel_ride": "CANCEL BOOKING", "cancellationFee": "Cancellation Fee", "cancellation_reason": "Cancellation Reason", "cancellation_reasons": "Cancellation Reasons", "cancellation_reasons_title": "CANCELLATION REASON", "cancelled_bookings": "Cancel Booking", "cancelled_incomplete_booking": "CANCELLED INCOMPLETE BOOKING", "carApproved_by_admin": "Active car not approved by admin.", "carType_required": "Car Is Required", "car_add": "Add car for accept booking", "car_approval": "Car Approval", "car_details_title": "Vehicle Details", "car_horn_repeat": "Repeat sound on new trip", "car_image": "Car Image", "car_no_not_found": "Vehicle number not assigned", "car_type": "Vehicle Type", "car_type_blank_error": "Select vehicle type", "car_type_title": "VEHICLE TYPE", "car_view_horizontal": "Car list view Horizontal", "card": "Card", "card_payment_amount": "Card payment amount", "cars": "Cars", "cars_title": "CARS", "cash": "Cash", "cash_booking_false": "Admin can't create a booking when cash payment is disable in system.", "cash_on_delivery": "CASH ON DELIVERY", "cash_payment_amount": "Cash payment amount", "chat_blank": "please write something...", "chat_input_title": "Type something nice...", "chat_not_found": "No chat history found", "chat_requested": ": Chat Message", "chat_title": "Cha<PERSON>", "check_approve_status": "Check Approve Status", "check_email": " Check your email inbox", "check_mobile": "Check your massage box", "choose_image_first": "Choose image first.", "close": "CLOSE", "code": "Code", "code_already_avilable": "This Promo Already Available", "code_colon": "Code : ", "company_phone": "Company Phone", "complain": "<PERSON><PERSON><PERSON>", "complain_date": "Complain Date", "complain_title": "COMPLAIN", "complete_payment": "COMPLETE PAYMENT", "complete_ride": "COMPLETE JOB", "completed_bookings": "Complete Booking", "confirm": "CONFIRM", "confirm_booking": "Confirm Booking", "confirm_password": "Confirm Password", "confirm_password_not_match_err": "Confirm password not match", "contact": "Contact", "contact_email": "Contact Email", "contact_input_error": "Please enter a valid email or mobile number.", "contact_placeholder": "Mobile Number Or Email", "contact_us": "Contact Us", "contentType": "Content-Type", "convenience_fee": "Convenience Fees", "convenience_fee_required": "Please Choose Convenience Fees", "convenience_fee_type": "Convenience Fee Type", "convenience_fee_type_required": "Please Choose Convenience Fee Type", "convert_button": "Convert", "convert_to_driver": "Convert to <PERSON>", "convert_to_mile": "Convert to <PERSON>", "convert_to_rider": "Convert To <PERSON>", "cost": "COST", "country_1": "Country", "country_blank_error": "Please select the country", "country_restriction": "Autocomplete Country Restriction", "create_driver": "Create Driver", "create_new_user": "Creating new user, please wait...", "create_rider": "Create Rider", "createdAt": "Create Date", "credited": "CREDITED", "currency_code": "Currency Code", "currency_settings": "<PERSON><PERSON><PERSON><PERSON>", "currency_symbol": "Currency Symbol", "customMobileOTP": "Enable Custom Mobile OTP", "customer": "Customer", "customer_contact": "Customer contact", "customer_email": "Customer <PERSON><PERSON>", "customer_id": "Customer ID", "customer_info": "Customer Info", "customer_name": "Customer Name", "daily": "DAILY", "dashboard_text": "Dashboard", "date": "Date", "dateLocale": "Date Local", "date_time": "Date & Time", "debited": "DEBITED", "delete": "Delete", "delete_account_lebel": "Delete Account", "delete_account_modal_subtitle": "Do you want to delete your account ?", "delete_account_modal_title": "Confirmation", "delete_account_msg": "User can remove all their data from the system by deleting the account from profile page in mobile app.", "delete_account_para1": "All your data will be purged from the system. Your profile image, email, phone number, social logins including Google login and all booking history, everything will be permanently removed.", "delete_account_para2": "Deleted user data and account are irrecoverable.", "delete_account_subheading": "Once you delete your account:", "delete_message": "ARE YOU SURE YOU WANT TO DELETE THIS ROW?", "delete_your_car": "Do you want to delete your car?", "deliveryDetailMissing": "Please enter receiving person's Name & Phone No.", "deliveryInstructions": "BOOKING INSTRUCTIONS", "deliveryPerson": "Receiving Person Name", "deliveryPersonPhone": "Receiving Person Phone No", "delivery_information": "Booking Information", "demo_mode": "Restricted in Demo App.", "description": "Description", "device_id": "Device ID", "device_type": "Device Type", "disable_cash": "Disable Cash Payments", "disable_online": "Disable Online Payments", "disclaimer": "Disclaimer", "disclaimer_text": "This app collects location data to enable 'Search Vehicle Location', 'Travel Route and Navigation', 'Realtime Vehicle Movement', even when the app is closed or not in use.", "discount": "Discount", "discount_ammount": "Discount Amount", "distance": "DISTANCE", "distance_web": "Distance", "documents": "Documents", "documents_title": "DOCUMENTS", "done": "DONE", "dont_cancel_text": "Don't Cancel", "drag_map": "Drag map to select address", "driver": "Driver", "driverRadius": "<PERSON>", "driver_accept_low_cost_capacity_booking": "Driver accept low cost/capacity booking", "driver_active": "Driver Active Status", "driver_active_staus_choose": "Driver Active Staus Cho<PERSON>", "driver_assign_messege": "Driver will Assign Soon..", "driver_cancelled_booking": "DRIVER CANCELLED BOOKING", "driver_completed_ride": "You have reached destination. Please complete the payment.", "driver_contact": "Driver contact", "driver_distance": "Driver will arrive in", "driver_earning": "Driver Earning History", "driver_earning_title": "DRIVER EARNING HISTORY", "driver_email": "Driver Email", "driver_finding_alert": "Finding Drivers for you", "driver_id": "Driver ID", "driver_info": "Driver Info", "driver_journey_msg": "Driver has started. Your booking id is ", "driver_name": "Driver Name", "driver_near": "Driver Near You", "driver_required": "Driver Required", "driver_setting": "Driver Settings", "driver_share": "Driver Share", "driver_threshold": "<PERSON>", "drivers": "Drivers", "drivers_title": "DRIVERS", "driving_license_back": "Driving License Back", "driving_license_font": "Driving License Font", "drop_address": "Drop address", "drop_address_from_map": "Select drop address from map", "drop_location": "Drop Location", "drop_location_blank_error": "Select drop location", "earning_amount": "Earning Amount", "earning_reports": "Earning Reports", "earning_reports_title": "EARNING REPORTS", "edit": "Edit", "edit_json": "<PERSON>", "editcar": "Edit Car", "email": "Email", "email_id": "Enter Email Id", "email_login": "<PERSON><PERSON>", "email_msg": "We hope you enjoyed your ride with us.", "email_or_mobile_issue": "Please check your email id or mobile number. Note: Mobile number should be in International format e.g. +919999888877", "email_placeholder": "Email", "email_send": "Email Send", "email_use": "Email <PERSON>cation Mode", "emergency": "Emergency", "end_date": "End Date", "end_time": "End Time", "enter_code": "Enter the code", "estimate_fare_text": "This is your estimate fare only", "export": "Export", "extra_info": "Extra Information", "facebook_login_auth_error": "Facebook Login Error:", "feedback": "<PERSON><PERSON><PERSON>", "fill_email_first": "Add email first then take document image", "first_admin_deleted": "First admin cannot be deleted", "first_name": "First Name", "first_name_placeholder": "First Name", "first_page_tooltip": "First Page", "firstname": "First Name", "fix": "Fix It", "flat": "Flat", "flat_minorder_maxorder": "In promo type of flat your minimum order value can not exceed max discount.", "fleet_admin_comission": "Fleetadmin Commission", "fleet_admin_fee": "Fleet Admin Fee", "fleet_admin_fee_required": "Fleet Admin <PERSON>e Required", "fleetadmin_earning_reports": "<PERSON><PERSON><PERSON>", "fleetadmin_id": "Fleetadmin ID", "fleetadmin_name": "Fleetadmin Name", "fleetadmins": "Fleet Admins", "fleetadmins_title": "FLEET ADMINS", "follow_facebook": "Follow us on Facebook", "follow_instagram": "Follow us on Instagram", "follow_twitter": "Follow us on Twitter", "for_other_person": "Booking for other person", "force_end": "Force End", "forgot_password": "Forgot password?", "fromEmail": "From Email", "general_settings": "GENERAL SETTINGS", "general_settings_title": "GENERAL SETTINGS", "get_estimate": "Get Estimate", "get_started": "Get startted", "go_back": "Go Back", "go_online_to_accepting_jobs": "Start accepting jobs.", "go_to_booking": "GO TO BOOKING", "go_to_home": "Go to Home", "google_places_error": "Place ID to Location Error", "goto_home": "Goto Home", "gross_earning": "Gross Earnings", "hidden_demo": "Hidden for <PERSON><PERSON>", "home": "Home", "host": "Host", "hours": "Hours", "how_your_trip": "How is your trip?", "id": "ID", "ignore_job_title": "Do you want to ignore this job?", "ignore_text": "IGNORE", "image": "Image", "image_size_warning": "(Image size: Max 2 MB)", "image_upload_error": "Image upload error", "in_demo_mobile_login": "Mobile login is disabled in demo mode", "incomeText": "My Earnings", "incomplete_user": "User profile is incomplete.", "info": "Info", "ios": "iOS", "km": "km", "landing_slogan": "Best Services Guaranteed", "lang": "Language", "lang1": "Lang:", "langLocale": "Language Locale", "langName": "Language Name", "language": "Languages", "language_cap": "LANGUAGES", "last_name": "Last Name", "last_name_placeholder": "Last Name", "last_page_tooltip": "Last Page", "lastname": "Last Name", "license_image": "License Image", "license_image_back": "License image back", "license_image_front": "License image front", "license_image_required": "License Image Required", "loading": "Loading...", "locationServiveBody": "Background location is running...", "locationServiveTitle": "Location Update", "location_fetch_error": "Location fetch error", "location_permission_error": "Location Permission Error", "login": "<PERSON><PERSON>", "login_error": "Authentication error", "login_otp": "LOG IN WITH OTP", "login_settings": "<PERSON><PERSON>", "login_signup": "Login / Sign Up", "logout": "Logout", "make_active": "Make Active", "make_changes_to_update": "Make changes to Update", "make_default": "<PERSON>", "map_screen_drop_input_text": "Drop Where ?", "map_screen_where_input_text": "Where From ?", "marker_title_1": "Pickup", "marker_title_2": "Drop", "marker_title_3": "Stops", "max_limit": "Max Discount Allowed", "max_promo_discount_value_error": "Fill max promo discount value", "medialibrary": "Media Library", "message": "Write your message", "message_text": "Message", "method": "Method", "mile": "mi", "min_fare": "Minimum Fare", "min_fare_required": "Please Choose Minimum Fare", "min_limit": "Minimum Order Value", "min_order_error": "Fill minimum order value", "min_order_value": "Minimum order value", "mins": "mins", "minsDelayed": "Minutes Delayed", "mobile": "Mobile Number", "mobile_apps_on_store": "Mobile Apps available on App Stores", "mobile_login": "Mobile Login", "mobile_need_update": "Please input your Mobile Number for placing a booking.", "mobile_no_blank_error": "Please enter a valid mobile number.", "mobile_number": "Enter Mobile Number", "mobile_or_email_cant_off": "Mobile and Email login cannot be disabled at a time", "modal_close": "Modal has been closed.", "months": "Months", "must_login": "Please Login for Booking", "my_rides_menu": "My Bookings", "my_wallet_menu": "My Wallet", "my_wallet_tile": "My Wallet", "my_wallet_title": "WALLET", "myaccount": "My Account", "name": "Name", "navigation_available": "Map Navigation is only available when booking is ACCEPTED or STARTED", "negative_balance": "Allow Driver Negative Balance", "new_booking_notification": "You Have A New Booking Request", "next_page_tooltip": "Next Page", "next_slide": "Next", "no": "No", "no_active_booking": "No active booking", "no_active_car": "No Active Car Available", "no_bookings": "No bookings available.", "no_cancel_reason": "No cancel reasons available.", "no_cancelled_booking": "No cancelled booking ", "no_car_assign_text": "No vehicle assign", "no_cars": "No cars available.", "no_data_available": "No Data Available", "no_details_error": "Please fill up all the details properly.", "no_driver_found_alert_OK_button": "OK", "no_driver_found_alert_messege": "Sorry,No Drivers found right now.Please try again later", "no_driver_found_alert_title": "<PERSON><PERSON>!", "no_name": "No Name", "no_others_car": "No Others Car Available", "no_payment_getways": "No payment getways", "no_provider_found": "No Payment Providers Found", "no_saved_address": "No Saved Address", "no_tasks": "No tasks found for Driver", "no_user_match": "No Users Matched", "not_approved": "Not Approved", "not_available": "Unavailable", "not_call": "You can not call right now.", "not_chat": "You can not chat right now.", "not_found_text": "Not Found", "not_logged_in": "Not Logged In", "not_on_same_time": "Email or Mobile and License images can't change at the same time.", "not_registred": "This email is not registered", "not_valid_user_type": "Usertype is not valid.", "notification_title": "You have a notification", "of": "of", "off": "OFF", "offline": "You're offline", "ok": "OK", "on": "ON", "on_duty": "On Duty", "online": "You're online", "options": "OPTIONS", "order_id": "OrderId ", "order_no": "Order No : ", "other": "Other", "otherPerson": "Other Person Name", "otherPersonDetailMissing": "Please enter other person's Name & Phone No.", "otherPersonPhone": "Other Person Phone", "otherPerson_title": "Other Person Details", "other_cars": "Other Cars", "other_info": "Other Vehicle or Driver Info", "otp": "OTP :", "otp_blank_error": "OTP can't be blank", "otp_here": "Please enter OTP here", "otp_sms": " is the OTP. Thank you.", "otp_validate_error": "OTP is not valid", "page_not_not_found": "Page Not Found", "page_not_not_found_text": "Sorry! The page you are looking for is no longer available or has been removed.", "panic_num": "Panic Dial Number", "panic_question": "Do you really want to make a Panic Call?", "panic_text": "Panic Call", "parcel_option": "Parcel Option", "parcel_type": "PARCEL TYPE", "parcel_type_web": "Parcel Type", "parcel_types": "PARCEL TYPES", "password": "Password", "password_alphaNumeric_check": "Password required alphaNumeric", "password_blank_messege": "password not blank", "password_complexity_check": "Password complexity", "password_must_be_8-16_characters_long": "Password must be 8-16 Characters Long.", "password_must_contain_at_least_one_digit": "Password must contain at least one Digit.", "password_must_have_at_least_one_lowercase_character": "Password must have at least one Lowercase Character.", "password_must_have_at_least_one_uppercase_character": "Password must have at least one Uppercase Character.", "password_must_not_contain_whitespaces": "Password must not contain Whitespaces.", "past_booking_error": "Book Later is not available for Past DateTime or within next 15 mins.", "payWithCard": "Pay With Card", "pay_cash": "PAY WITH CASH", "pay_mode": "Payment Mode", "payable_ammount": "Payable Amount", "payaed_ammount": "Payed Amount", "payment": "Payment", "payment_cancelled": "Payment Cancelled", "payment_fail": "Your Payment Failed", "payment_gateway": "Payment Gateway", "payment_info": "Payment Info", "payment_mode": "PAYMENT MODE", "payment_mode_web": "Payment Mode", "payment_of": "Your Payment of ", "payment_settings": "Payment Settings", "payment_status": "Payment Status", "payment_thanks": "Thank you for your payment.", "paynow_button": "Pay Now", "pending": "Pending", "percentage": "Percentage", "personal_info": "Personal Info", "phone": "Phone", "phone_error_msg": "*Please write Phone Number with your country code. Ex.: +449876543210", "phone_no_update": "Please update your Phone number in the Edit Profile section before making a booking", "pickUpInstructions": "PICKUP INSTRUCTION", "pickUpInstructions_web": "Pickup Instruction", "pickup_address": "Pickup Address", "pickup_address_from_map": "Select pickup address from map", "pickup_location": "Pickup Location", "place_to_coords_error": "Place to Coordinate error. Please try again.", "port": "Port", "position": "List Position", "position_required": "Please Choose List position", "prepaid": "Prepaid", "previous_page_tooltip": "Previous Page", "privacy_policy": "Privacy Policy", "privacy_policy_change_privacy_para1": "We may update our Privacy Policy from time to time. Thus, you are advised to review this page periodically for any changes. We will notify you of any changes by posting the new Privacy Policy on this page.", "privacy_policy_change_privacy_para2": "This policy is effective as of 2023-12-12", "privacy_policy_children_para1": "These Services do not address anyone under the age of 13. We do not knowingly collect personally identifiable information from children under 13. In the case we discover that a child under 13 has provided us with personal information, we immediately delete this from our servers. If you are a parent or guardian and you are aware that your child has provided us with personal information, please contact us so that we will be able to do necessary actions.", "privacy_policy_contact_para1": "If you have any questions or suggestions about our Privacy Policy, do not hesitate to contact us at ", "privacy_policy_cookie_para1": "Cookies are files with a small amount of data that are commonly used as anonymous unique identifiers. These are sent to your browser from the websites that you visit and are stored on your device's internal memory.", "privacy_policy_cookie_para2": "This Service does not use these “cookies” explicitly. However, the app may use third party code and libraries that use “cookies” to collect information and improve their services. You have the option to either accept or refuse these cookies and know when a cookie is being sent to your device. If you choose to refuse our cookies, you may not be able to use some portions of this Service.", "privacy_policy_heading_change_privacy": "CHANGES TO THIS PRIVACY POLICY", "privacy_policy_heading_children": "CHILDREN’S PRIVACY", "privacy_policy_heading_contact": "CONTACT US", "privacy_policy_heading_cookie": "COOKIES", "privacy_policy_heading_info": "INFORMATION COLLECTION AND USE", "privacy_policy_heading_link": "LINKS TO OTHER SITES", "privacy_policy_heading_log": "LOG DATA", "privacy_policy_heading_security": "SECURITY", "privacy_policy_heading_service": "SERVICE PROVIDERS", "privacy_policy_info_list1": "First Name", "privacy_policy_info_list2": "Last Name", "privacy_policy_info_list3": "Email Address", "privacy_policy_info_list4": "Phone Number", "privacy_policy_info_list5": "Search vehicle location", "privacy_policy_info_list6": "Travel Route and Navigation", "privacy_policy_info_list7": "Real-time vehicle movement", "privacy_policy_info_para1": "For a better experience, while using our Service, we will require you to provide us with certain personally identifiable information, including but not limited to ", "privacy_policy_info_para2": "The information that we request will be retained by us and used as described in this privacy policy.", "privacy_policy_info_para3": "We do collect the following sensitive information when you use or Sign up on our App ", "privacy_policy_info_para4": "We collect your location data to enable", "privacy_policy_info_para5": "The app use third party services for social login like Google and Apple Login that collect information used to identify you. We capture User email and Name from social login if user has chosen it to be disclosed while performing a social login.", "privacy_policy_link_para1": "This Service may contain links to other sites. If you click on a third-party link, you will be directed to that site. Note that these external sites are not operated by us. Therefore, we strongly advise you to review the Privacy Policy of these websites. We have no control over and assume no responsibility for the content, privacy policies, or practices of any third-party sites or services.", "privacy_policy_log_para1": "We want to inform you that whenever you use our Service, in a case of an error in the app we collect data and information (through third party products) on your phone called Log Data. This Log Data may include information such as your device Internet Protocol (“IP”) address, device name, operating system version, the configuration of the app when utilizing our Service, the time and date of your use of the Service, and other statistics.", "privacy_policy_para1": "This page is used to inform visitors regarding our policies with the collection, use, and disclosure of Personal Information if anyone decided to use any of our Service.", "privacy_policy_para2": "If you choose to use our Service, then you agree to the collection and use of information in relation to this policy. The Personal Information that we collect is used for providing and improving the Service. We will not use or share your information with anyone except as described in this Privacy Policy.", "privacy_policy_para3": "The terms used in this Privacy Policy have the same meanings as in our Terms and Conditions, which is accessible at our all Apps unless otherwise defined in this Privacy Policy.", "privacy_policy_security_para1": "We value your trust in providing us your Personal Information, thus we are striving to use commercially acceptable means of protecting it. But remember that no method of transmission over the internet, or method of electronic storage is 100% secure and reliable, and we cannot guarantee its absolute security.", "privacy_policy_service_list1": "To facilitate our Service;", "privacy_policy_service_list2": "To provide the Service on our behalf;", "privacy_policy_service_list3": "To perform Service-related services; or", "privacy_policy_service_list4": "To assist us in analyzing how our Service is used.", "privacy_policy_service_para1": "We may employ third-party companies and individuals due to the following reasons:", "privacy_policy_service_para2": " We want to inform users of this Service that these third parties have access to your Personal Information. The reason is to perform the tasks assigned to them on our behalf. However, they are obligated not to disclose or use the information for any other purpose.", "processDate": "Process Date", "process_withdraw": "Process Withdraw", "processed": "Processed", "product_section_1": "You experience is important to us. We never compromise on our standards. Safety regulations are particularly emphasized to ensure peace of mind.", "product_section_2": "View live tracking, price estimations, and even book ahead of time! We have every avenue covered with every latest technology.", "product_section_3": "Driver can add many type of vehicles.He/She can set one vehicle as default for booking", "product_section_4": "Aside from cash, credit card, payments are PCI DSS compliant. The website has an SSL certification, and the apps are checked by the Apple Store and Google Play.", "product_section_heading": "Service Information", "product_section_para": "We provide the best App based services in the country.", "profile": "Profile", "profile_image": "Profile Image", "profile_incomplete": "Profile incomplete. Go to Profile Settings.", "profile_page_subtitle": "Profile Details", "profile_page_title": "My Profile", "profile_setting_menu": "Profile Settings", "profile_title": "PROFILE", "profile_updated": "Profile Updated.", "promo": "Promos", "promo_apply": "(Promo Apply)", "promo_code": "PROMO CODE", "promo_code_error": "Fill promo code", "promo_code_web": "Promo Code", "promo_description_error": "Fill promo description", "promo_discount": "Promo Discount", "promo_discount_type_error": "Fill promo discount type", "promo_discount_value": "Promo Value", "promo_discount_value_error": "Fill promo discount value", "promo_eligiblity": "Sorry! Promo is not valid as bill amount is lower.", "promo_exp": "Sorry! Promo has expired.", "promo_exp_limit": "Sorry! Promo usage limit over.", "promo_name": "Promo Name", "promo_name_error": "Fill promo name", "promo_not_found": "This promo code not found", "promo_offer": "Promo and Offers", "promo_offer_title": "PROMO AND OFFERS", "promo_usage": "Promo Count Available", "promo_used": "Sorry! You have already used the promo code.", "promo_used_by": "Promo Used By Count", "proper_bonus": "PLease enter proper bonus in numbers.", "proper_email": "Please enter email properly.", "proper_input_image": "Upload Proper Image", "proper_input_licenseimage": "Upload proper License Image", "proper_input_name": "Enter proper name", "proper_input_vehicleno": "Enter proper vehicle number", "proper_mobile": "Please enter mobile properly.", "provider_fetch_error": "Unable to fetch payment providers", "provider_not_found": "No Payment Providers Found.", "pruduct_section_heading_1": "Comfortable", "pruduct_section_heading_2": "Convenient", "pruduct_section_heading_3": "Vehicle Management", "pruduct_section_heading_4": "Secure", "push_error_1": "Failed to get push token for push notification!", "push_error_2": "Must use physical device for Push Notifications", "push_notification_title": "Push Notifications", "push_notifications": "Push Notifications", "push_notifications_title": "PUSH NOTIFICATIONS", "queue": "Busy", "rate_for": "Rate for", "rate_per_hour": "Rate Per Hour", "rate_per_hour_required": "Please Choose Rate Per Hour", "rate_per_unit_distance": "Distance Rate Per (Km or Mile)", "rate_per_unit_distance_required": "Please Choose Distance Rate Per (Km or Mile)", "rate_ride": "Rate Your Driver", "real_time_driver_section_text": "Drivers Realtime", "realtime_drivers": "Driver Live Location", "reason": "Reason", "receipt": "Receipt", "received_rating": "You received a X star rating", "refer_earn": "Refer & Earn", "referer_not_found": "Referer Id not found", "referralId": "Referral Id", "referral_bonus": "Referral Bonus", "referral_email_used": "This Email already used a referal Id", "referral_id_placeholder": "Referral Id (Optional)", "referral_number_used": "This Number already used a referal Id", "reg_error": "Error while SignUp", "register": "Register", "register_as_driver": "Register User?", "register_button": "Register Now", "registration_title": "Registration", "remove_promo": "Remove Promo", "report": "Reports", "requestDate": "Request Date", "request_otp": "Sign In With OTP", "request_payment": "Request Payment", "require_approval": "Your account requires approval from Admin", "ride_details_page_title": "Booking Details", "ride_information": "Ride Info", "ride_list_title": "My Bookings", "rider_cancel_text": "Booking Cancel", "rider_not_here": "NO REQUESTS AT THE MOMENT", "rider_withdraw": "Customer <PERSON>w", "riders": "Customers", "riders_title": "CUSTOMERS", "rides": "Rides", "roundTrip": "ROUND TRIP", "rows": "Rows", "save": "SAVE", "saved_address": "Saved Address", "search": "Search", "search_for_an_address": "Search for an address", "searching": "Searching", "secure": "Secure", "security_title": "Security Settings", "selectBid": "Select Bid", "select_car": "Select Vehicle Type", "select_country": "Select Country", "select_date": "Please Select Date", "select_driver": "Select Driver", "select_payment_getway": "Select Payment Getway", "select_proper": "Please select properly.", "select_reason": "Select Cancellation Reason", "select_user": "Select User", "send_button_text": "Send", "senderPersonPhone": "Booking Person Phone No", "service_off": "YOUR SERVICE STATUS IS 'OFF'", "service_start_soon": "Our service will start soon...", "set_decimal": "Set <PERSON>", "set_link_email": "Reset password link send to above mail", "settings_label3": "Booking OTP", "settings_label4": "<PERSON> Approval", "settings_label5": "Car Approval", "settings_label6": "Verify Id / Passport", "settings_title": "Settings", "share_msg": "Hi, Use this code while registering and get Bonus of ", "share_msg_no_bonus": "Hi, Check out this App. I am loving it.", "show_in_list": "Show in list", "show_live_route": "Show Live Route", "signIn": "SignIn", "signup": "Sign Up", "signup_via_referral": "SignUp Via Referral", "skip": "<PERSON><PERSON>", "smssettings": "SMS Settings", "smssettings_title": "SMS SETTINGS", "smtp_error": "Please check your SMTP details", "smtpsettings": "SMTP Settings", "smtpsettings_title": "SMTP SETTINGS", "social_login": "Social Login", "solved": "Solved", "sos": "Sos", "sos_title": "SOS", "spacer_message": "OR CONNECT WITH", "start_accept_bid": "You Have Receive One Bid", "start_trip": "START TRIP", "stops": "Stops", "subject": "Subject", "submit": "SUBMIT", "submit_rating": "SUBMIT RATING", "success": "Success", "success_payment": "Payment done successfully.", "swipe_symbol": "Swipe Symbol Direction", "system_price": "System Price", "system_price_with_bid": "System Price With Bid", "take_deliver_image": "DELIVERY IMAGE", "take_deliver_image_web": "Delivery Image", "take_pickup_image": "PICKUP IMAGE", "take_pickup_image_web": "Pickup Image", "task_list": "Home", "term_condition": "Terms & Conditions", "term_condition_heading1": "Acceptance of Terms :", "term_condition_heading10": "Termination :", "term_condition_heading11": "Governing Law :", "term_condition_heading12": "Amendments :", "term_condition_heading2": "Eligibility :", "term_condition_heading3": "Service Description :", "term_condition_heading4": "Payment :", "term_condition_heading5": "User Conduct :", "term_condition_heading6": "User Content :", "term_condition_heading7": "Intellectual Property :", "term_condition_heading8": "Disclaimers :", "term_condition_heading9": "Limitation of Liability :", "term_condition_para1": "Thank you for choosing our app-based booking services for taxi, cab, and delivery needs. Please read the following terms and conditions carefully before using our services:", "term_condition_para10": "In no event shall we be liable for any indirect, incidental, special, or consequential damages arising out of or in connection with the use of our services, whether or not we have been advised of the possibility of such damages.", "term_condition_para11": "We reserve the right to terminate a users access to our platform at any time, without notice, for any reason.", "term_condition_para12": "These Terms and Conditions shall be governed by and construed in accordance with the laws of the jurisdiction where the company is based.", "term_condition_para13": "We reserve the right to update these Terms and Conditions at any time, without notice. Users are advised to review these Terms and Conditions periodically to stay informed of any changes.", "term_condition_para14": "If you have any questions or concerns about these Terms and Conditions, please contact us at ", "term_condition_para2": "By using our services, you agree to be bound by these Terms and Conditions. If you do not agree to these Terms and Conditions, you may not use our services.", "term_condition_para3": "You must be at least 18 years old to use our services. By using our services, you represent and warrant that you are at least 18 years old.", "term_condition_para4": "Our app-based booking services provide a platform to connect users with taxi, cab, and delivery providers. We do not own, operate, or control any of the vehicles or drivers that use our platform. Our services are intended to be used for personal and non-commercial use only.", "term_condition_para5": "Users are required to pay for services provided through our platform. We accept various forms of payment, including credit cards, debit cards, and electronic payment methods. All payments made through our platform are subject to our payment policies, which may be updated from time to time.", "term_condition_para6": "Users are expected to use our services responsibly and not engage in any behavior that may harm our platform, drivers, or other users. Users are not permitted to use our platform for any unlawful or unauthorized purpose. Users are also prohibited from interfering with the operation of our platform or engaging in any activity that could compromise the security or integrity of our platform.", "term_condition_para7": "Users are solely responsible for the content they submit through our platform. By submitting content, users grant us a non-exclusive, royalty-free, transferable, and sublicensable license to use, modify, and reproduce the content for the purpose of providing our services.", "term_condition_para8": "Our platform, including all content and intellectual property, is owned by us and/or our licensors. Users may not copy, modify, distribute, or reproduce any of the content or intellectual property without our prior written consent.", "term_condition_para9": "We do not guarantee the availability, reliability, or quality of any services provided by drivers using our platform. We are not responsible for any loss or damage that may result from the use of our services. Our platform is provided 'as is' and without any warranties, express or implied.", "term_required": "Term Required", "terms": "Privacy Policy", "thanks": "Thanks For Rating", "this_month_text": "Month", "thismonth": "THIS MONTH", "thisyear": "THIS YEAR", "time": "TIME", "title": "Title", "today_text": "Today", "total": "Total", "total_cumtomer": "No. of Customer", "total_drivers": "No. of Drivers", "total_fare": "Total Fare", "total_time": "Total Time", "totalearning": "Total Earnings", "transaction_history_title": "WALLET TRANSACTION HISTORY", "transaction_id": "Transaction Id :", "tripInstructions": "TRIP INSTRUCTIONS", "trip_cost": "Trip Cost", "trip_cost_driver_share": "Driver Share", "trip_date_time": "Trip Date Time", "trip_end_time": "Trip End Time", "trip_instruction": "Trip Instruction", "trip_start_date": "Trip Start Date", "trip_start_time": "Trip Start Time", "try_again": "Please try again.", "type": "Type", "update_admin": "<PERSON><PERSON><PERSON>", "update_admin_title": "UPDATE ADMIN", "update_any": "You cannot update Email and Mobile at same time.", "update_button": "Update Now", "update_carType_title": "UPDATE CAR TYPE", "update_car_title": "UPDATE CAR", "update_customer_title": "UPDATE CUSTOMER", "update_driver_title": "UPDATE DRIVER", "update_failed": "Update Failed", "update_fleetAdmin": "Upadate Fleet Admin", "update_fleetAdmin_title": "UPDATE FLEET ADMIN", "update_profile_title": "Update Document", "update_promo_title": "UPDATE PROMO", "updated": "Updated", "upload_car_image": "Upload Car Image", "upload_driving_license": "Upload your Driving License", "upload_driving_license_back": "Upload your Driving License Back", "upload_driving_license_front": "Upload your Driving License Front", "upload_id_details": "Upload Your ID / Passport", "upload_profile_image": "Upload Profile Image", "upload_verifyIdImage": "Upload your ID / Passport Number", "use_distance_matrix": "Use Distance Matrix API", "use_wallet_balance": "Use Wallet Cash (Your balance is ", "user": "Users", "user_exists": "User with same Email or Mobile number exists.", "user_issue_contact_admin": "No profile data found. Please contact Admin", "user_type": "User Type", "username": "Username", "users_title": "Users", "usertype": "User Type", "valid_amount": "Please enter a valid amount", "value_for_money": "Value For Money", "vehicleMake_required": "Vehicle Make/Brand Name Required", "vehicleModel_required": "Vehicle Model Required", "vehicleNumber_required": "Vehicle Reg No. Required", "vehicle_make": "Make", "vehicle_model": "Model", "vehicle_model_name": "Vehicle Make / Brand Name", "vehicle_model_no": "Vehicle Model No", "vehicle_no": "Vehicle Number", "vehicle_number": "Number", "vehicle_reg_no": "Vehicle Registration Number", "verify_id": "Id / Passport Number", "verify_otp": "Verify OTP", "verifyid_error": "Sorry you have no ID / Passport", "verifyid_image": "Id / Passport Image", "wallet": "Wallet", "wallet_bal_low": "Wallet balance low.", "wallet_balance": "Wallet Balance", "wallet_balance_low": "Your wallet balance is too low.", "wallet_balance_threshold_reached": "Your Wallet Balance Threshold Reached", "wallet_balance_zero": "You Wallet Balance is 0. If you take Payment in Cash, the Admin Convenience Fee will get deducted from Wallet and your Wallet Balance will be in Negative after the Job. Negative balance will restrict further Jobs. You need to Recharge Wallet or Ask Admin for help.", "wallet_balance_zero_customer": "You wallet balance is negative. You are not allowed to book with negative balance", "wallet_ballance": "Wallet Balance", "wallet_booking_alert": "You cannot book another by using Wallet. You have an existing wallet booking.", "wallet_money_field": "Wallet Denominations", "wallet_payment_amount": "Wallet payment amount", "wallet_title": "Finance", "wallet_updated": "Wallet Updated successfully.", "wallet_zero": "Wallet Balance 0.", "was_successful": " was successful", "where_are_you": "You Are Here", "withdraw": "WITHDRAW", "withdraw_below_zero": "Withdraw amount cannot be less than or equal to 0.", "withdraw_money": "Withdraw Money", "withdraw_more": "Withdraw amount cannot be greater than Wallet Balance.", "withdrawn": "WITHDRAWN", "withdraws": "WITHDRAWS", "withdraws_web": "Withdraws", "within_min": "min", "work": "Work", "year": "Years", "yes": "Yes", "you_are_offline": "Your duty is off", "you_rated_text": "Driver Rating", "your_fare": "Your Fare", "your_feedback": "Your feedback (optional) ...", "your_feedback_test": "Your feedback will help us improve driving experience better.", "your_promo": "Your promo", "your_trip": "Your Trip"}, "langLocale": "en", "langName": "English", "tableData": {"id": 1}}}, "locations": {"Mo6GG7QnH1fs42t4JtxtoQavRpE2": {"lat": 20.6857059, "lng": -103.3130367}, "j3NYf7YtOLNcJUTlhUAbzkUOdUM2": {"lat": 20.6856926, "lng": -103.3129783}, "tASwzLMHn4RA1pAZ1Uurl6zywmu1": {"lat": 20.4120136, "lng": -103.6667565}}, "payment_settings": {"braintree": {"active": false, "merchantId": "", "privateKey": "", "publicKey": "", "testing": false}, "culqi": {"PUBLIC_KEY": "", "SECURE_KEY": "", "active": false}, "flutterwave": {"FLUTTERWAVE_PUBLIC_KEY": "", "FLUTTERWAVE_SECRET_KEY": "", "active": false}, "iyzico": {"active": false, "apiKey": "", "secretKey": "", "testing": false}, "liqpay": {"active": false, "private_key": "", "public_key": ""}, "mercadopago": {"access_token": "", "active": false, "public_key": ""}, "payfast": {"PAYFAST_MERCHANT_KEY": "", "PAYFAST_MEWRCHANT_ID": "", "active": false, "testingMode": false}, "paymongo": {"active": false, "public_key": "", "secret_key": ""}, "paypal": {"active": false, "paypal_client_id": "", "paypal_secret": "", "testing": false}, "paystack": {"PAYSTACK_PUBLIC_KEY": "", "PAYSTACK_SECRET_KEY": "", "active": false}, "paytm": {"PAYTM_MERCHANT_KEY": "", "PAYTM_MEWRCHANT_ID": "", "active": false, "testing": false}, "payulatam": {"accountId": 0, "active": false, "apiKey": "", "merchantId": 0, "testing": false}, "razorpay": {"KEY_ID": "", "KEY_SECRET": "", "active": false}, "securepay": {"MERCHANT_CODE": "", "TXN_PASSWORD": "", "active": false, "testing": false}, "slickpay": {"active": false, "publicKey": "", "testing": false}, "squareup": {"ACCESS_TOKEN": "", "APPLICATION_ID": "", "LOCATION_ID": "", "active": false, "testing": false}, "stripe": {"active": false, "stripe_private_key": "", "stripe_public_key": ""}, "test": {"active": true}, "wipay": {"ACCOUNT_NO": "", "API_KEY": "", "active": false, "testing": false}}, "promos": {"promo1": {"max_promo_discount_value": 10, "min_order": 10, "promo_code": "ASDFG", "promo_description": "$10 for Testing", "promo_discount_type": "flat", "promo_discount_value": 10, "promo_name": "Test Promo", "promo_show": true, "promo_usage_limit": 100, "user_avail": 0}}, "settings": {"AllowCountrySelection": false, "AllowCriticalEditsAdmin": true, "AllowDeliveryPickupImageCapture": false, "AllowFinalDeliveryImageCapture": true, "AppleLoginEnabled": true, "AppleStoreLink": "https://apps.apple.com/", "CarHornRepeat": true, "CompanyAddress": "<PERSON><PERSON> 890, Santa María, 44350 Guadalajara, Jal.", "CompanyName": "Ballesteros TI", "CompanyPhone": "+************", "CompanyTermCondition": "https://exicube-test-delivery.web.app/term-condition", "CompanyTerms": "https://exicubedelivery.web.app/privacy-policy", "CompanyWebsite": "https://via-empresas.cloud", "FacebookHandle": "https://facebook.com/", "FacebookLoginEnabled": true, "InstagramHandle": "", "PlayStoreLink": "https://play.google.com/", "RiderWithDraw": false, "TwitterHandle": "https://twitter.com/", "appName": "ART & SOURCING", "autoDispatch": false, "bank_fields": false, "bonus": 10, "bookingFlow": "1", "carType_required": true, "code": "MXN", "contact_email": "", "convert_to_mile": false, "country": "Mexico", "customMobileOTP": false, "decimal": 2, "driverRadius": 10, "driver_approval": false, "emailLogin": true, "horizontal_view": false, "imageIdApproval": false, "license_image_required": true, "mapLanguage": "en", "mobileLogin": false, "negativeBalance": false, "otp_secure": false, "prepaid": true, "realtime_drivers": true, "restrictCountry": "", "showLiveRoute": true, "socialLogin": false, "swipe_symbol": false, "symbol": "$", "term_required": true, "useDistanceMatrix": false}, "tracking": {"-OWIWAh4GfUAlpzZap3F": {"-OWIWqtBYVa6y1gZMdEN": {"at": *************, "lat": 20.6857974, "lng": -103.3129867, "status": "ACCEPTED"}, "-OWIWqtEyWuqaB-6pstG": {"at": *************, "lat": 20.6857974, "lng": -103.3129867, "status": "ACCEPTED"}, "-OWIWqtGPG3pz5bshfH9": {"at": *************, "lat": 20.6857974, "lng": -103.3129867, "status": "ARRIVED"}, "-OWI_f32qlFjIxaLCgQS": {"at": 1753749040029, "lat": 20.68572, "lng": -103.3132283, "status": "STARTED"}, "-OWI_jroucT1BVwljiIm": {"at": 1753749059726, "lat": 20.6856831, "lng": -103.3131373, "status": "STARTED"}, "-OWIa2twZ4B55W_cDEoQ": {"at": 1753749141782, "lat": 20.6857468, "lng": -103.3132148, "status": "STARTED"}, "-OWIa3AbxUYFnw0kQd6T": {"at": 1753749142912, "lat": 20.685751, "lng": -103.3132152, "status": "REACHED"}}, "-OWQSBJB9Bce5-smNN9j": {"-OWQU3AGvaVpmK-U1mVm": {"at": 1753881525760, "lat": 20.68571, "lng": -103.3130377, "status": "ACCEPTED"}, "-OWQUKPb2CQxOhz4nuXV": {"at": 1753881596375, "lat": 20.6857055, "lng": -103.3130388, "status": "STARTED"}, "-OWQUZm0_EcdW7Q3P4He": {"at": 1753881659313, "lat": 20.6857047, "lng": -103.3130413, "status": "REACHED"}}, "-OWQUowLYK9ljrxuBkkI": {"-OWQUuHTn-7wTRQKzQ80": {"at": 1753881747406, "lat": 20.6857056, "lng": -103.3130369, "status": "ACCEPTED"}, "-OWQWMIKSPcOnz8OKRuY": {"at": 1753882128389, "lat": 20.6857064, "lng": -103.3130552, "status": "STARTED"}, "-OWQWlnp-5C90Ww6OCBD": {"at": 1753882236965, "lat": 20.6857053, "lng": -103.313053, "status": "REACHED"}}, "-OWR5Fxxr6XZz_aSczVP": {"-OWR5VDUcqCfKzy7H5b-": {"at": 1753891864304, "lat": 20.6857035, "lng": -103.3130477, "status": "ACCEPTED"}, "-OWR5_vcXJ83835gQNOf": {"at": 1753891887635, "lat": 20.6857057, "lng": -103.3130525, "status": "ACCEPTED"}}, "-OWR60oXd9n2vt5IVP8b": {"-OWR6UrObcWHYhLIoLNs": {"at": 1753892124940, "lat": 20.685707, "lng": -103.3130552, "status": "ACCEPTED"}, "-OWR6X3MmjKzxyV4fHqN": {"at": 1753892133962, "lat": 20.6857058, "lng": -103.3130464, "status": "STARTED"}, "-OWR6t0auE4C8sZzhfXB": {"at": 1753892227993, "lat": 20.6857073, "lng": -103.3130344, "status": "REACHED"}}, "-OWR7D4mnGBbgExSyano": {"-OWR7ER5gSIcucpcx3uQ": {"at": 1753892319977, "lat": 20.6857057, "lng": -103.3130371, "status": "ACCEPTED"}}, "-OWRNk_CUeOTUg9RcNVZ": {"-OWRNyac5W4riKfYBUWJ": {"at": 1753896707282, "lat": 20.6856945, "lng": -103.3130256, "status": "ACCEPTED"}, "-OWRO4LhjeRF1MpPeOJm": {"at": 1753896734937, "lat": 20.6859145, "lng": -103.3129706, "status": "ACCEPTED"}, "-OWRO9S-bl5UGxD0qO1d": {"at": 1753896755820, "lat": 20.6856978, "lng": -103.3130129, "status": "ACCEPTED"}, "-OWRO9Wt40Hx84UxXWqS": {"at": 1753896756132, "lat": 20.6856978, "lng": -103.3130129, "status": "STARTED"}, "-OWROjabmiavYtsDBLo2": {"at": 1753896907987, "lat": 20.6856959, "lng": -103.3130246, "status": "REACHED"}}, "-OWRR2Eozr4gLkgkbyTd": {"-OWRRBXh7ZyHgb0vJmk3": {"at": 1753897550809, "lat": 20.6856941, "lng": -103.3130263, "status": "ACCEPTED"}, "-OWRSGLi4C-iIianXVl4": {"at": 1753897832666, "lat": 20.6856941, "lng": -103.3130347, "status": "STARTED"}, "-OWRSM0-m-bHrJvdQGBr": {"at": 1753897855852, "lat": 20.6856938, "lng": -103.3130272, "status": "REACHED"}}, "-OWRVaCUXDCNM-fx-soe": {"-OWRVhgb9HipMijO7SGi": {"at": 1753898735187, "lat": 20.6857084, "lng": -103.3130295, "status": "ACCEPTED"}, "-OWRW4-2mSUugsPS0wGe": {"at": 1753898830639, "lat": 20.6856869, "lng": -103.3130203, "status": "STARTED"}, "-OWRXa6_yrxcQjVXJ5IW": {"at": 1753899228433, "lat": 20.6857086, "lng": -103.3130308, "status": "REACHED"}}, "-OWR_lkXuOp4SSH6rtvJ": {"-OWR_w84NuQhF6oFklmm": {"at": 1753900105073, "lat": 20.6856938, "lng": -103.3130272, "status": "ACCEPTED"}, "-OWRa6JL-AtktLuvxHX0": {"at": 1753900150849, "lat": 20.6856943, "lng": -103.3130363, "status": "STARTED"}, "-OWRaFwpIxnWrjDnIpP4": {"at": 1753900190305, "lat": 20.6857072, "lng": -103.3130242, "status": "REACHED"}}, "-OWRckDxc0h6Gw2WLiBK": {"-OWRcsdiHfuphPS7QvyB": {"at": 1753900877210, "lat": 20.6857084, "lng": -103.3130305, "status": "ACCEPTED"}, "-OWRcxXV6PU_zMFNbEuz": {"at": 1753900897227, "lat": 20.6857088, "lng": -103.3130138, "status": "ARRIVED"}, "-OWRdP4hbEaWCFwhQ2w5": {"at": 1753901014169, "lat": 20.6856942, "lng": -103.3130245, "status": "STARTED"}, "-OWRe6jB1FDCkwvAktV9": {"at": 1753901201143, "lat": 20.6856939, "lng": -103.3130268, "status": "REACHED"}}, "-OWRiIIKbyD9kNlnTY0h": {"-OWRiUq7Hh3FJbbGqtZN": {"at": 1753902348473, "lat": 20.6857034, "lng": -103.3130385, "status": "ACCEPTED"}, "-OWRiZr3NEEIDyked9vT": {"at": 1753902369013, "lat": 20.6857009, "lng": -103.3130154, "status": "STARTED"}, "-OWRil2_HsrzBUY_xt0h": {"at": 1753902418966, "lat": 20.6857056, "lng": -103.3130532, "status": "REACHED"}}, "-OWS1_ihVcsdlneEMz6y": {"-OWS1hKogcq6seqHcBGo": {"at": 1753907646626, "lat": 20.6857049, "lng": -103.3130509, "status": "ACCEPTED"}, "-OWS1j3cyEo27cX0kEJ-": {"at": 1753907653719, "lat": 20.6857027, "lng": -103.3130183, "status": "STARTED"}, "-OWS1pHKtnVsK5EkW0qL": {"at": 1753907679172, "lat": 20.6857074, "lng": -103.3130327, "status": "REACHED"}}, "-OWSc-puYsqTGgbUSw79": {"-OWSc4cd8MakDXw5QyS3": {"at": 1753917445469, "lat": 20.6856664, "lng": -103.3129766, "status": "ACCEPTED"}, "-OWScR1p2AtK7LxkyVhC": {"at": 1753917537249, "lat": 20.6857117, "lng": -103.3130455, "status": "STARTED"}, "-OWScdqxdRwqNAFiTiLB": {"at": 1753917593889, "lat": 20.6858118, "lng": -103.3130467, "status": "STARTED"}, "-OWSceSJ3DRKr9AtraZw": {"at": 1753917596345, "lat": 20.6858213, "lng": -103.3130415, "status": "REACHED"}}, "-OWShUnim0XKm_LzFaQj": {"-OWWxnzrh9oeCIrTiVsW": {"at": 1753990249261, "lat": 20.6857573, "lng": -103.3130145, "status": "ACCEPTED"}, "-OWWxnzuOpF-2oK8TyLI": {"at": 1753990249265, "lat": 20.6857573, "lng": -103.3130145, "status": "ARRIVED"}, "-OWWxo0Fz-bIQkOJwLeY": {"at": 1753990249351, "lat": 20.6857573, "lng": -103.3130145, "status": "ACCEPTED"}, "-OWXPgrY3yekABJft-dc": {"at": 1753997822349, "lat": 20.6857031, "lng": -103.3130399, "status": "STARTED"}, "-OWXQW6vSxy8B6ikyCPO": {"at": 1753998036271, "lat": 20.6856531, "lng": -103.3130099, "status": "REACHED"}}, "-OWSqKSqbi490roXZsRG": {"-OWWag7tZ3TR_m76z6ah": {"at": 1753984187759, "lat": 20.6857041, "lng": -103.3130483, "status": "ACCEPTED"}}, "-OWaGPwz7Mkh7O-9Z8d1": {"-OWaGWFQqbIkHwVon1Vo": {"at": 1754062524263, "lat": 20.6856933, "lng": -103.3129893, "status": "ACCEPTED"}, "-OWaGWHbUXucWUC3hU1W": {"at": 1754062524403, "lat": 20.6856933, "lng": -103.3129893, "status": "ACCEPTED"}, "-OWaGWuFYsmAbNzQB86v": {"at": 1754062526940, "lat": 20.6857073, "lng": -103.3129949, "status": "ARRIVED"}, "-OWaGcHSHotWN_ZKWXWK": {"at": 1754062553065, "lat": 20.6857253, "lng": -103.3129958, "status": "STARTED"}, "-OWaGhbtZJVpB87_Bbat": {"at": 1754062574916, "lat": 20.68581, "lng": -103.3129607, "status": "STARTED"}, "-OWaGkIWJVwXa1HC9X-f": {"at": 1754062585901, "lat": 20.6857152, "lng": -103.3129531, "status": "STARTED"}, "-OWaGkW0yqsNA8fdCK--": {"at": 1754062586765, "lat": 20.6857123, "lng": -103.3129554, "status": "REACHED"}}, "-OWbilQORNvQArJRdF9e": {"-OWbizXp-Jp8iJhuAorm": {"at": 1754087027700, "lat": 20.6858535, "lng": -103.3129441, "status": "ACCEPTED"}, "-OWbizaG7fOC6J_lSj1q": {"at": 1754087027920, "lat": 20.6858976, "lng": -103.3129266, "status": "ARRIVED"}, "-OWbjFSo7w3Eg1GaGFrS": {"at": 1754087097011, "lat": 20.6860481, "lng": -103.3130145, "status": "STARTED"}, "-OWbjHuEIN0jXGpTnfFS": {"at": 1754087107020, "lat": 20.6859494, "lng": -103.3130455, "status": "STARTED"}, "-OWbjMH9iEXB_Ty4Xk1N": {"at": 1754087124937, "lat": 20.6858017, "lng": -103.3129894, "status": "STARTED"}, "-OWbjNG5c1cTgEMWcN33": {"at": 1754087128965, "lat": 20.6857066, "lng": -103.3129824, "status": "STARTED"}, "-OWbjPgu3QwzV6G-6tXs": {"at": 1754087138937, "lat": 20.6858177, "lng": -103.3130063, "status": "STARTED"}, "-OWbjT1qm8SUC5v6Y_dN": {"at": 1754087152628, "lat": 20.6857036, "lng": -103.3130537, "status": "STARTED"}, "-OWbjV7ysMttO1DoNBar": {"at": 1754087161213, "lat": 20.685683, "lng": -103.3129958, "status": "REACHED"}}}, "userNotifications": {"-OWSeRH6x8kaAt-WxtfH": {"-OWWah9Wv8_dtzemQbIE": {"dated": 1753984193196, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}, "-OWWxpMKXO9LjjI16u9T": {"dated": 1753990256092, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}, "-OWaGXasYerjli7Ek3j7": {"dated": 1754062531000, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}, "-OWbj-VnFaVZetpZYeT-": {"dated": 1754087032891, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}}, "-OWaOYoXn__qr74Sl8oz": {"-OWbj-Vpk_WOcwxCouFw": {"dated": 1754087032893, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}}, "-OWaOgYboflCyAxml_wl": {"-OWbj-VqZi3gFZoP0s_x": {"dated": 1754087032894, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}}, "Mo6GG7QnH1fs42t4JtxtoQavRpE2": {"-OWIVPJg1Uc_m9IHC4Pc": {"dated": 1753747662388, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OWIWoX1T9tno9e9__It": {"dated": 1753748031626, "msg": "Tienes una nueva solicitud de reserva", "title": "Tienes una notificación"}, "-OWIWr6qe5zkb5d8u4zU": {"dated": 1753748042238, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}, "-OWIWsMZ5HN14JzUidrs": {"dated": 1753748047342, "msg": "Conductor cerca de ti", "title": "Tienes una notificación"}, "-OWI_fkIWp70ms4LDiT7": {"dated": 1753749044249, "msg": "El conductor ha comenzado. Tu identificación de reserva esAHHJCO", "title": "Tienes una notificación"}, "-OWIa3S0uGO_M7Fo9SSi": {"dated": 1753749145416, "msg": "Has llegado al destino. Complete el pago.", "title": "Tienes una notificación"}, "-OWIa4WlkCffE9lt5fQG": {"dated": 1753749149816, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OWIa4YULyBwrOBjIDi4": {"dated": 1753749149926, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OWNnpBc6PsFwOe-3Trb": {"dated": 1753836639023, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}}, "j3NYf7YtOLNcJUTlhUAbzkUOdUM2": {"-OWQTiij6k-Q2rEiyvwd": {"dated": 1753881439161, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OWQUaxp5z3dH2wJA5Ql": {"dated": 1753881669499, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OWQWmfwE0xG0NaEoUVj": {"dated": 1753882241795, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OWR6uCoJtpIp3_igHLf": {"dated": 1753892234113, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}}, "pb9R4TC3Ocba5ivcYT5C1rlRG862": {"-OWIUWRietzoBi7wmdnI": {"dated": 1753747429172, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OWO-J8Z6a_yNGvrLTyv": {"dated": 1753839911532, "msg": "Recibiste una calificación de estrella 5", "title": "Tienes una notificación"}, "-OWS6a1z86BH9UAMqpYd": {"dated": 1753908928710, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OWS6dtIPQKaoxuCC894": {"dated": 1753908944474, "msg": "Recibiste una calificación de estrella 5", "title": "Tienes una notificación"}, "-OWS6eqK-XLgnxni-mUv": {"dated": 1753908948379, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OWS6frse-hzANtW9jzV": {"dated": 1753908952575, "msg": "Recibiste una calificación de estrella 5", "title": "Tienes una notificación"}}}, "userRatings": {"Mo6GG7QnH1fs42t4JtxtoQavRpE2": {"-OWO-IN0K0xirLQiAKOK": {"rate": 5, "user": "pb9R4TC3Ocba5ivcYT5C1rlRG862"}}, "j3NYf7YtOLNcJUTlhUAbzkUOdUM2": {"-OWS6dk_nDdvKoj6iirv": {"rate": 5, "user": "pb9R4TC3Ocba5ivcYT5C1rlRG862"}, "-OWS6fonYn5OzXJfvZIu": {"rate": 5, "user": "pb9R4TC3Ocba5ivcYT5C1rlRG862"}}}, "users": {"-OWLjWoHfzJNlUpu8f5-": {"city": "IZTACALCO", "email": "<EMAIL>", "estado": "Activo", "fecha_registro": "2025-07-29T15:12:36.626Z", "firstName": "FM CONSTRUCTORES", "lastName": ".", "mobile": "", "nombreComercial": "FM CONSTRUCTORES", "rfc": "FCO930413Q65"}, "-OWRQNQGDVolQkfioSxo": {"city": "OCOTLAN", "email": "<EMAIL>", "estado": "Activo", "fecha_registro": "2025-07-30T17:42:18.577Z", "firstName": "DESARROLLOS INMOBILIARIOS EL MANTE", "lastName": ".", "mobile": "", "nombreComercial": "DESARROLLOS INMOBILIARIOS EL MANTE", "rfc": "DIM100125450"}, "-OWS_ktOpX2PKVz-1t2_": {"approved": true, "createdAt": 1753916933382, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "mobile": "+523316011424", "usertype": "admin", "walletBalance": 0}, "-OWSeRH6x8kaAt-WxtfH": {"approved": false, "createdAt": 1753918063859, "currentRouteId": "-OWakG6PIrEdQUyVAyBW", "driverActiveStatus": false, "email": "<EMAIL>", "firstName": "JUAN FRANCISCO JAVIER", "lastAssignedRoute": 1754070585862, "lastName": "GIL JUAREZ", "mobile": "+523314344857", "pushToken": "", "queue": true, "referralId": "AI1P4", "signupViaReferral": " ", "term": true, "updatedAt": 1754064617929, "usertype": "driver", "walletBalance": 0}, "-OWXPHD6nUG8eyJO6k5U": {"city": "OTRA NO ESPECIFICADA EN EL CATALOGO", "email": "", "estado": "Activo", "fecha_registro": "2025-07-31T21:35:14.312Z", "firstName": "RAUL RAMIREZ RUEDA", "lastName": ".", "mobile": "", "nombreComercial": "RAUL RAMIREZ RUEDA", "rfc": "RARR841105EU7"}, "-OWaND17m3IWy_-cUIWh": {"city": "BAHIA DE BANDERAS", "email": "<EMAIL>", "estado": "Activo", "fecha_registro": "2025-08-01T16:04:41.736Z", "firstName": "STONE CASAS Y CONSTRUCCIONES", "lastName": ".", "mobile": "", "nombreComercial": "STONE CASAS Y CONSTRUCCIONES", "rfc": "SCC0401282S7"}, "-OWaNJOevm_PsmbY8n8m": {"email": "<EMAIL>", "estado": "Activo", "fecha_registro": "2025-08-01T16:05:07.827Z", "firstName": "IMPULSORA Y CONSTRUCTORA HS", "lastName": ".", "mobile": "+3225961017", "nombreComercial": "IMPULSORA Y CONSTRUCTORA HS", "rfc": "ICH080331F42"}, "-OWaNKrbNs0H6mhGHpOU": {"city": "NUEVO VALLARTA", "email": "<EMAIL>", "estado": "Activo", "fecha_registro": "2025-08-01T16:05:13.841Z", "firstName": "SCC ADMINISTRACION Y CONSTRUCCION", "lastName": ".", "mobile": "", "nombreComercial": "SCC ADMINISTRACION Y CONSTRUCCION", "rfc": "SAC060622EU0"}, "-OWaOYoXn__qr74Sl8oz": {"approved": false, "createdAt": 1754064633137, "driverActiveStatus": false, "email": "<EMAIL>", "firstName": "FRANCISCO JAVIER", "lastName": "ESPARZA HERNANDEZ", "mobile": "+523328354266", "pushToken": "", "queue": false, "referralId": "PSRHX", "signupViaReferral": " ", "term": true, "usertype": "driver", "walletBalance": 0}, "-OWaOgYboflCyAxml_wl": {"approved": false, "createdAt": 1754064668919, "driverActiveStatus": false, "email": "<EMAIL>", "firstName": "JONAS", "lastName": "CARRILLO DOMINGUEZ", "mobile": "+523316037479", "pushToken": "", "queue": false, "referralId": "UU5MJ", "signupViaReferral": " ", "term": true, "usertype": "driver", "walletBalance": 0}, "-OWaPiLEXXZTrXoS0Zhn": {"email": "<EMAIL>", "estado": "Activo", "fecha_registro": "2025-08-01T16:15:38.392Z", "firstName": "IMPULSORA Y CONSTRUCTORA HS", "lastName": ".", "mobile": "+3225961017", "nombreComercial": "IMPULSORA Y CONSTRUCTORA HS", "rfc": "ICH080331F42"}, "-OWaVs-2CiU4OQ0xpzcE": {"email": "<EMAIL>", "estado": "Activo", "fecha_registro": "2025-08-01T16:42:30.787Z", "firstName": "IMPULSORA Y CONSTRUCTORA HS", "lastName": ".", "mobile": "+3225961017", "nombreComercial": "IMPULSORA Y CONSTRUCTORA HS", "rfc": "ICH080331F42"}, "-OWa_VPIT4XcWnmcVwkC": {"estado": "Activo", "fecha_registro": "2025-08-01T17:02:44.883Z"}, "-OWae-bPbNufC7-CdQNd": {"email": "<EMAIL>", "estado": "Activo", "fecha_registro": "2025-08-01T17:22:25.370Z", "firstName": "IMPULSORA Y CONSTRUCTORA HS", "lastName": ".", "mobile": "+3225961017", "nombreComercial": "IMPULSORA Y CONSTRUCTORA HS", "rfc": "ICH080331F42"}, "-OWaf3QnCYTcmhCadqiW": {"city": "NUEVO VALLARTA", "email": "<EMAIL>", "estado": "Activo", "fecha_registro": "2025-08-01T17:27:03.162Z", "firstName": "SCC ADMINISTRACION Y CONSTRUCCION", "lastName": ".", "mobile": "", "nombreComercial": "SCC ADMINISTRACION Y CONSTRUCCION", "rfc": "SAC060622EU0"}, "-OWafMFZ67ttomBjW9r_": {"city": "BAHIA DE BANDERAS", "email": "<EMAIL>", "estado": "Activo", "fecha_registro": "2025-08-01T17:28:20.267Z", "firstName": "STONE CASAS Y CONSTRUCCIONES", "lastName": ".", "mobile": "", "nombreComercial": "STONE CASAS Y CONSTRUCCIONES", "rfc": "SCC0401282S7"}, "-OWaiUNXV1X_5GtnIpiZ": {"city": "CANCUN", "email": "", "estado": "Activo", "fecha_registro": "2025-08-01T17:41:59.977Z", "firstName": "CONSTRUCTORA MODUMAR", "lastName": ".", "mobile": "", "nombreComercial": "CONSTRUCTORA MODUMAR", "rfc": "CMO240524RF5"}, "-OWaxsnC1Htgw_CfMSR3": {"city": "PUERTO VALLARTA", "email": "", "estado": "Activo", "fecha_registro": "2025-08-01T18:49:16.237Z", "firstName": "PROTECCION INTEGRAL GUCODA", "lastName": ".", "mobile": "", "nombreComercial": "PROTECCION INTEGRAL GUCODA", "rfc": "PIG160926QS6"}, "admin0001": {"approved": true, "email": "<EMAIL>", "firstName": "Admin", "lastName": "Admin", "usertype": "admin"}, "customer1_id_example": {"savedAddresses": {"-OWbjVCLW2pcvGmmEzIM": {"count": 1, "description": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}}}, "j3NYf7YtOLNcJUTlhUAbzkUOdUM2": {"approved": true, "carApproved": true, "carType": "CAMIONETA", "car_image": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/cars%2F-OWQTZzGK18-kJkqXSnA?alt=media&token=f08cf84b-6cfa-40a7-af73-399146c37925", "createdAt": 1753881268436, "currentRouteId": "-OWbilPjyVYVCuCBMikl", "driverActiveStatus": false, "email": "<EMAIL>", "firstName": "Diego", "lastActive": 1754087029398, "lastAssignedRoute": 1754086971102, "lastName": "Ballesteros", "licenseImage": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/users%2Fj3NYf7YtOLNcJUTlhUAbzkUOdUM2%2Flicense?alt=media&token=9b390fb5-436b-49e2-89fd-ec483726991b", "licenseImageBack": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/users%2Fj3NYf7YtOLNcJUTlhUAbzkUOdUM2%2FlicenseBack?alt=media&token=eca90318-c46e-46fb-a75e-d8b04cccd4f3", "mobile": "+523313036516", "other_info": "", "pushToken": "ExponentPushToken[KGv5TdCx8WNHvVepJvPNMN]", "queue": false, "rating": "5.0", "referralId": "DQMTS", "signupViaReferral": " ", "term": true, "updateAt": 1753881398447, "userPlatform": "ANDROID", "usertype": "driver", "vehicleMake": "RAM 2500", "vehicleModel": "2025", "vehicleNumber": "ERP2025", "walletBalance": 50000}, "pb9R4TC3Ocba5ivcYT5C1rlRG862": {"approved": true, "createdAt": 1753747047421, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Ballesteros", "mobile": "+************", "pushToken": "ExponentPushToken[qJ5acWCKrVMR-mMlb7GlCr]", "referralId": "WTNYH", "savedAddresses": {"-OWIa3OE-LdVG_Hwc-F-": {"count": 2, "description": "Parroquia de San Juan Bosco, Calle Industria, San Juan Bosco, Guadalajara, Jalisco, Mexico", "lat": 20.6752755, "lng": -103.3143675}, "-OWQWltM9hzqgqL9CRfn": {"count": 1, "description": "Centro Joyero, Guadalajara, Jalisco", "lat": 20.6771497, "lng": -103.3390651}, "-OWR6t6gffEDaT_2asKJ": {"count": 1, "description": "Escuela Primaria Urbana 181 Atala Apodaca, Calle Castellanos y Tapia, Santa María, Guadalajara, Jal., México", "lat": 20.6856157, "lng": -103.3111471}, "-OWROjen6V_JeGaKW3ih": {"count": 2, "description": "Av. Américas 1500, Providencia, 44630 Guadalajara, Jal.", "lat": 20.6668, "lng": -103.3918}, "-OWRXaD8s_uRf3zayOx5": {"count": 3, "description": "Av. <PERSON> 1234, Guadalajara, Jalisco", "lat": 20.674917, "lng": -103.3636746}, "-OWRil7aRMXFgpLlu0gk": {"count": 5, "description": "Av. <PERSON> 2375, Jardines del Country, Guadalajara, Jalisco", "lat": 20.6524042, "lng": -103.4014461}}, "signupViaReferral": " ", "term": true, "userPlatform": "ANDROID", "usertype": "customer", "walletBalance": 50000}, "tASwzLMHn4RA1pAZ1Uurl6zywmu1": {"approved": true, "createdAt": 1753916585245, "driverActiveStatus": false, "email": "<EMAIL>", "firstName": "<EMAIL>", "lastName": "<EMAIL>", "licenseImage": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/users%2FtASwzLMHn4RA1pAZ1Uurl6zywmu1%2Flicense?alt=media&token=4165b08a-9db3-44fb-9055-24fdca014a2c", "licenseImageBack": "https://firebasestorage.googleapis.com/v0/b/balle-813e3.firebasestorage.app/o/users%2FtASwzLMHn4RA1pAZ1Uurl6zywmu1%2FlicenseBack?alt=media&token=c8b5be2f-db2d-4627-b9a7-a386d5d81365", "mobile": "+523316011427", "queue": false, "referralId": "VVEVG", "signupViaReferral": " ", "term": true, "userPlatform": "ANDROID", "usertype": "driver", "walletBalance": 0}}, "walletHistory": {"Mo6GG7QnH1fs42t4JtxtoQavRpE2": {"-OWIVPGhB8SgbMn7pmmq": {"amount": "50000", "date": 1753747662188, "transaction_id": "1753747658738", "txRef": "wallet-Mo6GG7QnH1fs42t4JtxtoQavRpE2-JPJD", "type": "Credit"}, "-OWNnnp5W9cJixjcpMUD": {"amount": 50000, "date": 1753836632101, "txRef": "AdminCredit", "type": "Credit"}}, "j3NYf7YtOLNcJUTlhUAbzkUOdUM2": {"-OWQTihyzw2BQoOh4fTy": {"amount": "50000", "date": 1753881439104, "transaction_id": "1753881431783", "txRef": "wallet-j3NYf7YtOLNcJUTlhUAbzkUOdUM2-IAGC", "type": "Credit"}}, "pb9R4TC3Ocba5ivcYT5C1rlRG862": {"-OWIUWRCyUqerFtZi2q8": {"amount": "50000", "date": 1753747429130, "transaction_id": "1753747421990", "txRef": "wallet-pb9R4TC3Ocba5ivcYT5C1rlRG862-ZDET", "type": "Credit"}}}}