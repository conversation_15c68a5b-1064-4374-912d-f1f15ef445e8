{"bookings": {"-OL-wqfI7mD5dVrPFnOA": {"bookLater": true, "bookingDate": 1741625191219, "booking_from_web": true, "booking_type_admin": true, "carImage": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cartypes%2Ftype1?alt=media&token=164e9ddb-4017-43c8-95ea-a218d55dfcda", "carType": "AUTO EJECUTIVO", "car_image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cars%2F-OKser6Z_1YanSXXhoAA?alt=media&token=b8c94268-c47a-40cc-91ce-baa07cc3e42f", "cardPaymentAmount": 0, "cashPaymentAmount": "17.36", "commission_rate": 15, "commission_type": "percentage", "convenience_fees": "2.26", "coords": [{"latitude": 20.6856157, "longitude": -103.3111471}, {"latitude": 20.6854854, "longitude": -103.3066325}], "customer": "TEPe0cfPgBSk1OWAdW88ntTExLp1", "customer_contact": "+523311917230", "customer_email": "<EMAIL>", "customer_image": "", "customer_name": "<PERSON>", "customer_paid": "17.36", "customer_token": " ", "deliver_image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/bookings%2F-OL-wqfI7mD5dVrPFnOA%2Fdeliver_image?alt=media&token=b0e3c10b-b318-4277-8586-19d274dd4a53", "deliveryInstructions": "", "deliveryWithBid": false, "discount": "0.00", "distance": "0.96", "driver": "UlGIEKCBAaaUZLD6EdE4DRRIK6R2", "driverDeviceId": "id1741611951715", "driverRating": 5, "driver_contact": "+523311917231", "driver_image": "", "driver_name": "<PERSON>", "driver_share": "15.10", "driver_token": "ExponentPushToken[0A3ro-BfJ3PHA7fHjqneWA]", "drop": {"add": "Sta. Clem<PERSON> 1935, San Martin, 44710 Guadalajara, Jal., México", "lat": 20.6854854, "lng": -103.3066325}, "dropAddress": "Sta. Clem<PERSON> 1935, San Martin, 44710 Guadalajara, Jal., México", "endTime": 1741626356668, "estimate": "17.36", "estimateDistance": "0.96", "estimateTime": 216, "fleetCommission": "0", "fleet_admin_comission": 5, "fleetadmin": "", "id": "-OL-wqfI7mD5dVrPFnOA", "optionIndex": 0, "otherPerson": "", "otherPersonPhone": "", "otp": false, "parcelTypeIndex": 0, "payableAmount": "17.36", "payment_mode": "cash", "pickUpInstructions": "", "pickup": {"add": "Escuela Primaria Urbana 181 Atala Apodaca, Calle Castellanos y Tapia, Santa María, Guadalajara, Jal., México", "lat": 20.6856157, "lng": -103.3111471}, "pickupAddress": "Escuela Primaria Urbana 181 Atala Apodaca, Calle Castellanos y Tapia, Santa María, Guadalajara, Jal., México", "pickup_image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/bookings%2F-OL-wqfI7mD5dVrPFnOA%2Fpickup_image?alt=media&token=f8d49642-d5a3-496a-b017-f5fe17d1c236", "promo_applied": false, "reference": "ZNZRBT", "startTime": 1741625589736, "status": "COMPLETE", "tableData": {"id": 0}, "total_trip_time": 767, "trip_cost": "17.36", "trip_end_time": "11:5:56", "trip_start_time": "10:53:9", "tripdate": 1741628460000, "usedWalletMoney": 0, "vehicleMake": "Nissan", "vehicleModel": "Versa", "vehicle_number": "eydg2638"}, "-OL06mnlviTeVjzLa7Re": {"bookLater": true, "bookingDate": 1741628058793, "booking_from_web": false, "booking_type_admin": false, "carImage": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cartypes%2Ftype1?alt=media&token=164e9ddb-4017-43c8-95ea-a218d55dfcda", "carType": "AUTO EJECUTIVO", "car_image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cars%2F-OKser6Z_1YanSXXhoAA?alt=media&token=b8c94268-c47a-40cc-91ce-baa07cc3e42f", "cardPaymentAmount": 0, "cashPaymentAmount": 0, "commission_rate": 15, "commission_type": "percentage", "convenience_fees": "11.42", "coords": [{"latitude": 20.7603303, "longitude": -103.3308786}, {"latitude": 20.6837515, "longitude": -103.3223714}], "customer": "TEPe0cfPgBSk1OWAdW88ntTExLp1", "customer_contact": "+523311917230", "customer_email": "<EMAIL>", "customer_image": "", "customer_name": "<PERSON>", "customer_paid": "87.55", "customer_token": "ExponentPushToken[0A3ro-BfJ3PHA7fHjqneWA]", "deliveryInstructions": "", "deliveryWithBid": false, "discount": "0.00", "distance": "12.69", "driver": "UlGIEKCBAaaUZLD6EdE4DRRIK6R2", "driverDeviceId": "id1741627158980", "driverRating": "5.0", "driver_arrive_time": "1741631983492", "driver_contact": "+523311917231", "driver_image": "", "driver_name": "<PERSON>", "driver_share": "76.13", "driver_token": "ExponentPushToken[8EOjFICLxKI7LIshz9O9ej]", "drop": {"add": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Guadalajara, Jalisco, Mexico", "lat": 20.6837515, "lng": -103.3223714}, "dropAddress": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Guadalajara, Jalisco, Mexico", "endTime": 1741632598814, "estimate": "87.55", "estimateDistance": "12.69", "estimateTime": 1917, "feedback": "", "fleetCommission": "0", "fleet_admin_comission": 5, "fleetadmin": "", "id": "-OL06mnlviTeVjzLa7Re", "optionIndex": 0, "otherPerson": "", "otherPersonPhone": "", "otp": false, "parcelTypeIndex": 0, "payableAmount": "87.55", "payment_mode": "wallet", "pickUpInstructions": "", "pickup": {"add": "Vista Al Oriente, La Coronilla, Zapopan, Jalisco, Mexico", "lat": 20.7603303, "lng": -103.3308786}, "pickupAddress": "Vista Al Oriente, La Coronilla, Zapopan, Jalisco, Mexico", "pickup_image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/bookings%2F-OL06mnlviTeVjzLa7Re%2Fpickup_image?alt=media&token=4d374cf8-46b1-4a4c-8b7c-91ee2bbbb868", "prepaid": true, "promo_applied": false, "rating": 5, "reference": "KTGORQ", "startTime": 1741632402555, "status": "COMPLETE", "tableData": {"id": 0}, "total_trip_time": 196, "trip_cost": "87.55", "trip_end_time": "12:49:58", "trip_start_time": "12:46:42", "tripdate": 1741632000000, "usedWalletMoney": "87.55", "vehicleMake": "Nissan", "vehicleModel": "Versa", "vehicle_number": "eydg2638"}, "-OMHVq_VAu-E5k1Bv5ES": {"aerolinea": "LATAM", "aeropuero": true, "cliente": {"contacto": "987654321", "id": "cliente123"}, "conductor": {"id": "conductor456"}, "createdAt": 1742993582631, "destino": {"direccion": "Hotel Marriott Miraflores"}, "formaPago": "efectivo", "horaLlegada": "2025-03-27T10:00:00Z", "importeCobrado": 150, "nombrePasajero": "<PERSON>", "numeroPasajero": 1234567890, "numeroVoucher": "VOU001", "numeroVuelo": 123, "origen": {"direccion": "Aeropuert<PERSON>"}, "status": "PENDING", "tipoPago": "contado"}}, "cancel_reason": [{"label": "Incapaz de contactar con el conductor", "tableData": {"id": 0}, "value": 0}, {"label": "El vehículo no se mueve en mi dirección.", "tableData": {"id": 1}, "value": 1}, {"label": "Mi motivo no está en la lista", "tableData": {"id": 2}, "value": 2}, {"label": "Conductor a negado servicio", "tableData": {"id": 3}, "value": 3}, {"label": "El conductor está tomando mucho tiempo", "value": 4}], "cars": {"-OKser6Z_1YanSXXhoAA": {"active": true, "approved": true, "carType": "AUTO EJECUTIVO", "car_image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cars%2F-OKser6Z_1YanSXXhoAA?alt=media&token=b8c94268-c47a-40cc-91ce-baa07cc3e42f", "createdAt": 1741486255102, "driver": "UlGIEKCBAaaUZLD6EdE4DRRIK6R2", "other_info": "<PERSON><PERSON> os<PERSON>ro ", "vehicleMake": "Nissan", "vehicleModel": "Versa", "vehicleNumber": "eydg2638"}, "-OL2EhvjkpojiHg7rPaR": {"active": true, "approved": true, "carType": "AUTO EJECUTIVO", "car_image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cars%2F-OL2EhvjkpojiHg7rPaR?alt=media&token=a6eb45a6-bc34-414a-b0a0-77d01221be6d", "createdAt": 1741663688961, "driver": "r5Pd7zVyTLU0wxzkIPX1ASwW1qy2", "other_info": "Color gris oscuro ", "vehicleMake": "Nissan ", "vehicleModel": "Versa", "vehicleNumber": "EYS4627"}}, "cartypes": {"type1": {"base_fare": 10, "cancelSlab": [{"amount": 10, "minsDelayed": 2}, {"amount": 15, "minsDelayed": 4}], "convenience_fee_type": "percentage", "convenience_fees": 15, "extra_info": "4 personas", "fleet_admin_fee": 5, "id": "type1", "image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cartypes%2Ftype1?alt=media&token=164e9ddb-4017-43c8-95ea-a218d55dfcda", "min_fare": 10, "name": "AUTO EJECUTIVO", "pos": 1, "rate_per_hour": 5, "rate_per_unit_distance": 5}, "type2": {"base_fare": 12, "cancelSlab": [{"amount": 15, "minsDelayed": 2}, {"amount": 20, "minsDelayed": 4}], "convenience_fee_type": "percentage", "convenience_fees": 15, "extra_info": "VAN", "fleet_admin_fee": 10, "id": "type2", "image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cartypes%2Ftype2?alt=media&token=90802082-e1a7-46c3-844d-097fe289b2cc", "min_fare": 20, "name": "MINI VAN", "options": [{"amount": 100, "description": "About 10 kg to 50 kg"}], "parcelTypes": [{"amount": 10, "description": "Box"}, {"amount": 20, "description": "Fragile or Glass"}], "pos": 2, "rate_per_hour": 6, "rate_per_unit_distance": 8, "tableData": {"id": 1}}}, "drivers": {"-OMFHZbGhDdMD0EfijsH": {"email": "<EMAIL>", "estado": "Activo", "fecha_registro": "2025-03-26T02:31:24.369Z", "licencia": {"fechaVencimiento": "2025-12-31", "numero": "L123456", "tipo": "A-IIIa"}, "nombre": "Conductor <PERSON><PERSON><PERSON>", "telefono": "+51999888666", "vehiculo": {"año": "2022", "marca": "Toyota", "modelo": "<PERSON><PERSON><PERSON>", "placa": "ABC-123"}}, "-OMFIdWFdDCOZnR0OAoG": {"email": "<EMAIL>", "estado": "Activo", "fecha_registro": "2025-03-26T02:36:06.619Z", "licencia": {"fechaVencimiento": "2025-12-31", "numero": "L123456", "tipo": "A-IIIa"}, "nombre": "Conductor <PERSON><PERSON><PERSON>", "telefono": "+51999888666", "vehiculo": {"año": "2022", "marca": "Toyota", "modelo": "<PERSON><PERSON><PERSON>", "placa": "ABC-123"}}}, "languages": {"-OKql6ZINQn17HeBHeqn": {"createdAt": 1741452064323, "dateLocale": "es-mx", "default": true, "id": "-OKql6ZINQn17HeBHeqn", "keyValuePairs": {"ACCEPTED": "ACEPTADO", "ARRIVED": "LLEGÓ", "AppName": "Nombre de la aplicación", "AppleStoreLink": "Enlace de Apple Store", "Balance": "Balance", "CANCELLED": "CANCELADO", "COMPLETE": "COMPLETO", "CardPaymentAmount": "<PERSON><PERSON> por tarjeta -", "CashPaymentAmount": "Pagado por efectivo -", "CompanyName": "nombre de empresa", "CompanyWebsite": "Sitio web de la empresa", "Customer_paid": "Cliente pagado", "Discounts": "Descuentos", "FacebookHandle": "Enlace de la página de Facebook", "Gross_trip_cost": "Costo de viaje bruto", "InstagramHandle": "Enlace de la página de Instagram", "NEW": "NUEVO", "PAID": "PAGADO", "PAYMENT_PENDING": "Pago pendiente", "PENDING": "PENDIENTE", "PlayStoreLink": "Play Store Link", "Profit": "Ganancia", "REACHED": "ALCANZÓ", "STARTED": "COMENZÓ", "TwitterHandle": "Enlace de la página de Twitter", "WalletPayment": "<PERSON><PERSON> por billetera", "Withdraw_title": "<PERSON><PERSON><PERSON>", "about_us": "Sobre nosotros", "about_us_content1": "Somos la plataforma de movilidad más grande y una de las mayores proveedores de servicios en línea en línea del mundo.", "about_us_content2": "Administre reservas, solicite cotizaciones o reserve un servicio en línea con nuestro sistema de reservas en línea simple y rápido. Somos una compañía de servicios a pedido que permite a los huéspedes reservar fácilmente varios servicios en línea. Ofrecemos los mejores servicios del país.", "about_us_menu": "Sobre nosotros", "accept": "ACEPTAR", "accept_booking_request": "aceptó su solicitud de reserva.", "accepted_booking": "Tu reserva es aceptada", "account_approve": "Cuenta aprobada", "account_create_successfully": "Cuenta creada con éxito", "actions": "Comportamiento", "active_booking": "Reserva activa", "active_car": "Coche activo", "active_car_delete": "El auto activo no se puede eliminar.", "active_driver": "Conductores activos", "active_status": "Estado activo", "add": "AGREGAR", "addMoneyTextInputPlaceholder": "Agregar cantidad", "add_admin": "Agregar administrador", "add_admin_title": "Agregar administrador", "add_booking_title": "Agregar reservas", "add_car": "Agregar coche", "add_carType": "Agregar tipo de vehículo", "add_carType_title": "Agregar tipo de coche", "add_car_title": "Agregar coche", "add_cartype_title": "Agregar ve<PERSON>", "add_customer": "Agregar cliente", "add_customer_title": "Agregar cliente", "add_driver": "Agregar conductor", "add_driver_title": "Agregar conductor", "add_fleetadmin": "Agregar administrador de flota", "add_fleetadmin_title": "Agregar administrador de flota", "add_language": "Editar idioma", "add_money": "Agregar dinero", "add_notification": "Agregar notificaciones", "add_notification_title": "Agregar notificación", "add_promo_title": "Agregar promoción", "add_to_review": "Agregar a la revisión", "add_to_wallet": "Agregar a la billetera", "add_to_wallet_title": "Agregar a la billetera", "addbookinglable": "Agregar reservas", "admin": "Administración", "admin_contact": "Póngase en contacto con su administrador para aprobar la cuenta", "advance_settings": "Configuración avanzada", "alert": "<PERSON><PERSON><PERSON>", "alert_text": "<PERSON><PERSON><PERSON>", "all": "Todo", "alladmin": "Todos los administradores", "alladmins_title": "Todos los administradores", "allow_del_final_img": "Permitir una imagen de caída de entrega", "allow_del_pkp_img": "Permit<PERSON> de selección de entrega", "allow_location": "Permitir ubicación para el mapa en tiempo real", "allow_multi_country": "Permitir la selección de múltiples países", "allow_only": "Necesidades de ubicación mientras usa la aplicación", "always_on": "La ubicación necesita siempre en", "amount": "Cantidad", "amount_must_be_gereater_than_100": "Porque la cantidad de slickpay debe ser mayor que 100", "android": "<PERSON><PERSON>", "apiUrl": "URL API", "app_info": "Información de la aplicación", "app_info_title": "Información de la aplicación", "app_link": "Enlace de la aplicación:", "app_store_deception": "Su negocio debe estar en manos seguras en todo momento. Nos aseguramos de que nunca se quede sin clientes y no se ejecute con pérdida. Más de 500 empresas nos confiaron para ofrecer campañas de marketing de calidad utilizando marketing digital", "app_store_deception1": "La aplicación y para el cliente y el conductor, no para el inicio de sesión del administrador. Tome 2 teléfonos e instale en cada uno. Registre a un cliente en uno y registre un conductor en uno. Para el conductor, siempre seleccione la ubicación Siempre permita iniciar sesión. <PERSON>ego, creando una reserva de un teléfono y recibiendo en el otro dónde inició sesión el conductor. Use el inicio de sesión de usuario proporcionado en el enlace web para ver el lado administrativo de las cosas.", "apple_signin_error": "Apple Signin no está configurado en desarrollador.appple.com o ya ha utilizado el mismo correo electrónico para registrarse.", "apply": "APLICAR", "apply_promo": "Aplicar promoción", "approve_status": "Aprobar el estado", "approved": "Aprobado", "assign": "ASIGNAR", "assign_driver": "<PERSON><PERSON><PERSON> conductor", "auth_error": "Error de autenticación: verifique sus credenciales ingresadas", "authorization": "Autorización", "auto_dispatch": "Envío automá<PERSON>o", "back": "Volver a iniciar sesión", "bankAccount": "Cuenta bancaria", "bankCode": "<PERSON><PERSON><PERSON> ban<PERSON>", "bankDetails": "<PERSON><PERSON> ban<PERSON>", "bankName": "Nombre del banco", "bank_fields": "Campos regulares bancarios", "base_fare": "Tarifa base", "base_fare_required": "Elija la tarifa base", "best_experience": "Mejor experiencia", "best_service_provider": "El mejor servicio proporcionado", "best_services": "Mejores servicios", "bid": "Licitación", "bill_details": "Detalles de la factura", "bill_details_title": "Detalles de la factura", "blank_message": "No hay registros para mostrar", "body": "<PERSON><PERSON><PERSON>", "book": "Libro", "book_later": "<PERSON><PERSON><PERSON> mas tarde", "book_later_button": "<PERSON><PERSON><PERSON> mas tarde", "book_now": "<PERSON><PERSON><PERSON> ahora", "book_now_button": "<PERSON><PERSON><PERSON> ahora", "book_ride": "Reserve su viaje", "book_your_ride_menu": "Hacer una reserva", "book_your_title": "Reserve su servicio", "booked_cab_title": "Gestionar la reserva", "bookingPayment": "Pago de reserva", "booking_cancelled": "La reserva se cancela. IDENTIFICACIÓN :", "booking_chart": "Tabla de reserva", "booking_confirm": "Tu reserva ha sido confirmada", "booking_count": "Recuento de reservas", "booking_date": "<PERSON><PERSON> de reserva", "booking_date_time": "Tiempo de datos de reserva", "booking_details": "Detalles de la reserva", "booking_flow": "Flujo de reserva", "booking_history": "Historial de reservas", "booking_id": "ID de reserva", "booking_is": "Tu reserva es", "booking_ref": "Referencia de reserva", "booking_request": "Solicitudes de reserva", "booking_status": "Estado de reserva", "booking_status_web": "Estado de reserva", "booking_success": "Reserva exitosa. ID de reserva:", "booking_successful": "Reserva exitosa", "booking_title": "Mis reservas", "booking_type": "Tipo de reserva", "bookings_table": "Mesa de reservas", "bookings_table_title": "Mesa de reservas", "camera": "<PERSON><PERSON><PERSON>", "camera_permission_error": "Error de permiso de <PERSON>ámara", "cancel": "CANCELAR", "cancelSlab": "Losas de cancelación", "cancel_booking": "Cancelar la reserva", "cancel_confirm": "¿Quieres cancelar realmente?", "cancel_messege1": "Tu trabajo con la identificación de reserva", "cancel_messege2": "ha sido cancelado con éxito", "cancel_reason_modal_title": "Razón", "cancel_ride": "Cancelar la reserva", "cancellationFee": "Tarifa de cancelación", "cancellation_reason": "Razón de cancelación", "cancellation_reasons": "Razones de cancelación", "cancellation_reasons_title": "Razón de cancelación", "cancelled_bookings": "Cancelar la reserva", "cancelled_incomplete_booking": "Reserva incompleta cancelada", "carApproved_by_admin": "CAR activo no aprobado por Admin.", "carType_required": "Se requiere coche", "car_add": "Agregar coche para aceptar la reserva", "car_approval": "Aprobación de coche", "car_details_title": "Detalles del vehículo", "car_horn_repeat": "Repita el sonido en el nuevo viaje", "car_image": "Imagen de coche", "car_no_not_found": "Número de vehículo no asignado", "car_type": "Tipo de vehículo", "car_type_blank_error": "Seleccione el tipo de vehículo", "car_type_title": "Tipo de vehículo", "car_view_horizontal": "Vista de la lista de autos horizontal", "card": "Tarjeta", "card_payment_amount": "Monto del pago de la tarjeta", "cars": "Coches", "cars_title": "Coches", "cash": "<PERSON><PERSON>", "cash_booking_false": "El administrador no puede crear una reserva cuando el pago en efectivo se desactiva en el sistema.", "cash_on_delivery": "Efectivo en la entrega", "cash_payment_amount": "Monto de pago en efectivo", "chat_blank": "Por favor escriba algo ...", "chat_input_title": "Escriba algo agradable ...", "chat_not_found": "No se encuentra la historia de chat", "chat_requested": ": <PERSON><PERSON><PERSON>", "chat_title": "<PERSON><PERSON><PERSON>", "check_approve_status": "Verifique el estado de aprobación", "check_email": "Revise su bandeja de entrada de correo electrónico", "check_mobile": "Marque su casilla de masaje", "choose_image_first": "Elija la imagen primero.", "close": "CERCA", "code": "Código", "code_already_avilable": "Esta promoción ya disponible", "code_colon": "Código:", "company_phone": "Teléfono de la compañía", "complain": "<PERSON><PERSON><PERSON>", "complain_date": "<PERSON><PERSON>", "complain_title": "QUEJARSE", "complete_payment": "<PERSON>go completo", "complete_ride": "Trabajo completo", "completed_bookings": "Reserva completa", "confirm": "CONFIRMAR", "confirm_booking": "Confirmar la reserva", "confirm_password": "<PERSON><PERSON>", "confirm_password_not_match_err": "Confirmar que la contraseña no coincida", "contact": "Contacto", "contact_email": "Correo electrónico de contacto", "contact_input_error": "Ingrese un correo electrónico o número de teléfono móvil válido.", "contact_placeholder": "Número de teléfono móvil o correo electrónico", "contact_us": "Cont<PERSON><PERSON><PERSON>s", "contentType": "Tipo de contenido", "convenience_fee": "Tarifas de conveniencia", "convenience_fee_required": "Elija tarifas de conveniencia", "convenience_fee_type": "Tipo de tarifa de conveniencia", "convenience_fee_type_required": "Elija el tipo de tarifa de conveniencia", "convert_button": "Convertir", "convert_to_driver": "Convertirse en conductor", "convert_to_mile": "Convertir a milla", "convert_to_rider": "Convertirse en cliente", "cost": "COSTO", "country_1": "<PERSON><PERSON>", "country_blank_error": "Seleccione el país", "country_restriction": "Restricción de país de autocompletado", "create_driver": "<PERSON><PERSON><PERSON> conductor", "create_new_user": "Creando un nuevo usuario, espere ...", "create_rider": "Crear cliente", "createdAt": "<PERSON><PERSON><PERSON> fecha", "credited": "Acreditado", "currency_code": "Código de divisas", "currency_settings": "Configuración de divisas", "currency_symbol": "Símbolo de moneda", "customMobileOTP": "Habilitar OTP móvil personalizado", "customer": "Cliente", "customer_contact": "Contacto con el cliente", "customer_email": "Correo electrónico del cliente", "customer_id": "ID de cliente", "customer_info": "Información del cliente", "customer_name": "Nombre del cliente", "daily": "A DIARIO", "dashboard_text": "Panel", "date": "<PERSON><PERSON>", "dateLocale": "Fecha local", "date_time": "<PERSON><PERSON>", "debited": "Debitado", "delete": "Bo<PERSON>r", "delete_account_lebel": "Eliminar cuenta", "delete_account_modal_subtitle": "¿Quieres eliminar tu cuenta?", "delete_account_modal_title": "Confirmación", "delete_account_msg": "El usuario puede eliminar todos sus datos del sistema eliminando la cuenta de la página de perfil en la aplicación móvil.", "delete_account_para1": "Todos sus datos se purgarán desde el sistema. Su imagen de perfil, correo electrónico, número de teléfono, inicios de sesión sociales, incluido el inicio de sesión de Google y todo el historial de reservas, todo se eliminará permanentemente.", "delete_account_para2": "Los datos y la cuenta eliminados del usuario son irrecuperables.", "delete_account_subheading": "Una vez que elimine su cuenta:", "delete_message": "¿Estás seguro de que quieres eliminar esta fila?", "delete_your_car": "¿Quieres eliminar tu coche?", "deliveryDetailMissing": "Por favor ingrese el nombre de la persona receptora", "deliveryInstructions": "Aprobador", "deliveryPerson": "Nombre de la persona receptora", "deliveryPersonPhone": "Recibir persona por teléfono no", "delivery_information": "Información de reserva", "demo_mode": "Restringido en la aplicación de demostración.", "description": "Descripción", "device_id": "ID de dispositivo", "device_type": "Tipo de dispositivo", "disable_cash": "Deshabilitar los pagos en efectivo", "disable_online": "Deshabilitar los pagos en línea", "disclaimer": "Descargo de responsabilidad", "disclaimer_text": "Esta aplicación recopila datos de ubicación para habilitar la 'ubicación del vehículo de búsqueda', 'ruta de viaje y navegación', 'movimiento del vehículo en tiempo real', incluso cuando la aplicación está cerrada o no en uso.", "discount": "Descuento", "discount_ammount": "Cantidad de descuento", "distance": "DISTANCIA", "distance_web": "Distancia", "documents": "Documentos", "documents_title": "Documentos", "done": "HECHO", "dont_cancel_text": "No canceles", "drag_map": "Rasta el mapa para seleccionar la dirección", "driver": "Conductor", "driverRadius": "Radio del conductor", "driver_accept_low_cost_capacity_booking": "El conductor acepta la reserva de bajo costo/capacidad", "driver_active": "Estado activo del conductor", "driver_active_staus_choose": "Estado activo del conductor <PERSON><PERSON>", "driver_assign_messege": "El conductor se asignará pronto.", "driver_cancelled_booking": "Reserva cancelada del conductor", "driver_completed_ride": "Has llegado al destino. Complete el pago.", "driver_contact": "<PERSON><PERSON>", "driver_distance": "El conductor ll<PERSON><PERSON><PERSON> a", "driver_earning": "Historial de ganancias del conductor", "driver_earning_title": "Historial de ganancias del conductor", "driver_email": "Correo electr<PERSON><PERSON> del conductor", "driver_finding_alert": "Encontrar conductores para ti", "driver_id": "ID de conductor", "driver_info": "Información del conductor", "driver_journey_msg": "El conductor ha comenzado. Tu identificación de reserva es", "driver_name": "Nombre del conductor", "driver_near": "Conductor cerca de ti", "driver_required": "Conductor requerido", "driver_setting": "Configuración del conductor", "driver_share": "Compartir el <PERSON>", "driver_threshold": "Umbral del conductor", "drivers": "Conductores", "drivers_title": "Conductores", "driving_license_back": "Licencia de conducir de regreso", "driving_license_font": "Fuente de licencia de conducir", "drop_address": "Dirección", "drop_address_from_map": "Seleccione la dirección de soltar desde el mapa", "drop_location": "Ubicación de destino", "drop_location_blank_error": "Seleccionar ubicación de destino", "earning_amount": "Cantidad de ganancias", "earning_reports": "Informes de ganancias", "earning_reports_title": "Informes de ganancias", "edit": "<PERSON><PERSON>", "edit_json": "<PERSON><PERSON>", "editcar": "Editar coche", "email": "Correo electrónico", "email_id": "Ingrese  correo electrónico", "email_login": "Inicio de sesión de correo electrónico", "email_msg": "Esperamos que hayas disfrutado tu viaje con nosotros.", "email_or_mobile_issue": "Verifique su identificación de correo electrónico o número de teléfono móvil. Nota: El número de móvil debe estar en formato internacional, p.  9199998888877", "email_placeholder": "Correo electrónico", "email_send": "Envío de correo electrónico", "email_use": "Modo de autenticación de correo electrónico", "emergency": "Emergencia", "end_date": "Fecha final", "end_time": "Tiempo de finalización", "enter_code": "Ingrese el código", "estimate_fare_text": "Esta es solo su tarifa estimada", "export": "Exportar", "extra_info": "Información adicional", "facebook_login_auth_error": "Error de inicio de sesión de Facebook:", "feedback": "Comentario", "fill_email_first": "Agregue el correo electrónico primero y luego tome la imagen del documento", "first_admin_deleted": "El primer administrador no se puede eliminar", "first_name": "Nombre de pila", "first_name_placeholder": "Nombre de pila", "first_page_tooltip": "Primera página", "firstname": "Nombre de pila", "fix": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat": "Departamento", "flat_minorder_maxorder": "En el tipo de promoción de plano, su valor de pedido mínimo no puede exceder el descuento máximo.", "fleet_admin_comission": "Comisión Fleetadmin", "fleet_admin_fee": "Tarifa de administración de la flota", "fleet_admin_fee_required": "Se requiere tarifa de administración de la flota", "fleetadmin_earning_reports": "<PERSON><PERSON><PERSON> G<PERSON>", "fleetadmin_id": "Identificación de Fleetadmin", "fleetadmin_name": "Nombre de flota", "fleetadmins": "Administradores de la flota", "fleetadmins_title": "Administradores de la flota", "follow_facebook": "Síguenos en Facebook", "follow_instagram": "Síguenos en Instagram", "follow_twitter": "Síguenos en Twitter", "for_other_person": "Reserva para otra persona", "force_end": "Force End", "forgot_password": "¿Has olvidado tu contraseña?", "fromEmail": "Desde el correo electrónico", "general_settings": "Configuración general", "general_settings_title": "Configuración general", "get_estimate": "Obtener una estimación", "get_started": "Empezar", "go_back": "Volver", "go_online_to_accepting_jobs": "Empiece a aceptar trabajos.", "go_to_booking": "Ir a la reserva", "go_to_home": "Ir a casa", "google_places_error": "ID de lugar al error de ubicación", "goto_home": "GOTO a casa", "gross_earning": "Ganancias totales", "hidden_demo": "Oculto para la demostración", "home": "<PERSON><PERSON>o", "host": "<PERSON><PERSON><PERSON><PERSON>", "hours": "<PERSON><PERSON>", "how_your_trip": "¿Cómo es tu viaje?", "id": "IDENTIFICACIÓN", "ignore_job_title": "¿Quieres ignorar este trabajo?", "ignore_text": "IGNORAR", "image": "Imagen", "image_size_warning": "(Tamaño de la imagen: Max 2 MB)", "image_upload_error": "Error de carga de imágenes", "in_demo_mobile_login": "El inicio de sesión móvil está desactivado en modo de demostración", "incomeText": "Mis ganancias", "incomplete_user": "El perfil de usuario está incompleto.", "info": "Información", "ios": "iOS", "km": "km", "landing_slogan": "Mejores servicios <PERSON>", "lang": "Idioma", "lang1": "Idioma:", "langLocale": "Roce de idiomas", "langName": "Nombre del idioma", "language": "Idiomas", "language_cap": "Idiomas", "last_name": "Apellido", "last_name_placeholder": "Apellido", "last_page_tooltip": "Última página", "lastname": "Apellido", "license_image": "Imagen de licencia", "license_image_back": "La imagen de la licencia retrocede", "license_image_front": "Frente de la imagen de la licencia", "license_image_required": "Imagen de licencia requerida", "loading": "Cargando...", "locationServiveBody": "La ubicación de fondo se está ejecutando ...", "locationServiveTitle": "Actualización de ubicación", "location_fetch_error": "Error de búsqueda de ubicación", "location_permission_error": "Error de permiso de ubicación", "login": "Acceso", "login_error": "Error de autenticación", "login_otp": "Inicie sesión con OTP", "login_settings": "Configuración de inicio de sesión", "login_signup": "Iniciar <PERSON> / Registrarse", "logout": "Cierre de sesión", "make_active": "Activar", "make_changes_to_update": "Hacer cambios en la actualización", "make_default": "<PERSON><PERSON><PERSON> predeterminado", "map_screen_drop_input_text": "<PERSON><PERSON>", "map_screen_where_input_text": "Origen", "marker_title_1": "Levantar", "marker_title_2": "<PERSON><PERSON>", "marker_title_3": "Parada", "max_limit": "Descuento máximo permitido", "max_promo_discount_value_error": "Valor de descuento de promoción de relleno máximo", "medialibrary": "Biblioteca de medios", "message": "Escribe tu mensaje", "message_text": "Men<PERSON><PERSON>", "method": "<PERSON><PERSON><PERSON><PERSON>", "mile": "mi", "min_fare": "<PERSON><PERSON><PERSON>", "min_fare_required": "<PERSON>ja una tarifa mínima", "min_limit": "Valor de pedido mínimo", "min_order_error": "Llenar el valor de pedido mínimo", "min_order_value": "Valor de pedido mínimo", "mins": "minutos", "minsDelayed": "<PERSON><PERSON><PERSON> re<PERSON>", "mobile": "Número de teléfono móvil", "mobile_apps_on_store": "Aplicaciones móviles disponibles en tiendas de aplicaciones", "mobile_login": "Inicio de sesión móvil", "mobile_need_update": "Ingrese su número de teléfono móvil para colocar una reserva.", "mobile_no_blank_error": "Ingrese un número móvil válido.", "mobile_number": "Ingrese el número de móvil", "mobile_or_email_cant_off": "El inicio de sesión móvil y de correo electrónico no se puede deshabilitar a la vez", "modal_close": "Modal ha sido cerrado.", "months": "Meses", "must_login": "Inicie sesión para reservar", "my_rides_menu": "Mis reservas", "my_wallet_menu": "Mi billetera", "my_wallet_tile": "Mi billetera", "my_wallet_title": "BILLETERA", "myaccount": "Mi cuenta", "name": "Nombre", "navigation_available": "La navegación del mapa solo está disponible cuando se acepta o inicia la reserva", "negative_balance": "<PERSON><PERSON><PERSON> el saldo negativo del conductor", "new_booking_notification": "Tienes una nueva solicitud de reserva", "next_page_tooltip": "<PERSON><PERSON><PERSON><PERSON> sigu<PERSON>e", "next_slide": "Próximo", "no": "No", "no_active_booking": "Sin reserva activa", "no_active_car": "No hay un coche activo disponible", "no_bookings": "No hay reservas disponibles.", "no_cancel_reason": "No hay razones de cancelación disponibles.", "no_cancelled_booking": "Sin reserva cancelada", "no_car_assign_text": "No hay asignación de vehículos", "no_cars": "No hay autos disponibles.", "no_data_available": "No hay datos disponibles", "no_details_error": "Complete todos los detalles correctamente.", "no_driver_found_alert_OK_button": "DE ACUERDO", "no_driver_found_alert_messege": "Lo siento, no se encuentran los conductores en este momento. Por favor, inténtelo de nuevo más tarde", "no_driver_found_alert_title": "¡Alerta!", "no_name": "Sin nombre", "no_others_car": "No otro coche disponible", "no_payment_getways": "Sin pasarelas de pago", "no_provider_found": "No se encontraron proveedores de pagos", "no_saved_address": "Sin dirección guardada", "no_tasks": "No se encontraron tareas para el conductor", "no_user_match": "No hay usuarios emparejados", "not_approved": "No aprobado", "not_available": "Indisponible", "not_call": "No puedes llamar ahora mismo.", "not_chat": "No puedes chatear ahora mismo.", "not_found_text": "Extraviado", "not_logged_in": "No iniciado sesión", "not_on_same_time": "El correo electrónico o las imágenes móviles y de licencia no pueden cambiar al mismo tiempo.", "not_registred": "Este correo electrónico no está registrado", "not_valid_user_type": "Usertype no es válido.", "notification_title": "Tienes una notificación", "of": "de", "off": "APAGADO", "offline": "Estás fuera de línea", "ok": "DE ACUERDO", "on": "EN", "on_duty": "De servicio", "online": "Estás en línea", "options": "Opción", "order_id": "Ordenado", "order_no": "Pedido no:", "other": "<PERSON><PERSON>", "otherPerson": "Nombre de otra persona", "otherPersonDetailMissing": "Por favor ingrese el nombre de otra persona", "otherPersonPhone": "Otra persona Teléfono", "otherPerson_title": "Detalles de otra persona", "other_cars": "Otros autos", "other_info": "Otra información sobre vehículos o conductores", "otp": "OTP:", "otp_blank_error": "OTP no puede estar en blanco", "otp_here": "Por favor ingrese OTP aquí", "otp_sms": "es el OTP. Gracias.", "otp_validate_error": "OTP no es válido", "page_not_not_found": "Página no encontrada", "page_not_not_found_text": "¡Lo siento! La página que está buscando ya no está disponible o ha sido eliminada.", "panic_num": "Número de dial de pánico", "panic_question": "¿Realmente quieres hacer una llamada de pánico?", "panic_text": "Llamada de pánico", "parcel_option": "Opción de paquete", "parcel_type": "<PERSON><PERSON><PERSON> <PERSON>", "parcel_type_web": "<PERSON><PERSON><PERSON> <PERSON>", "parcel_types": "Tipos de paque<PERSON>", "password": "Contraseña", "password_alphaNumeric_check": "Contraseña requerida alfanumérica", "password_blank_messege": "contraseña no en blanco", "password_complexity_check": "Complejidad de contraseña", "password_must_be_8-16_characters_long": "La contraseña debe tener 8-16 caracteres.", "password_must_contain_at_least_one_digit": "La contraseña debe contener al menos un dígito.", "password_must_have_at_least_one_lowercase_character": "La contraseña debe tener al menos un carácter en minúsculas.", "password_must_have_at_least_one_uppercase_character": "La contraseña debe tener al menos un carácter mayúscula.", "password_must_not_contain_whitespaces": "La contraseña no debe contener espacios en blanco.", "past_booking_error": "Libro más tarde no está disponible para la fecha de fecha pasada o en los próximos 15 minutos.", "payWithCard": "Pagar con tarjeta", "pay_cash": "Pagar en efectivo", "pay_mode": "Modo de pago", "payable_ammount": "<PERSON><PERSON> por pagar", "payaed_ammount": "<PERSON><PERSON> paga<PERSON>", "payment": "Pago", "payment_cancelled": "Pago cancelado", "payment_fail": "Su pago falló", "payment_gateway": "<PERSON><PERSON><PERSON>", "payment_info": "Información de pago", "payment_mode": "Modo de pago", "payment_mode_web": "Modo de pago", "payment_of": "Su pago de", "payment_settings": "Configuración de pago", "payment_status": "Estado de pago", "payment_thanks": "<PERSON><PERSON><PERSON> por su pago.", "paynow_button": "<PERSON><PERSON> ahora", "pending": "Pendiente", "percentage": "Po<PERSON>entaj<PERSON>", "personal_info": "Información personal", "phone": "Teléfono", "phone_error_msg": "*Escriba el número de teléfono con el código de su país. Ej.: 449876543210", "phone_no_update": "Actualice su número de teléfono en la sección Editar perfil antes de hacer una reserva", "pickUpInstructions": "Instrucciones", "pickUpInstructions_web": "Instrucciones", "pickup_address": "Dirección de origen", "pickup_address_from_map": "Seleccione la dirección de recogida del mapa", "pickup_location": "Ubicación de origen", "place_to_coords_error": "Lugar para coordinar el error. <PERSON>r favor intente de nuevo.", "port": "Puerto", "position": "Posición de lista", "position_required": "Elija la posición de la lista", "prepaid": "<PERSON><PERSON>", "previous_page_tooltip": "Página anterior", "privacy_policy": "política de privacidad", "privacy_policy_change_privacy_para1": "Podemos actualizar nuestra Política de privacidad de vez en cuando. Por lo tanto, se le aconseja que revise esta página periódicamente para cualquier cambio. Le notificaremos cualquier cambio publicando la nueva Política de privacidad en esta página.", "privacy_policy_change_privacy_para2": "Esta política es efectiva a partir de 2023-12-12", "privacy_policy_children_para1": "Estos servicios no se dirigen a nadie menor de 13 años. No recopilamos a sabiendas información de identificación personal de niños menores de 13 años. En el caso, descubrimos que un niño menor de 13 años nos ha proporcionado información personal, inmediatamente lo eliminamos de nuestros servidores. Si usted es padre o tutor y sabe que su hijo nos ha proporcionado información personal, contáctenos para que podamos realizar las acciones necesarias.", "privacy_policy_contact_para1": "Si tiene alguna pregunta o sugerencia sobre nuestra Política de privacidad, no dude en contactarnos en", "privacy_policy_cookie_para1": "Las cookies son archivos con una pequeña cantidad de datos que se usan comúnmente como identificadores únicos anónimos. Estos se envían a su navegador desde los sitios web que visita y se almacenan en la memoria interna de su dispositivo.", "privacy_policy_cookie_para2": "Este servicio no utiliza estas \"cookies\" explícitamente. Sin embargo, la aplicación puede usar código de terceros y bibliotecas que usan \"cookies\" para recopilar información y mejorar sus servicios. Tiene la opción de aceptar o rechazar estas cookies y saber cuándo se envía una cookie a su dispositivo. Si elige rechazar nuestras cookies, es posible que no pueda usar algunas partes de este servicio.", "privacy_policy_heading_change_privacy": "Cambios a esta Política de privacidad", "privacy_policy_heading_children": "Privacidad de los niños", "privacy_policy_heading_contact": "Cont<PERSON><PERSON><PERSON>s", "privacy_policy_heading_cookie": "GALLETAS", "privacy_policy_heading_info": "Recopilación y uso de información", "privacy_policy_heading_link": "Enlaces a otros sitios", "privacy_policy_heading_log": "Datos de registro", "privacy_policy_heading_security": "SEGURIDAD", "privacy_policy_heading_service": "Proveedores de servicios", "privacy_policy_info_list1": "Nombre de pila", "privacy_policy_info_list2": "Apellido", "privacy_policy_info_list3": "Dirección de correo electrónico", "privacy_policy_info_list4": "Número de teléfono", "privacy_policy_info_list5": "Buscar ubicación del vehículo", "privacy_policy_info_list6": "Ruta de viaje y navegación", "privacy_policy_info_list7": "Movimiento del vehículo en tiempo real", "privacy_policy_info_para1": "Para una mejor experiencia, mientras usa nuestro servicio, requeriremos que nos proporcione cierta información de identificación personal, incluida, entre otros,", "privacy_policy_info_para2": "La información que solicitemos será retenida por nosotros y utilizada como se describe en esta Política de privacidad.", "privacy_policy_info_para3": "Recopilamos la siguiente información confidencial cuando usa o registra en nuestra aplicación.", "privacy_policy_info_para4": "Recopilamos sus datos de ubicación para habilitar", "privacy_policy_info_para5": "La aplicación utiliza servicios de terceros para el inicio de sesión social, como Google y Apple, el inicio de sesión que recopila información utilizada para identificarlo. Capturamos el correo electrónico del usuario y el nombre del inicio de sesión social si el usuario ha elegido para que se revele mientras realiza un inicio de sesión social.", "privacy_policy_link_para1": "Este servicio puede contener enlaces a otros sitios. Si hace clic en un enlace de terceros, se le dirigirá a ese sitio. Tenga en cuenta que estos sitios externos no son operados por nosotros. Por lo tanto, le recomendamos encarecidamente que revise la política de privacidad de estos sitios web. No tenemos control y no asumimos ninguna responsabilidad por el contenido, las políticas de privacidad o las prácticas de ningún sitio o servicios de terceros.", "privacy_policy_log_para1": "Queremos informarle que cada vez que usa nuestro servicio, en un caso de un error en la aplicación, recopilamos datos e información (a través de productos de terceros) en su teléfono llamado Log Data. Estos datos de registro pueden incluir información como la dirección de su protocolo de Internet (\"IP\"), el nombre del dispositivo, la versión del sistema operativo, la configuración de la aplicación al utilizar nuestro servicio, la hora y la fecha de su uso del servicio y otras estadísticas.", "privacy_policy_para1": "Esta página se utiliza para informar a los visitantes sobre nuestras políticas con la recopilación, uso y divulgación de información personal si alguien decidió usar cualquiera de nuestro servicio.", "privacy_policy_para2": "Si elige usar nuestro servicio, entonces acepta la recopilación y el uso de información en relación con esta política. La información personal que recopilamos se utiliza para proporcionar y mejorar el servicio. No usaremos ni compartiremos su información con nadie, excepto como se describe en esta Política de privacidad.", "privacy_policy_para3": "Los términos utilizados en esta Política de privacidad tienen los mismos significados que en nuestros Términos y condiciones, que es accesible en todas nuestras aplicaciones a menos que se define lo contrario en esta Política de privacidad.", "privacy_policy_security_para1": "%20 Valor%20 our%20Trust%20in%20Provising%20US%20 our%20personal%20información,%20thus%20we%20e%20Strining%20to%20use%20Mercialmente%20aceptable%20Means%20Of%20 PROTECTING%20IT.%20BUT%20 Rember%20thththththththththththththththththththththththththT A%20NO%20metod%20Of%20transmission%20ver%20The%20internet,%20or%20metod%20Of%20Electrónico%20Storage%20IS%20100 %% 20 SECUESTRA%20 y%20RIVIABLEDE,%20 y%20 Ve 20 Cannot%20 garantía%20ITS%20Bsolute%20sCurity.", "privacy_policy_service_list1": "Para facilitar nuestro servicio;", "privacy_policy_service_list2": "Para proporcionar el servicio en nuestro nombre;", "privacy_policy_service_list3": "Para realizar servicios relacionados con el servicio; o", "privacy_policy_service_list4": "Para ayudarnos a analizar cómo se utiliza nuestro servicio.", "privacy_policy_service_para1": "Podemos emplear compañías e individuos de terceros debido a las siguientes razones:", "privacy_policy_service_para2": "Queremos informar a los usuarios de este servicio que estos terceros tienen acceso a su información personal. La razón es realizar las tareas que se les asignan en nuestro nombre. Sin embargo, están obligados a no divulgar o usar la información para ningún otro propósito.", "processDate": "Fecha de proceso", "process_withdraw": "Retirar el proceso", "processed": "Procesado", "product_section_1": "Tu experiencia es importante para nosotros. Nunca nos comprometemos en nuestros estándares. Se enfatizan particularmente las regulaciones de seguridad para garantizar la tranquilidad.", "product_section_2": "¡Vea el seguimiento en vivo, las estimaciones de precios e incluso reservar con anticipación! Tenemos todas las vías cubiertas con todas las últimas tecnologías.", "product_section_3": "El conductor puede agregar muchos tipos de vehículos.", "product_section_4": "Además del efectivo, la tarjeta de crédito, los pagos cumplen con PCI DSS. El sitio web tiene una certificación SSL, y las aplicaciones son revisadas por Apple Store y Google Play.", "product_section_heading": "Información de servicio", "product_section_para": "Brindamos los mejores servicios basados ​​en aplicaciones del país.", "profile": "Perfil", "profile_image": "Imagen de perfil", "profile_incomplete": "Perfil incompleto. Vaya a la configuración del perfil.", "profile_page_subtitle": "Detalles del perfil", "profile_page_title": "Mi perfil", "profile_setting_menu": "Configuración de perfil", "profile_title": "PERFIL", "profile_updated": "Perfil actualizado.", "promo": "Promoción", "promo_apply": "(Aplicar la promoción)", "promo_code": "Código de promoción", "promo_code_error": "Llenar el código de promoción", "promo_code_web": "Código de promoción", "promo_description_error": "Descripción de la promoción de relleno", "promo_discount": "Descuento de promoción", "promo_discount_type_error": "Tipo de descuento de promoción de relleno", "promo_discount_value": "Valor promocional", "promo_discount_value_error": "Valor de descuento de promoción de relleno", "promo_eligiblity": "¡Lo siento! La promoción no es válida ya que el monto de la factura es menor.", "promo_exp": "¡Lo siento! La promoción ha expirado.", "promo_exp_limit": "¡Lo siento! Límite de uso de promoción.", "promo_name": "Nombre promocional", "promo_name_error": "Llenar el nombre de la promoción", "promo_not_found": "Este código de promoción no se encuentra", "promo_offer": "Promoción y ofertas", "promo_offer_title": "Promoción y ofertas", "promo_usage": "Recuento de promociones disponibles", "promo_used": "¡Lo siento! Ya ha usado el código de promoción.", "promo_used_by": "Promoción utilizada por el conteo", "proper_bonus": "Ingrese la bonificación adecuada en los números.", "proper_email": "Ingrese el correo electrónico correctamente.", "proper_input_image": "Subir la imagen adecuada", "proper_input_licenseimage": "Subir la imagen de licencia adecuada", "proper_input_name": "Ingrese el nombre propio", "proper_input_vehicleno": "Ingrese el número adecuado del vehículo", "proper_mobile": "Ingrese el móvil correctamente.", "provider_fetch_error": "No se puede buscar proveedores de pagos", "provider_not_found": "No se encontraron proveedores de pagos.", "pruduct_section_heading_1": "<PERSON><PERSON><PERSON><PERSON>", "pruduct_section_heading_2": "Conveniente", "pruduct_section_heading_3": "Gestión de vehículos", "pruduct_section_heading_4": "<PERSON><PERSON><PERSON>", "push_error_1": "¡No se pudo obtener token  para la notificación push!", "push_error_2": "Debe usar el dispositivo físico para las notificaciones push", "push_notification_title": "Notificaciones push", "push_notifications": "Notificaciones push", "push_notifications_title": "Notificaciones push", "queue": "Ocupado", "rate_for": "Calificar para", "rate_per_hour": "<PERSON><PERSON><PERSON> por hora", "rate_per_hour_required": "<PERSON>ja la tarifa por hora", "rate_per_unit_distance": "Velocidad de distancia por (km o milla)", "rate_per_unit_distance_required": "Elija la tarifa de distancia por (km o milla)", "rate_ride": "Califica a tu conductor", "real_time_driver_section_text": "Conductores en tiempo real", "realtime_drivers": "Ubicación en vivo del conductor", "reason": "Razón", "receipt": "Recibo", "received_rating": "Recibiste una calificación de estrella X", "refer_earn": "Referirse", "referer_not_found": "ID de referencia no encontrado", "referralId": "ID de referencia", "referral_bonus": "<PERSON><PERSON>", "referral_email_used": "Este correo electrónico ya utilizó una identificación de referencia", "referral_id_placeholder": "ID de referencia (opcional)", "referral_number_used": "Este número ya usó una identificación de referencia", "reg_error": "Error mientras se registra", "register": "Registro", "register_as_driver": "Registrar usuario?", "register_button": "Registrar<PERSON> ahora", "registration_title": "Registro", "remove_promo": "Eliminar la promoción", "report": "Informes", "requestDate": "<PERSON><PERSON>", "request_otp": "Iniciar sesión con OTP", "request_payment": "Solicitar pago", "require_approval": "Su cuenta requiere la aprobación del administrador", "ride_details_page_title": "Detalles de la reserva", "ride_information": "Información de viaje", "ride_list_title": "Mis reservas", "rider_cancel_text": "Reserva Cancelar", "rider_not_here": "No hay solicitudes en este momento", "rider_withdraw": "Retirar el cliente", "riders": "Clientes", "riders_title": "CLIENTES", "rides": "Paseos", "roundTrip": "IDA Y VUELTA", "rows": "Hilera", "save": "AHORRAR", "saved_address": "Dirección guardada", "search": "Buscar", "search_for_an_address": "Buscar una dirección", "searching": "Búsqueda", "secure": "<PERSON><PERSON><PERSON>", "security_title": "Configuración de seguridad", "selectBid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "select_car": "Seleccione el tipo de vehículo", "select_country": "<PERSON><PERSON>", "select_date": "Seleccione Fecha", "select_driver": "<PERSON><PERSON><PERSON><PERSON>r conductor", "select_payment_getway": "Seleccione la pasarela de pago", "select_proper": "Seleccione correctamente.", "select_reason": "Seleccione la razón de cancelación", "select_user": "Se<PERSON><PERSON><PERSON>r usuario", "send_button_text": "Enviar", "senderPersonPhone": "Reserva de la persona por teléfono no", "service_off": "El estado de su servicio está 'desactivado'", "service_start_soon": "Nuestro servicio comenzará pronto ...", "set_decimal": "Establecer decimal", "set_link_email": "Restablecer el enlace de contraseña enviar al correo anterior", "settings_label3": "Reserva OTP", "settings_label4": "Aprobación del conductor", "settings_label5": "Aprobación de coche", "settings_label6": "Verificar ID / Passport", "settings_title": "<PERSON><PERSON><PERSON><PERSON>", "share_msg": "<PERSON><PERSON>, use este código mientras se registra y obtiene una bonificación de", "share_msg_no_bonus": "<PERSON><PERSON>, mira esta aplicación. Me encanta.", "show_in_list": "Mostrar en la lista", "show_live_route": "Mostrar ruta en vivo", "signIn": "In<PERSON><PERSON>", "signup": "Inscribirse", "signup_via_referral": "Registrarse a través de la referencia", "skip": "Saltar", "smssettings": "Configuración de SMS", "smssettings_title": "Configuración de SMS", "smtp_error": "Consulte sus detalles de SMTP", "smtpsettings": "Configuración SMTP", "smtpsettings_title": "Configuración SMTP", "social_login": "Inicio de sesión social", "solved": "<PERSON><PERSON><PERSON><PERSON>", "sos": "Llamada de socorro", "sos_title": "LLAMADA DE SOCORRO", "spacer_message": "O conectarse con", "start_accept_bid": "Has recibido una oferta", "start_trip": "<PERSON><PERSON><PERSON><PERSON>", "stops": "Parada", "subject": "Sujeto", "submit": "ENTREGAR", "submit_rating": "Enviar calificación", "success": "Éxito", "success_payment": "Pago realizado con éxito.", "swipe_symbol": "Dirección de símbolo de deslizamiento", "system_price": "Precio del sistema", "system_price_with_bid": "Precio del sistema con oferta", "take_deliver_image": "Imagen de entrega", "take_deliver_image_web": "Imagen de entrega", "take_pickup_image": "Imagen de recogida", "take_pickup_image_web": "Imagen de recogida", "task_list": "<PERSON><PERSON>o", "term_condition": "Térm<PERSON>s", "term_condition_heading1": "Aceptación de los términos:", "term_condition_heading10": "Terminación:", "term_condition_heading11": "Ley de gobierno:", "term_condition_heading12": "Enmiendas:", "term_condition_heading2": "Elegibilidad:", "term_condition_heading3": "Descripción del servicio:", "term_condition_heading4": "Pago :", "term_condition_heading5": "Conducta del usuario:", "term_condition_heading6": "Contenido de usuario:", "term_condition_heading7": "Propiedad intelectual:", "term_condition_heading8": "Descargos de responsabilidad:", "term_condition_heading9": "Limitación de responsabilidad:", "term_condition_para1": "G<PERSON><PERSON> por elegir nuestros servicios de reserva basados ​​en aplicaciones para el taxi, el taxi y las necesidades de entrega. Lea cuidadosamente los siguientes términos y condiciones antes de usar nuestros servicios:", "term_condition_para10": "En ningún caso seremos responsables de daños indirectos, incidentales, especiales o consecuentes que surjan o en relación con el uso de nuestros servicios, ya sea que se nos haya informado o no sobre la posibilidad de tales daños.", "term_condition_para11": "Nos reservamos el derecho de cancelar un acceso a los usuarios a nuestra plataforma en cualquier momento, sin previo aviso, por cualquier motivo.", "term_condition_para12": "Estos Términos y condiciones se regirán e interpretan de acuerdo con las leyes de la jurisdicción donde se basa la Compañía.", "term_condition_para13": "Nos reservamos el derecho de actualizar estos términos y condiciones en cualquier momento, sin previo aviso. Se recomienda a los usuarios que revisen estos términos y condiciones periódicamente para mantenerse informados de cualquier cambio.", "term_condition_para14": "Si tiene alguna pregunta o inquietud sobre estos términos y condiciones, contáctenos en", "term_condition_para2": "Al utilizar nuestros servicios, usted acepta estar sujeto a estos términos y condiciones. Si no está de acuerdo con estos Términos y condiciones, no puede usar nuestros Servicios.", "term_condition_para3": "Debe tener al menos 18 años para usar nuestros servicios. Al usar nuestros servicios, usted representa y garantiza que tiene al menos 18 años.", "term_condition_para4": "Nuestros servicios de reserva basados ​​en aplicaciones proporcionan una plataforma para conectar a los usuarios con proveedores de taxis, taxis y entrega. No poseemos, operamos ni controlamos ninguno de los vehículos o conductores que usan nuestra plataforma. Nuestros servicios están destinados a ser utilizados solo para uso personal y no comercial.", "term_condition_para5": "Los usuarios deben pagar los servicios proporcionados a través de nuestra plataforma. Aceptamos varias formas de pago, incluidas tarjetas de crédito, tarjetas de débito y métodos de pago electrónico. Todos los pagos realizados a través de nuestra plataforma están sujetos a nuestras políticas de pago, que pueden actualizarse de vez en cuando.", "term_condition_para6": "Se espera que los usuarios usen nuestros servicios de manera responsable y no participen en ningún comportamiento que pueda dañar nuestra plataforma, conductores u otros usuarios. Los usuarios no pueden usar nuestra plataforma para ningún propósito ilegal o no autorizado. También se les prohíbe a los usuarios interferir con el funcionamiento de nuestra plataforma o participar en cualquier actividad que pueda comprometer la seguridad o la integridad de nuestra plataforma.", "term_condition_para7": "Los usuarios son únicamente responsables del contenido que envían a través de nuestra plataforma. Al enviar contenido, los usuarios nos otorgan una licencia no exclusiva, libre de regalías, transferible y sublicensible para usar, modificar y reproducir el contenido con el fin de proporcionar nuestros servicios.", "term_condition_para8": "Nuestra plataforma, incluida todo el contenido y la propiedad intelectual, es propiedad de nosotros y/o nuestros licenciantes. Los usuarios no pueden copiar, modificar, distribuir o reproducir ninguno de los contenidos o propiedad intelectual sin nuestro consentimiento previo por escrito.", "term_condition_para9": "No garantizamos la disponibilidad, confiabilidad o calidad de los servicios proporcionados por los conductores que utilizan nuestra plataforma. No somos responsables de ninguna pérdida o daño que pueda resultar del uso de nuestros servicios. Nuestra plataforma se proporciona 'tal cual' y sin ninguna garantía, expresa o implícita.", "term_required": "<PERSON><PERSON><PERSON><PERSON>", "terms": "política de privacidad", "thanks": "<PERSON><PERSON><PERSON> por la calificación", "this_month_text": "<PERSON><PERSON>", "thismonth": "<PERSON>ste mes", "thisyear": "<PERSON>ste año", "time": "TIEMPO", "title": "<PERSON><PERSON><PERSON><PERSON>", "today_text": "Hoy", "total": "Total", "total_cumtomer": "No. de cliente", "total_drivers": "No. de conductores", "total_fare": "Tarifa total", "total_time": "Tiempo total", "totalearning": "Ganancias totales", "transaction_history_title": "Historial de transacciones de billetera", "transaction_id": "ID de transacción:", "tripInstructions": "Instrucciones de viaje", "trip_cost": "Costo de viaje", "trip_cost_driver_share": "Compartir el <PERSON>", "trip_date_time": "Hora de la fecha de viaje", "trip_end_time": "Hora de finalización de viaje", "trip_instruction": "Instrucción de viaje", "trip_start_date": "Fecha de inicio del viaje", "trip_start_time": "Hora de inicio del viaje", "try_again": "Por favor intente de nuevo.", "type": "Tipo", "update_admin": "Actualizar admin", "update_admin_title": "Actualizar admin", "update_any": "No puede actualizar el correo electrónico y el móvil al mismo tiempo.", "update_button": "Actualización ahora", "update_carType_title": "Actualizar el tipo de coche", "update_car_title": "Actualizar coche", "update_customer_title": "Actualizar el cliente", "update_driver_title": "Actualizar conductor", "update_failed": "La actualización falló", "update_fleetAdmin": "Actualizar admin de flota", "update_fleetAdmin_title": "Actualizar admin de flota", "update_profile_title": "Documento de actualización", "update_promo_title": "Actualizar promoción", "updated": "Actualizado", "upload_car_image": "<PERSON>gar imagen de coche", "upload_driving_license": "Sube tu licencia de conducir", "upload_driving_license_back": "Cargue su licencia de conducir hacia atrás", "upload_driving_license_front": "Sube el frente de tu licencia de conducir", "upload_id_details": "Sube tu identificación / pasaporte", "upload_profile_image": "Imagen de perfil de carga", "upload_verifyIdImage": "Cargue su ID / número de pasaporte", "use_distance_matrix": "Use la API de matriz de distancia", "use_wallet_balance": "Use el efectivo de la billetera (su saldo es", "user": "Usuarios", "user_exists": "El usuario con el mismo correo electrónico o número de teléfono móvil existe.", "user_issue_contact_admin": "No se encontraron datos de perfil. Póngase en contacto con el administrador", "user_type": "Tipo de usuario", "username": "Nombre de usuario", "users_title": "Usuarios", "usertype": "Tipo de usuario", "valid_amount": "Ingrese una cantidad válida", "value_for_money": "Valor por dinero", "vehicleMake_required": "Se requiere una marca del vehículo/marca", "vehicleModel_required": "Requerido el modelo de vehículo", "vehicleNumber_required": "REG DE VEHÍCULO No. requerido", "vehicle_make": "<PERSON><PERSON>", "vehicle_model": "<PERSON><PERSON>", "vehicle_model_name": "Marca del vehículo / marca", "vehicle_model_no": "Modelo de vehículo NO", "vehicle_no": "Número de vehículo", "vehicle_number": "Número", "vehicle_reg_no": "Número de registro del vehículo", "verify_id": "ID / Número de pasaporte", "verify_otp": "Verificar OTP", "verifyid_error": "Lo siento, no tienes identificación / pasaporte", "verifyid_image": "ID / imagen de pasaporte", "wallet": "Bill<PERSON>a", "wallet_bal_low": "Balance de billetera bajo.", "wallet_balance": "Equilibrio de billetera", "wallet_balance_low": "Su equilibrio de billetera es demasiado bajo.", "wallet_balance_threshold_reached": "El umbral de equilibrio de su billetera alcanzó", "wallet_balance_zero": "El saldo de su billetera es 0. Si toma el pago en efectivo, la tarifa de conveniencia del administrador se deducirá de la billetera y su saldo de billetera será negativo después del trabajo. El equilibrio negativo restringirá más trabajos. Debe recargar la billetera o solicitar ayuda a Admin.", "wallet_balance_zero_customer": "Su equilibrio de billetera es negativo. No se le permite reservar con equilibrio negativo", "wallet_ballance": "Equilibrio de billetera", "wallet_booking_alert": "No puedes reservar otro usando billetera. Tienes una reserva de billetera existente.", "wallet_money_field": "Denominaciones de billetera", "wallet_payment_amount": "Monto del pago de la billetera", "wallet_title": "Finanzas", "wallet_updated": "Billetera actualizada con éxito.", "wallet_zero": "Balance de billetera 0.", "was_successful": "fue exitoso", "where_are_you": "Usted está aquí", "withdraw": "RETIRAR", "withdraw_below_zero": "La cantidad de retiro no puede ser menor o igual a 0.", "withdraw_money": "<PERSON><PERSON><PERSON>o", "withdraw_more": "El monto de retiro no puede ser mayor que el saldo de la billetera.", "withdrawn": "RETIRADO", "withdraws": "<PERSON><PERSON><PERSON>", "withdraws_web": "<PERSON><PERSON><PERSON>", "within_min": "<PERSON><PERSON><PERSON>", "work": "Trabajar", "year": "<PERSON><PERSON><PERSON>", "yes": "Sí", "you_are_offline": "Tu deber está apagado", "you_rated_text": "Calificación del conductor", "your_fare": "Tu tarifa", "your_feedback": "Sus comentarios (opcionales) ...", "your_feedback_test": "Sus comentarios nos ayudarán a mejorar mejor la experiencia de manejo.", "your_promo": "Tu promoción", "your_trip": "<PERSON>"}, "langLocale": "es", "langName": "Español", "tableData": {"id": 0}}, "lang1": {"dateLocale": "en-gb", "default": false, "id": "lang1", "keyValuePairs": {"ACCEPTED": "ACCEPTED", "ARRIVED": "ARRIVED", "AppName": "App Name", "AppleStoreLink": "Apple Store Link", "Balance": "Balance", "CANCELLED": "CANCELLED", "COMPLETE": "COMPLETE", "CardPaymentAmount": "Paid by Card -", "CashPaymentAmount": "Paid by Cash -", "CompanyName": "Company Name", "CompanyWebsite": "Company Website", "Customer_paid": "Customer paid", "Discounts": "Discounts", "FacebookHandle": "Facebook Page Link", "Gross_trip_cost": "Gross trip cost", "InstagramHandle": "Instagram Page Link", "NEW": "NEW", "PAID": "PAID", "PAYMENT_PENDING": "PAYMENT PENDING", "PENDING": "PENDING", "PlayStoreLink": "Play Store Link", "Profit": "Profit", "REACHED": "REACHED", "STARTED": "STARTED", "TwitterHandle": "Twitter Page Link", "WalletPayment": "Paid by <PERSON><PERSON>", "Withdraw_title": "WITHDRAWS", "about_us": "About Us", "about_us_content1": "We are the largest mobility platform and one of the world's largest online on-demand services provider.", "about_us_content2": "Manage bookings, request quotes, or book a online service with our simple and quick online booking system. We are an on-demand services company that allows guests to easily book various services online. We offer the best services in the country. ", "about_us_menu": "About Us", "accept": "ACCEPT", "accept_booking_request": " accepted your booking request.", "accepted_booking": "Your booking is accepted", "account_approve": "Account Approved", "account_create_successfully": "Account created successfully", "actions": "Actions", "active_booking": "ACTIVE BOOKING", "active_car": "Active Car", "active_car_delete": "Active Car cannot be deleted.", "active_driver": "Active Drivers", "active_status": "Active Status", "add": "ADD", "addMoneyTextInputPlaceholder": "Add Amount", "add_admin": "Add Admin", "add_admin_title": "ADD ADMIN", "add_booking_title": "ADD BOOKINGS", "add_car": "Add Car", "add_carType": "Add Vehicle Type", "add_carType_title": "ADD CAR TYPE", "add_car_title": "ADD CAR", "add_cartype_title": "ADD VEHICLE", "add_customer": "Add Customer", "add_customer_title": "ADD CUSTOMER", "add_driver": "Add Driver", "add_driver_title": "ADD DRIVER", "add_fleetadmin": "Add Fleet Admin", "add_fleetadmin_title": "ADD FLEET ADMIN", "add_language": "Edit Language", "add_money": "Add Money", "add_notification": "Add Notifications", "add_notification_title": "ADD NOTIFICATION", "add_promo_title": "ADD PROMO", "add_to_review": "Add to review", "add_to_wallet": "Add to Wallet", "add_to_wallet_title": "ADD TO WALLET", "addbookinglable": "Add Bookings", "admin": "Admin", "admin_contact": "Contact your admin to approve account", "advance_settings": "Advance Settings", "alert": "<PERSON><PERSON>", "alert_text": "<PERSON><PERSON>", "all": "All", "alladmin": "All Admins", "alladmins_title": "ALL ADMINS", "allow_del_final_img": "Allow delivery drop image", "allow_del_pkp_img": "Allow delivery pick image", "allow_location": "Allow Location for the Realtime Map", "allow_multi_country": "Allow Multi Country Selection", "allow_only": "Location needs while using app", "always_on": "Location needs Always On", "amount": "Amount", "amount_must_be_gereater_than_100": "For Slickpay amount must be greater than 100", "android": "Android", "apiUrl": "Api Url", "app_info": "App Information", "app_info_title": "APP INFORMATION", "app_link": "App Link : ", "app_store_deception": "Your business needs to be in safe hands at all times. We ensure you never run out of customers and not run at loss. We are trusted by over 500+ companies to deliver quality marketing campaigns using Digital marketing & Offline marketing channels.", "app_store_deception1": "The App and for Customer and Driver, not for the Admin Login. Take 2 phones and install in each. Register a Customer in One and register a Driver in One. For the Driver always select Location Always Allow when login in. Then creating a booking from One phone and receiving on the other where driver logged in. Use the provided User login on the Web link to see the Admin side of things.", "apple_signin_error": "Apple SignIn is not set up in developer.appple.com or you have used the same email to SignUp already.", "apply": "APPLY", "apply_promo": "Apply Promo", "approve_status": "Approve Status", "approved": "Approved", "assign": "ASSIGN", "assign_driver": "Assign Driver", "auth_error": "Auth Error: Please check your entered credentials", "authorization": "Authorization", "auto_dispatch": "Auto Dispatch", "back": "BACK TO LOGIN", "bankAccount": "Bank Account", "bankCode": "Bank Code", "bankDetails": "BANK DETAILS", "bankName": "Bank Name", "bank_fields": "Bank Reg Fields", "base_fare": "Base Fare", "base_fare_required": "Please Choose Base Fare", "best_experience": "Best Experience", "best_service_provider": "BEST SERVICE PROVIDED", "best_services": "Best Services", "bid": "Bid", "bill_details": "<PERSON>", "bill_details_title": "<PERSON>", "blank_message": "No Records To Display", "body": "Body", "book": "Book", "book_later": "BOOK LATER", "book_later_button": "BOOK LATER", "book_now": "BOOK NOW", "book_now_button": "BOOK NOW", "book_ride": "Book Your Ride", "book_your_ride_menu": "Make a Booking", "book_your_title": "Book your Service", "booked_cab_title": "Manage Booking", "bookingPayment": "Booking Payment", "booking_cancelled": "Booking is cancelled. ID : ", "booking_chart": "Booking Chart", "booking_confirm": "Your booking has been confirmed", "booking_count": "Booking Count", "booking_date": "Booking Date", "booking_date_time": "Booking Data Time", "booking_details": "Booking Details", "booking_flow": "Booking Flow", "booking_history": "Booking History", "booking_id": "Booking ID", "booking_is": "Your booking is ", "booking_ref": "Booking Reference", "booking_request": "Booking Requests", "booking_status": "BOOKING STATUS", "booking_status_web": "Booking Status", "booking_success": "Booking successful. Booking Id : ", "booking_successful": "Booking Successful", "booking_title": "MY BOOKINGS", "booking_type": "Booking Type", "bookings_table": "Bookings Table", "bookings_table_title": "BOOKINGS TABLE", "camera": "Camera", "camera_permission_error": "Camera Permission Error", "cancel": "CANCEL", "cancelSlab": "Cancellation Slabs", "cancel_booking": "Cancel Booking", "cancel_confirm": "Do you want really cancel?", "cancel_messege1": "Your Job with Booking Id", "cancel_messege2": "has been cancelled successfully", "cancel_reason_modal_title": "Reason", "cancel_ride": "CANCEL BOOKING", "cancellationFee": "Cancellation Fee", "cancellation_reason": "Cancellation Reason", "cancellation_reasons": "Cancellation Reasons", "cancellation_reasons_title": "CANCELLATION REASON", "cancelled_bookings": "Cancel Booking", "cancelled_incomplete_booking": "CANCELLED INCOMPLETE BOOKING", "carApproved_by_admin": "Active car not approved by admin.", "carType_required": "Car Is Required", "car_add": "Add car for accept booking", "car_approval": "Car Approval", "car_details_title": "Vehicle Details", "car_horn_repeat": "Repeat sound on new trip", "car_image": "Car Image", "car_no_not_found": "Vehicle number not assigned", "car_type": "Vehicle Type", "car_type_blank_error": "Select vehicle type", "car_type_title": "VEHICLE TYPE", "car_view_horizontal": "Car list view Horizontal", "card": "Card", "card_payment_amount": "Card payment amount", "cars": "Cars", "cars_title": "CARS", "cash": "Cash", "cash_booking_false": "Admin can't create a booking when cash payment is disable in system.", "cash_on_delivery": "CASH ON DELIVERY", "cash_payment_amount": "Cash payment amount", "chat_blank": "please write something...", "chat_input_title": "Type something nice...", "chat_not_found": "No chat history found", "chat_requested": ": Chat Message", "chat_title": "Cha<PERSON>", "check_approve_status": "Check Approve Status", "check_email": " Check your email inbox", "check_mobile": "Check your massage box", "choose_image_first": "Choose image first.", "close": "CLOSE", "code": "Code", "code_already_avilable": "This Promo Already Available", "code_colon": "Code : ", "company_phone": "Company Phone", "complain": "<PERSON><PERSON><PERSON>", "complain_date": "Complain Date", "complain_title": "COMPLAIN", "complete_payment": "COMPLETE PAYMENT", "complete_ride": "COMPLETE JOB", "completed_bookings": "Complete Booking", "confirm": "CONFIRM", "confirm_booking": "Confirm Booking", "confirm_password": "Confirm Password", "confirm_password_not_match_err": "Confirm password not match", "contact": "Contact", "contact_email": "Contact Email", "contact_input_error": "Please enter a valid email or mobile number.", "contact_placeholder": "Mobile Number Or Email", "contact_us": "Contact Us", "contentType": "Content-Type", "convenience_fee": "Convenience Fees", "convenience_fee_required": "Please Choose Convenience Fees", "convenience_fee_type": "Convenience Fee Type", "convenience_fee_type_required": "Please Choose Convenience Fee Type", "convert_button": "Convert", "convert_to_driver": "Convert to <PERSON>", "convert_to_mile": "Convert to <PERSON>", "convert_to_rider": "Convert To <PERSON>", "cost": "COST", "country_1": "Country", "country_blank_error": "Please select the country", "country_restriction": "Autocomplete Country Restriction", "create_driver": "Create Driver", "create_new_user": "Creating new user, please wait...", "create_rider": "Create Rider", "createdAt": "Create Date", "credited": "CREDITED", "currency_code": "Currency Code", "currency_settings": "<PERSON><PERSON><PERSON><PERSON>", "currency_symbol": "Currency Symbol", "customMobileOTP": "Enable Custom Mobile OTP", "customer": "Customer", "customer_contact": "Customer contact", "customer_email": "Customer <PERSON><PERSON>", "customer_id": "Customer ID", "customer_info": "Customer Info", "customer_name": "Customer Name", "daily": "DAILY", "dashboard_text": "Dashboard", "date": "Date", "dateLocale": "Date Local", "date_time": "Date & Time", "debited": "DEBITED", "delete": "Delete", "delete_account_lebel": "Delete Account", "delete_account_modal_subtitle": "Do you want to delete your account ?", "delete_account_modal_title": "Confirmation", "delete_account_msg": "User can remove all their data from the system by deleting the account from profile page in mobile app.", "delete_account_para1": "All your data will be purged from the system. Your profile image, email, phone number, social logins including Google login and all booking history, everything will be permanently removed.", "delete_account_para2": "Deleted user data and account are irrecoverable.", "delete_account_subheading": "Once you delete your account:", "delete_message": "ARE YOU SURE YOU WANT TO DELETE THIS ROW?", "delete_your_car": "Do you want to delete your car?", "deliveryDetailMissing": "Please enter receiving person's Name & Phone No.", "deliveryInstructions": "BOOKING INSTRUCTIONS", "deliveryPerson": "Receiving Person Name", "deliveryPersonPhone": "Receiving Person Phone No", "delivery_information": "Booking Information", "demo_mode": "Restricted in Demo App.", "description": "Description", "device_id": "Device ID", "device_type": "Device Type", "disable_cash": "Disable Cash Payments", "disable_online": "Disable Online Payments", "disclaimer": "Disclaimer", "disclaimer_text": "This app collects location data to enable 'Search Vehicle Location', 'Travel Route and Navigation', 'Realtime Vehicle Movement', even when the app is closed or not in use.", "discount": "Discount", "discount_ammount": "Discount Amount", "distance": "DISTANCE", "distance_web": "Distance", "documents": "Documents", "documents_title": "DOCUMENTS", "done": "DONE", "dont_cancel_text": "Don't Cancel", "drag_map": "Drag map to select address", "driver": "Driver", "driverRadius": "<PERSON>", "driver_accept_low_cost_capacity_booking": "Driver accept low cost/capacity booking", "driver_active": "Driver Active Status", "driver_active_staus_choose": "Driver Active Staus Cho<PERSON>", "driver_assign_messege": "Driver will Assign Soon..", "driver_cancelled_booking": "DRIVER CANCELLED BOOKING", "driver_completed_ride": "You have reached destination. Please complete the payment.", "driver_contact": "Driver contact", "driver_distance": "Driver will arrive in", "driver_earning": "Driver Earning History", "driver_earning_title": "DRIVER EARNING HISTORY", "driver_email": "Driver Email", "driver_finding_alert": "Finding Drivers for you", "driver_id": "Driver ID", "driver_info": "Driver Info", "driver_journey_msg": "Driver has started. Your booking id is ", "driver_name": "Driver Name", "driver_near": "Driver Near You", "driver_required": "Driver Required", "driver_setting": "Driver Settings", "driver_share": "Driver Share", "driver_threshold": "<PERSON>", "drivers": "Drivers", "drivers_title": "DRIVERS", "driving_license_back": "Driving License Back", "driving_license_font": "Driving License Font", "drop_address": "Drop address", "drop_address_from_map": "Select drop address from map", "drop_location": "Drop Location", "drop_location_blank_error": "Select drop location", "earning_amount": "Earning Amount", "earning_reports": "Earning Reports", "earning_reports_title": "EARNING REPORTS", "edit": "Edit", "edit_json": "<PERSON>", "editcar": "Edit Car", "email": "Email", "email_id": "Enter Email Id", "email_login": "<PERSON><PERSON>", "email_msg": "We hope you enjoyed your ride with us.", "email_or_mobile_issue": "Please check your email id or mobile number. Note: Mobile number should be in International format e.g. +919999888877", "email_placeholder": "Email", "email_send": "Email Send", "email_use": "Email <PERSON>cation Mode", "emergency": "Emergency", "end_date": "End Date", "end_time": "End Time", "enter_code": "Enter the code", "estimate_fare_text": "This is your estimate fare only", "export": "Export", "extra_info": "Extra Information", "facebook_login_auth_error": "Facebook Login Error:", "feedback": "<PERSON><PERSON><PERSON>", "fill_email_first": "Add email first then take document image", "first_admin_deleted": "First admin cannot be deleted", "first_name": "First Name", "first_name_placeholder": "First Name", "first_page_tooltip": "First Page", "firstname": "First Name", "fix": "Fix It", "flat": "Flat", "flat_minorder_maxorder": "In promo type of flat your minimum order value can not exceed max discount.", "fleet_admin_comission": "Fleetadmin Commission", "fleet_admin_fee": "Fleet Admin Fee", "fleet_admin_fee_required": "Fleet Admin <PERSON>e Required", "fleetadmin_earning_reports": "<PERSON><PERSON><PERSON>", "fleetadmin_id": "Fleetadmin ID", "fleetadmin_name": "Fleetadmin Name", "fleetadmins": "Fleet Admins", "fleetadmins_title": "FLEET ADMINS", "follow_facebook": "Follow us on Facebook", "follow_instagram": "Follow us on Instagram", "follow_twitter": "Follow us on Twitter", "for_other_person": "Booking for other person", "force_end": "Force End", "forgot_password": "Forgot password?", "fromEmail": "From Email", "general_settings": "GENERAL SETTINGS", "general_settings_title": "GENERAL SETTINGS", "get_estimate": "Get Estimate", "get_started": "Get startted", "go_back": "Go Back", "go_online_to_accepting_jobs": "Start accepting jobs.", "go_to_booking": "GO TO BOOKING", "go_to_home": "Go to Home", "google_places_error": "Place ID to Location Error", "goto_home": "Goto Home", "gross_earning": "Gross Earnings", "hidden_demo": "Hidden for <PERSON><PERSON>", "home": "Home", "host": "Host", "hours": "Hours", "how_your_trip": "How is your trip?", "id": "ID", "ignore_job_title": "Do you want to ignore this job?", "ignore_text": "IGNORE", "image": "Image", "image_size_warning": "(Image size: Max 2 MB)", "image_upload_error": "Image upload error", "in_demo_mobile_login": "Mobile login is disabled in demo mode", "incomeText": "My Earnings", "incomplete_user": "User profile is incomplete.", "info": "Info", "ios": "iOS", "km": "km", "landing_slogan": "Best Services Guaranteed", "lang": "Language", "lang1": "Lang:", "langLocale": "Language Locale", "langName": "Language Name", "language": "Languages", "language_cap": "LANGUAGES", "last_name": "Last Name", "last_name_placeholder": "Last Name", "last_page_tooltip": "Last Page", "lastname": "Last Name", "license_image": "License Image", "license_image_back": "License image back", "license_image_front": "License image front", "license_image_required": "License Image Required", "loading": "Loading...", "locationServiveBody": "Background location is running...", "locationServiveTitle": "Location Update", "location_fetch_error": "Location fetch error", "location_permission_error": "Location Permission Error", "login": "<PERSON><PERSON>", "login_error": "Authentication error", "login_otp": "LOG IN WITH OTP", "login_settings": "<PERSON><PERSON>", "login_signup": "Login / Sign Up", "logout": "Logout", "make_active": "Make Active", "make_changes_to_update": "Make changes to Update", "make_default": "<PERSON>", "map_screen_drop_input_text": "Drop Where ?", "map_screen_where_input_text": "Where From ?", "marker_title_1": "Pickup", "marker_title_2": "Drop", "marker_title_3": "Stops", "max_limit": "Max Discount Allowed", "max_promo_discount_value_error": "Fill max promo discount value", "medialibrary": "Media Library", "message": "Write your message", "message_text": "Message", "method": "Method", "mile": "mi", "min_fare": "Minimum Fare", "min_fare_required": "Please Choose Minimum Fare", "min_limit": "Minimum Order Value", "min_order_error": "Fill minimum order value", "min_order_value": "Minimum order value", "mins": "mins", "minsDelayed": "Minutes Delayed", "mobile": "Mobile Number", "mobile_apps_on_store": "Mobile Apps available on App Stores", "mobile_login": "Mobile Login", "mobile_need_update": "Please input your Mobile Number for placing a booking.", "mobile_no_blank_error": "Please enter a valid mobile number.", "mobile_number": "Enter Mobile Number", "mobile_or_email_cant_off": "Mobile and Email login cannot be disabled at a time", "modal_close": "Modal has been closed.", "months": "Months", "must_login": "Please Login for Booking", "my_rides_menu": "My Bookings", "my_wallet_menu": "My Wallet", "my_wallet_tile": "My Wallet", "my_wallet_title": "WALLET", "myaccount": "My Account", "name": "Name", "navigation_available": "Map Navigation is only available when booking is ACCEPTED or STARTED", "negative_balance": "Allow Driver Negative Balance", "new_booking_notification": "You Have A New Booking Request", "next_page_tooltip": "Next Page", "next_slide": "Next", "no": "No", "no_active_booking": "No active booking", "no_active_car": "No Active Car Available", "no_bookings": "No bookings available.", "no_cancel_reason": "No cancel reasons available.", "no_cancelled_booking": "No cancelled booking ", "no_car_assign_text": "No vehicle assign", "no_cars": "No cars available.", "no_data_available": "No Data Available", "no_details_error": "Please fill up all the details properly.", "no_driver_found_alert_OK_button": "OK", "no_driver_found_alert_messege": "Sorry,No Drivers found right now.Please try again later", "no_driver_found_alert_title": "<PERSON><PERSON>!", "no_name": "No Name", "no_others_car": "No Others Car Available", "no_payment_getways": "No payment getways", "no_provider_found": "No Payment Providers Found", "no_saved_address": "No Saved Address", "no_tasks": "No tasks found for Driver", "no_user_match": "No Users Matched", "not_approved": "Not Approved", "not_available": "Unavailable", "not_call": "You can not call right now.", "not_chat": "You can not chat right now.", "not_found_text": "Not Found", "not_logged_in": "Not Logged In", "not_on_same_time": "Email or Mobile and License images can't change at the same time.", "not_registred": "This email is not registered", "not_valid_user_type": "Usertype is not valid.", "notification_title": "You have a notification", "of": "of", "off": "OFF", "offline": "You're offline", "ok": "OK", "on": "ON", "on_duty": "On Duty", "online": "You're online", "options": "OPTIONS", "order_id": "OrderId ", "order_no": "Order No : ", "other": "Other", "otherPerson": "Other Person Name", "otherPersonDetailMissing": "Please enter other person's Name & Phone No.", "otherPersonPhone": "Other Person Phone", "otherPerson_title": "Other Person Details", "other_cars": "Other Cars", "other_info": "Other Vehicle or Driver Info", "otp": "OTP :", "otp_blank_error": "OTP can't be blank", "otp_here": "Please enter OTP here", "otp_sms": " is the OTP. Thank you.", "otp_validate_error": "OTP is not valid", "page_not_not_found": "Page Not Found", "page_not_not_found_text": "Sorry! The page you are looking for is no longer available or has been removed.", "panic_num": "Panic Dial Number", "panic_question": "Do you really want to make a Panic Call?", "panic_text": "Panic Call", "parcel_option": "Parcel Option", "parcel_type": "PARCEL TYPE", "parcel_type_web": "Parcel Type", "parcel_types": "PARCEL TYPES", "password": "Password", "password_alphaNumeric_check": "Password required alphaNumeric", "password_blank_messege": "password not blank", "password_complexity_check": "Password complexity", "password_must_be_8-16_characters_long": "Password must be 8-16 Characters Long.", "password_must_contain_at_least_one_digit": "Password must contain at least one Digit.", "password_must_have_at_least_one_lowercase_character": "Password must have at least one Lowercase Character.", "password_must_have_at_least_one_uppercase_character": "Password must have at least one Uppercase Character.", "password_must_not_contain_whitespaces": "Password must not contain Whitespaces.", "past_booking_error": "Book Later is not available for Past DateTime or within next 15 mins.", "payWithCard": "Pay With Card", "pay_cash": "PAY WITH CASH", "pay_mode": "Payment Mode", "payable_ammount": "Payable Amount", "payaed_ammount": "Payed Amount", "payment": "Payment", "payment_cancelled": "Payment Cancelled", "payment_fail": "Your Payment Failed", "payment_gateway": "Payment Gateway", "payment_info": "Payment Info", "payment_mode": "PAYMENT MODE", "payment_mode_web": "Payment Mode", "payment_of": "Your Payment of ", "payment_settings": "Payment Settings", "payment_status": "Payment Status", "payment_thanks": "Thank you for your payment.", "paynow_button": "Pay Now", "pending": "Pending", "percentage": "Percentage", "personal_info": "Personal Info", "phone": "Phone", "phone_error_msg": "*Please write Phone Number with your country code. Ex.: +449876543210", "phone_no_update": "Please update your Phone number in the Edit Profile section before making a booking", "pickUpInstructions": "PICKUP INSTRUCTION", "pickUpInstructions_web": "Pickup Instruction", "pickup_address": "Pickup Address", "pickup_address_from_map": "Select pickup address from map", "pickup_location": "Pickup Location", "place_to_coords_error": "Place to Coordinate error. Please try again.", "port": "Port", "position": "List Position", "position_required": "Please Choose List position", "prepaid": "Prepaid", "previous_page_tooltip": "Previous Page", "privacy_policy": "Privacy Policy", "privacy_policy_change_privacy_para1": "We may update our Privacy Policy from time to time. Thus, you are advised to review this page periodically for any changes. We will notify you of any changes by posting the new Privacy Policy on this page.", "privacy_policy_change_privacy_para2": "This policy is effective as of 2023-12-12", "privacy_policy_children_para1": "These Services do not address anyone under the age of 13. We do not knowingly collect personally identifiable information from children under 13. In the case we discover that a child under 13 has provided us with personal information, we immediately delete this from our servers. If you are a parent or guardian and you are aware that your child has provided us with personal information, please contact us so that we will be able to do necessary actions.", "privacy_policy_contact_para1": "If you have any questions or suggestions about our Privacy Policy, do not hesitate to contact us at ", "privacy_policy_cookie_para1": "Cookies are files with a small amount of data that are commonly used as anonymous unique identifiers. These are sent to your browser from the websites that you visit and are stored on your device's internal memory.", "privacy_policy_cookie_para2": "This Service does not use these “cookies” explicitly. However, the app may use third party code and libraries that use “cookies” to collect information and improve their services. You have the option to either accept or refuse these cookies and know when a cookie is being sent to your device. If you choose to refuse our cookies, you may not be able to use some portions of this Service.", "privacy_policy_heading_change_privacy": "CHANGES TO THIS PRIVACY POLICY", "privacy_policy_heading_children": "CHILDREN’S PRIVACY", "privacy_policy_heading_contact": "CONTACT US", "privacy_policy_heading_cookie": "COOKIES", "privacy_policy_heading_info": "INFORMATION COLLECTION AND USE", "privacy_policy_heading_link": "LINKS TO OTHER SITES", "privacy_policy_heading_log": "LOG DATA", "privacy_policy_heading_security": "SECURITY", "privacy_policy_heading_service": "SERVICE PROVIDERS", "privacy_policy_info_list1": "First Name", "privacy_policy_info_list2": "Last Name", "privacy_policy_info_list3": "Email Address", "privacy_policy_info_list4": "Phone Number", "privacy_policy_info_list5": "Search vehicle location", "privacy_policy_info_list6": "Travel Route and Navigation", "privacy_policy_info_list7": "Real-time vehicle movement", "privacy_policy_info_para1": "For a better experience, while using our Service, we will require you to provide us with certain personally identifiable information, including but not limited to ", "privacy_policy_info_para2": "The information that we request will be retained by us and used as described in this privacy policy.", "privacy_policy_info_para3": "We do collect the following sensitive information when you use or Sign up on our App ", "privacy_policy_info_para4": "We collect your location data to enable", "privacy_policy_info_para5": "The app use third party services for social login like Google and Apple Login that collect information used to identify you. We capture User email and Name from social login if user has chosen it to be disclosed while performing a social login.", "privacy_policy_link_para1": "This Service may contain links to other sites. If you click on a third-party link, you will be directed to that site. Note that these external sites are not operated by us. Therefore, we strongly advise you to review the Privacy Policy of these websites. We have no control over and assume no responsibility for the content, privacy policies, or practices of any third-party sites or services.", "privacy_policy_log_para1": "We want to inform you that whenever you use our Service, in a case of an error in the app we collect data and information (through third party products) on your phone called Log Data. This Log Data may include information such as your device Internet Protocol (“IP”) address, device name, operating system version, the configuration of the app when utilizing our Service, the time and date of your use of the Service, and other statistics.", "privacy_policy_para1": "This page is used to inform visitors regarding our policies with the collection, use, and disclosure of Personal Information if anyone decided to use any of our Service.", "privacy_policy_para2": "If you choose to use our Service, then you agree to the collection and use of information in relation to this policy. The Personal Information that we collect is used for providing and improving the Service. We will not use or share your information with anyone except as described in this Privacy Policy.", "privacy_policy_para3": "The terms used in this Privacy Policy have the same meanings as in our Terms and Conditions, which is accessible at our all Apps unless otherwise defined in this Privacy Policy.", "privacy_policy_security_para1": "We value your trust in providing us your Personal Information, thus we are striving to use commercially acceptable means of protecting it. But remember that no method of transmission over the internet, or method of electronic storage is 100% secure and reliable, and we cannot guarantee its absolute security.", "privacy_policy_service_list1": "To facilitate our Service;", "privacy_policy_service_list2": "To provide the Service on our behalf;", "privacy_policy_service_list3": "To perform Service-related services; or", "privacy_policy_service_list4": "To assist us in analyzing how our Service is used.", "privacy_policy_service_para1": "We may employ third-party companies and individuals due to the following reasons:", "privacy_policy_service_para2": " We want to inform users of this Service that these third parties have access to your Personal Information. The reason is to perform the tasks assigned to them on our behalf. However, they are obligated not to disclose or use the information for any other purpose.", "processDate": "Process Date", "process_withdraw": "Process Withdraw", "processed": "Processed", "product_section_1": "You experience is important to us. We never compromise on our standards. Safety regulations are particularly emphasized to ensure peace of mind.", "product_section_2": "View live tracking, price estimations, and even book ahead of time! We have every avenue covered with every latest technology.", "product_section_3": "Driver can add many type of vehicles.He/She can set one vehicle as default for booking", "product_section_4": "Aside from cash, credit card, payments are PCI DSS compliant. The website has an SSL certification, and the apps are checked by the Apple Store and Google Play.", "product_section_heading": "Service Information", "product_section_para": "We provide the best App based services in the country.", "profile": "Profile", "profile_image": "Profile Image", "profile_incomplete": "Profile incomplete. Go to Profile Settings.", "profile_page_subtitle": "Profile Details", "profile_page_title": "My Profile", "profile_setting_menu": "Profile Settings", "profile_title": "PROFILE", "profile_updated": "Profile Updated.", "promo": "Promos", "promo_apply": "(Promo Apply)", "promo_code": "PROMO CODE", "promo_code_error": "Fill promo code", "promo_code_web": "Promo Code", "promo_description_error": "Fill promo description", "promo_discount": "Promo Discount", "promo_discount_type_error": "Fill promo discount type", "promo_discount_value": "Promo Value", "promo_discount_value_error": "Fill promo discount value", "promo_eligiblity": "Sorry! Promo is not valid as bill amount is lower.", "promo_exp": "Sorry! Promo has expired.", "promo_exp_limit": "Sorry! Promo usage limit over.", "promo_name": "Promo Name", "promo_name_error": "Fill promo name", "promo_not_found": "This promo code not found", "promo_offer": "Promo and Offers", "promo_offer_title": "PROMO AND OFFERS", "promo_usage": "Promo Count Available", "promo_used": "Sorry! You have already used the promo code.", "promo_used_by": "Promo Used By Count", "proper_bonus": "PLease enter proper bonus in numbers.", "proper_email": "Please enter email properly.", "proper_input_image": "Upload Proper Image", "proper_input_licenseimage": "Upload proper License Image", "proper_input_name": "Enter proper name", "proper_input_vehicleno": "Enter proper vehicle number", "proper_mobile": "Please enter mobile properly.", "provider_fetch_error": "Unable to fetch payment providers", "provider_not_found": "No Payment Providers Found.", "pruduct_section_heading_1": "Comfortable", "pruduct_section_heading_2": "Convenient", "pruduct_section_heading_3": "Vehicle Management", "pruduct_section_heading_4": "Secure", "push_error_1": "Failed to get push token for push notification!", "push_error_2": "Must use physical device for Push Notifications", "push_notification_title": "Push Notifications", "push_notifications": "Push Notifications", "push_notifications_title": "PUSH NOTIFICATIONS", "queue": "Busy", "rate_for": "Rate for", "rate_per_hour": "Rate Per Hour", "rate_per_hour_required": "Please Choose Rate Per Hour", "rate_per_unit_distance": "Distance Rate Per (Km or Mile)", "rate_per_unit_distance_required": "Please Choose Distance Rate Per (Km or Mile)", "rate_ride": "Rate Your Driver", "real_time_driver_section_text": "Drivers Realtime", "realtime_drivers": "Driver Live Location", "reason": "Reason", "receipt": "Receipt", "received_rating": "You received a X star rating", "refer_earn": "Refer & Earn", "referer_not_found": "Referer Id not found", "referralId": "Referral Id", "referral_bonus": "Referral Bonus", "referral_email_used": "This Email already used a referal Id", "referral_id_placeholder": "Referral Id (Optional)", "referral_number_used": "This Number already used a referal Id", "reg_error": "Error while SignUp", "register": "Register", "register_as_driver": "Register User?", "register_button": "Register Now", "registration_title": "Registration", "remove_promo": "Remove Promo", "report": "Reports", "requestDate": "Request Date", "request_otp": "Sign In With OTP", "request_payment": "Request Payment", "require_approval": "Your account requires approval from Admin", "ride_details_page_title": "Booking Details", "ride_information": "Ride Info", "ride_list_title": "My Bookings", "rider_cancel_text": "Booking Cancel", "rider_not_here": "NO REQUESTS AT THE MOMENT", "rider_withdraw": "Customer <PERSON>w", "riders": "Customers", "riders_title": "CUSTOMERS", "rides": "Rides", "roundTrip": "ROUND TRIP", "rows": "Rows", "save": "SAVE", "saved_address": "Saved Address", "search": "Search", "search_for_an_address": "Search for an address", "searching": "Searching", "secure": "Secure", "security_title": "Security Settings", "selectBid": "Select Bid", "select_car": "Select Vehicle Type", "select_country": "Select Country", "select_date": "Please Select Date", "select_driver": "Select Driver", "select_payment_getway": "Select Payment Getway", "select_proper": "Please select properly.", "select_reason": "Select Cancellation Reason", "select_user": "Select User", "send_button_text": "Send", "senderPersonPhone": "Booking Person Phone No", "service_off": "YOUR SERVICE STATUS IS 'OFF'", "service_start_soon": "Our service will start soon...", "set_decimal": "Set <PERSON>", "set_link_email": "Reset password link send to above mail", "settings_label3": "Booking OTP", "settings_label4": "<PERSON> Approval", "settings_label5": "Car Approval", "settings_label6": "Verify Id / Passport", "settings_title": "Settings", "share_msg": "Hi, Use this code while registering and get Bonus of ", "share_msg_no_bonus": "Hi, Check out this App. I am loving it.", "show_in_list": "Show in list", "show_live_route": "Show Live Route", "signIn": "SignIn", "signup": "Sign Up", "signup_via_referral": "SignUp Via Referral", "skip": "<PERSON><PERSON>", "smssettings": "SMS Settings", "smssettings_title": "SMS SETTINGS", "smtp_error": "Please check your SMTP details", "smtpsettings": "SMTP Settings", "smtpsettings_title": "SMTP SETTINGS", "social_login": "Social Login", "solved": "Solved", "sos": "Sos", "sos_title": "SOS", "spacer_message": "OR CONNECT WITH", "start_accept_bid": "You Have Receive One Bid", "start_trip": "START TRIP", "stops": "Stops", "subject": "Subject", "submit": "SUBMIT", "submit_rating": "SUBMIT RATING", "success": "Success", "success_payment": "Payment done successfully.", "swipe_symbol": "Swipe Symbol Direction", "system_price": "System Price", "system_price_with_bid": "System Price With Bid", "take_deliver_image": "DELIVERY IMAGE", "take_deliver_image_web": "Delivery Image", "take_pickup_image": "PICKUP IMAGE", "take_pickup_image_web": "Pickup Image", "task_list": "Home", "term_condition": "Terms & Conditions", "term_condition_heading1": "Acceptance of Terms :", "term_condition_heading10": "Termination :", "term_condition_heading11": "Governing Law :", "term_condition_heading12": "Amendments :", "term_condition_heading2": "Eligibility :", "term_condition_heading3": "Service Description :", "term_condition_heading4": "Payment :", "term_condition_heading5": "User Conduct :", "term_condition_heading6": "User Content :", "term_condition_heading7": "Intellectual Property :", "term_condition_heading8": "Disclaimers :", "term_condition_heading9": "Limitation of Liability :", "term_condition_para1": "Thank you for choosing our app-based booking services for taxi, cab, and delivery needs. Please read the following terms and conditions carefully before using our services:", "term_condition_para10": "In no event shall we be liable for any indirect, incidental, special, or consequential damages arising out of or in connection with the use of our services, whether or not we have been advised of the possibility of such damages.", "term_condition_para11": "We reserve the right to terminate a users access to our platform at any time, without notice, for any reason.", "term_condition_para12": "These Terms and Conditions shall be governed by and construed in accordance with the laws of the jurisdiction where the company is based.", "term_condition_para13": "We reserve the right to update these Terms and Conditions at any time, without notice. Users are advised to review these Terms and Conditions periodically to stay informed of any changes.", "term_condition_para14": "If you have any questions or concerns about these Terms and Conditions, please contact us at ", "term_condition_para2": "By using our services, you agree to be bound by these Terms and Conditions. If you do not agree to these Terms and Conditions, you may not use our services.", "term_condition_para3": "You must be at least 18 years old to use our services. By using our services, you represent and warrant that you are at least 18 years old.", "term_condition_para4": "Our app-based booking services provide a platform to connect users with taxi, cab, and delivery providers. We do not own, operate, or control any of the vehicles or drivers that use our platform. Our services are intended to be used for personal and non-commercial use only.", "term_condition_para5": "Users are required to pay for services provided through our platform. We accept various forms of payment, including credit cards, debit cards, and electronic payment methods. All payments made through our platform are subject to our payment policies, which may be updated from time to time.", "term_condition_para6": "Users are expected to use our services responsibly and not engage in any behavior that may harm our platform, drivers, or other users. Users are not permitted to use our platform for any unlawful or unauthorized purpose. Users are also prohibited from interfering with the operation of our platform or engaging in any activity that could compromise the security or integrity of our platform.", "term_condition_para7": "Users are solely responsible for the content they submit through our platform. By submitting content, users grant us a non-exclusive, royalty-free, transferable, and sublicensable license to use, modify, and reproduce the content for the purpose of providing our services.", "term_condition_para8": "Our platform, including all content and intellectual property, is owned by us and/or our licensors. Users may not copy, modify, distribute, or reproduce any of the content or intellectual property without our prior written consent.", "term_condition_para9": "We do not guarantee the availability, reliability, or quality of any services provided by drivers using our platform. We are not responsible for any loss or damage that may result from the use of our services. Our platform is provided 'as is' and without any warranties, express or implied.", "term_required": "Term Required", "terms": "Privacy Policy", "thanks": "Thanks For Rating", "this_month_text": "Month", "thismonth": "THIS MONTH", "thisyear": "THIS YEAR", "time": "TIME", "title": "Title", "today_text": "Today", "total": "Total", "total_cumtomer": "No. of Customer", "total_drivers": "No. of Drivers", "total_fare": "Total Fare", "total_time": "Total Time", "totalearning": "Total Earnings", "transaction_history_title": "WALLET TRANSACTION HISTORY", "transaction_id": "Transaction Id :", "tripInstructions": "TRIP INSTRUCTIONS", "trip_cost": "Trip Cost", "trip_cost_driver_share": "Driver Share", "trip_date_time": "Trip Date Time", "trip_end_time": "Trip End Time", "trip_instruction": "Trip Instruction", "trip_start_date": "Trip Start Date", "trip_start_time": "Trip Start Time", "try_again": "Please try again.", "type": "Type", "update_admin": "<PERSON><PERSON><PERSON>", "update_admin_title": "UPDATE ADMIN", "update_any": "You cannot update Email and Mobile at same time.", "update_button": "Update Now", "update_carType_title": "UPDATE CAR TYPE", "update_car_title": "UPDATE CAR", "update_customer_title": "UPDATE CUSTOMER", "update_driver_title": "UPDATE DRIVER", "update_failed": "Update Failed", "update_fleetAdmin": "Upadate Fleet Admin", "update_fleetAdmin_title": "UPDATE FLEET ADMIN", "update_profile_title": "Update Document", "update_promo_title": "UPDATE PROMO", "updated": "Updated", "upload_car_image": "Upload Car Image", "upload_driving_license": "Upload your Driving License", "upload_driving_license_back": "Upload your Driving License Back", "upload_driving_license_front": "Upload your Driving License Front", "upload_id_details": "Upload Your ID / Passport", "upload_profile_image": "Upload Profile Image", "upload_verifyIdImage": "Upload your ID / Passport Number", "use_distance_matrix": "Use Distance Matrix API", "use_wallet_balance": "Use Wallet Cash (Your balance is ", "user": "Users", "user_exists": "User with same Email or Mobile number exists.", "user_issue_contact_admin": "No profile data found. Please contact Admin", "user_type": "User Type", "username": "Username", "users_title": "Users", "usertype": "User Type", "valid_amount": "Please enter a valid amount", "value_for_money": "Value For Money", "vehicleMake_required": "Vehicle Make/Brand Name Required", "vehicleModel_required": "Vehicle Model Required", "vehicleNumber_required": "Vehicle Reg No. Required", "vehicle_make": "Make", "vehicle_model": "Model", "vehicle_model_name": "Vehicle Make / Brand Name", "vehicle_model_no": "Vehicle Model No", "vehicle_no": "Vehicle Number", "vehicle_number": "Number", "vehicle_reg_no": "Vehicle Registration Number", "verify_id": "Id / Passport Number", "verify_otp": "Verify OTP", "verifyid_error": "Sorry you have no ID / Passport", "verifyid_image": "Id / Passport Image", "wallet": "Wallet", "wallet_bal_low": "Wallet balance low.", "wallet_balance": "Wallet Balance", "wallet_balance_low": "Your wallet balance is too low.", "wallet_balance_threshold_reached": "Your Wallet Balance Threshold Reached", "wallet_balance_zero": "You Wallet Balance is 0. If you take Payment in Cash, the Admin Convenience Fee will get deducted from Wallet and your Wallet Balance will be in Negative after the Job. Negative balance will restrict further Jobs. You need to Recharge Wallet or Ask Admin for help.", "wallet_balance_zero_customer": "You wallet balance is negative. You are not allowed to book with negative balance", "wallet_ballance": "Wallet Balance", "wallet_booking_alert": "You cannot book another by using Wallet. You have an existing wallet booking.", "wallet_money_field": "Wallet Denominations", "wallet_payment_amount": "Wallet payment amount", "wallet_title": "Finance", "wallet_updated": "Wallet Updated successfully.", "wallet_zero": "Wallet Balance 0.", "was_successful": " was successful", "where_are_you": "You Are Here", "withdraw": "WITHDRAW", "withdraw_below_zero": "Withdraw amount cannot be less than or equal to 0.", "withdraw_money": "Withdraw Money", "withdraw_more": "Withdraw amount cannot be greater than Wallet Balance.", "withdrawn": "WITHDRAWN", "withdraws": "WITHDRAWS", "withdraws_web": "Withdraws", "within_min": "min", "work": "Work", "year": "Years", "yes": "Yes", "you_are_offline": "Your duty is off", "you_rated_text": "Driver Rating", "your_fare": "Your Fare", "your_feedback": "Your feedback (optional) ...", "your_feedback_test": "Your feedback will help us improve driving experience better.", "your_promo": "Your promo", "your_trip": "Your Trip"}, "langLocale": "en", "langName": "English", "tableData": {"id": 1}}}, "locations": {"UlGIEKCBAaaUZLD6EdE4DRRIK6R2": {"lat": 20.6857961, "lng": -103.3129397}, "r5Pd7zVyTLU0wxzkIPX1ASwW1qy2": {"lat": 20.6857463, "lng": -103.3129622}}, "payment_settings": {"braintree": {"active": false, "merchantId": "", "privateKey": "", "publicKey": "", "testing": false}, "culqi": {"PUBLIC_KEY": "", "SECURE_KEY": "", "active": false}, "flutterwave": {"FLUTTERWAVE_PUBLIC_KEY": "", "FLUTTERWAVE_SECRET_KEY": "", "active": false}, "iyzico": {"active": false, "apiKey": "", "secretKey": "", "testing": false}, "liqpay": {"active": false, "private_key": "", "public_key": ""}, "mercadopago": {"access_token": "", "active": false, "public_key": ""}, "payfast": {"PAYFAST_MERCHANT_KEY": "", "PAYFAST_MEWRCHANT_ID": "", "active": false, "testingMode": false}, "paymongo": {"active": false, "public_key": "", "secret_key": ""}, "paypal": {"active": false, "paypal_client_id": "", "paypal_secret": "", "testing": false}, "paystack": {"PAYSTACK_PUBLIC_KEY": "", "PAYSTACK_SECRET_KEY": "", "active": false}, "paytm": {"PAYTM_MERCHANT_KEY": "", "PAYTM_MEWRCHANT_ID": "", "active": false, "testing": false}, "payulatam": {"accountId": 0, "active": false, "apiKey": "", "merchantId": 0, "testing": false}, "razorpay": {"KEY_ID": "", "KEY_SECRET": "", "active": false}, "securepay": {"MERCHANT_CODE": "", "TXN_PASSWORD": "", "active": false, "testing": false}, "slickpay": {"active": false, "publicKey": "", "testing": false}, "squareup": {"ACCESS_TOKEN": "", "APPLICATION_ID": "", "LOCATION_ID": "", "active": false, "testing": false}, "stripe": {"active": false, "stripe_private_key": "", "stripe_public_key": ""}, "test": {"active": true}, "wipay": {"ACCOUNT_NO": "", "API_KEY": "", "active": false, "testing": false}}, "promos": {"promo1": {"max_promo_discount_value": 10, "min_order": 10, "promo_code": "ASDFG", "promo_description": "$10 for Testing", "promo_discount_type": "flat", "promo_discount_value": 10, "promo_name": "Test Promo", "promo_show": true, "promo_usage_limit": 100, "user_avail": 0}}, "settings": {"AllowCountrySelection": false, "AllowCriticalEditsAdmin": true, "AllowDeliveryPickupImageCapture": false, "AllowFinalDeliveryImageCapture": false, "AppleLoginEnabled": true, "AppleStoreLink": "https://apps.apple.com/", "CarHornRepeat": true, "CompanyAddress": "<PERSON>illo 890", "CompanyName": "Ballesteros TI", "CompanyPhone": "+************", "CompanyTermCondition": "https://exicube-test-delivery.web.app/term-condition", "CompanyTerms": "https://exicubedelivery.web.app/privacy-policy", "CompanyWebsite": "https://", "FacebookHandle": "https://facebook.com/", "FacebookLoginEnabled": true, "InstagramHandle": "", "PlayStoreLink": "https://play.google.com/", "RiderWithDraw": false, "TwitterHandle": "https://twitter.com/", "appName": "Ballesteros TI", "autoDispatch": false, "bank_fields": false, "bonus": 10, "bookingFlow": "1", "carType_required": true, "code": "MXN", "contact_email": "<EMAIL>", "convert_to_mile": false, "country": "Mexico", "customMobileOTP": false, "decimal": 2, "disable_cash": false, "disable_online": true, "driverRadius": 100, "driver_approval": false, "emailLogin": true, "horizontal_view": false, "imageIdApproval": false, "license_image_required": true, "mapLanguage": "en", "mobileLogin": false, "negativeBalance": false, "otp_secure": false, "prepaid": true, "realtime_drivers": true, "restrictCountry": "MX", "showLiveRoute": true, "socialLogin": false, "swipe_symbol": false, "symbol": "$", "term_required": true, "useDistanceMatrix": true}, "tracking": {"-OL-BXrKyhUfRWy3VNMb": {"-OL-CbaNK2YwEafL75MR": {"at": *************, "lat": 20.6856032, "lng": -103.3129741, "status": "ACCEPTED"}, "-OL-CfdDU0wsrqNsB1-1": {"at": *************, "lat": 20.6856775, "lng": -103.3128999, "status": "ACCEPTED"}, "-OL-Cv0cCZPy4yyOJFcu": {"at": *************, "lat": 20.6856658, "lng": -103.312997, "status": "ACCEPTED"}, "-OL-CyfhEDNtXs-eIOKc": {"at": *************, "lat": 20.6857575, "lng": -103.3129986, "status": "ACCEPTED"}, "-OL-D3Vg6bDD3UeOT0k-": {"at": *************, "lat": 20.6857334, "lng": -103.3129978, "status": "STARTED"}, "-OL-D6ynIekuvmR2YG_R": {"at": *************, "lat": 20.6856, "lng": -103.31306, "status": "STARTED"}, "-OL-DE30m9nFRPqPgaTG": {"at": *************, "lat": 20.6855107, "lng": -103.3130391, "status": "STARTED"}, "-OL-DIvJCdD3BUceAgdp": {"at": *************, "lat": 20.6856064, "lng": -103.3130363, "status": "STARTED"}, "-OL-DLqJxT8MhNnf9Mwa": {"at": *************, "lat": 20.6856884, "lng": -103.3129892, "status": "STARTED"}, "-OL-DT1thdUPluTOCokD": {"at": 1741613030102, "lat": 20.6857114, "lng": -103.3129792, "status": "REACHED"}}, "-OL-GCzTM8gbJx3h-rv3": {"-OL-S8ce4Vff5FBZd9V_": {"at": 1741616878695, "lat": 20.6854738, "lng": -103.3132699, "status": "ACCEPTED"}, "-OL-S9wl6meU8ZoCJga-": {"at": 1741616884077, "lat": 20.6853804, "lng": -103.313263, "status": "ACCEPTED"}, "-OL-SD6XFFufzfc0e3bq": {"at": 1741616897054, "lat": 20.6853433, "lng": -103.3133521, "status": "ACCEPTED"}, "-OL-SG2BV-g2uOYnepkB": {"at": 1741616909065, "lat": 20.6853223, "lng": -103.3133915, "status": "STARTED"}, "-OL-SK1CnVKOjoiaIx6l": {"at": 1741616925386, "lat": 20.6852292, "lng": -103.3133811, "status": "STARTED"}, "-OL-SMB2yXu_UCYdaako": {"at": 1741616934208, "lat": 20.6851457, "lng": -103.3134303, "status": "STARTED"}, "-OL-SOigbQWY_C7_FBSu": {"at": 1741616944617, "lat": 20.6850617, "lng": -103.3134796, "status": "STARTED"}, "-OL-SQQ_vVKu8mMrM284": {"at": 1741616951586, "lat": 20.6849713, "lng": -103.3134525, "status": "STARTED"}, "-OL-SST54a64UVcB-gnT": {"at": 1741616959939, "lat": 20.6848774, "lng": -103.3134666, "status": "STARTED"}, "-OL-SUo6hvf3mF-7ihAk": {"at": 1741616969540, "lat": 20.6848105, "lng": -103.3135321, "status": "STARTED"}, "-OL-SWpKTBDVoN9R2a11": {"at": 1741616977809, "lat": 20.6847294, "lng": -103.3135838, "status": "STARTED"}, "-OL-SYpcbU2_OCcpj6uZ": {"at": 1741616986021, "lat": 20.6846414, "lng": -103.3136189, "status": "STARTED"}, "-OL-SaRauZCMNbDZ8QJ3": {"at": 1741616996707, "lat": 20.6845719, "lng": -103.3136878, "status": "STARTED"}, "-OL-SbNnKIRmDNnYTe4A": {"at": 1741617000560, "lat": 20.6844798, "lng": -103.313679, "status": "STARTED"}, "-OL-Sd5U0wD3l_hiAEwJ": {"at": 1741617007580, "lat": 20.6843873, "lng": -103.3136521, "status": "STARTED"}, "-OL-Sd_nUHszehJrI8hI": {"at": 1741617009584, "lat": 20.6843698, "lng": -103.3136499, "status": "REACHED"}}, "-OL-wqfI7mD5dVrPFnOA": {"-OL-yFs73nhhAz6Nf9MZ": {"at": 1741625559139, "lat": 20.6861839, "lng": -103.3111156, "status": "ACCEPTED"}, "-OL-yNNeVG4P0hxpJhs4": {"at": 1741625589893, "lat": 20.6861717, "lng": -103.3110617, "status": "STARTED"}, "-OL-yS6-Qa4b6GRsF1C2": {"at": 1741625609243, "lat": 20.6860783, "lng": -103.3110735, "status": "STARTED"}, "-OL-yTssU-GzTBfTPD_1": {"at": 1741625616531, "lat": 20.6859928, "lng": -103.3111129, "status": "STARTED"}, "-OL-yVmLyQS8i8Eg2sAn": {"at": 1741625624305, "lat": 20.6859028, "lng": -103.3111664, "status": "STARTED"}, "-OL-yXg5gpqwipKJdh7T": {"at": 1741625632097, "lat": 20.6858141, "lng": -103.3111989, "status": "STARTED"}, "-OL-yZSQ591NL-3PwdGT": {"at": 1741625639350, "lat": 20.6857267, "lng": -103.3112324, "status": "STARTED"}, "-OL-yaDuZ5eBqvCATt-P": {"at": 1741625646613, "lat": 20.6856312, "lng": -103.3112572, "status": "STARTED"}, "-OL-yc7pqkewE01VHLqS": {"at": 1741625654416, "lat": 20.68553, "lng": -103.3112967, "status": "STARTED"}, "-OL-ydi0kAJk2qOB_bee": {"at": 1741625660892, "lat": 20.6854374, "lng": -103.311318, "status": "STARTED"}, "-OL-yfLzkyIn4kzSUMgh": {"at": 1741625667610, "lat": 20.6853495, "lng": -103.3113402, "status": "STARTED"}, "-OL-yhHfMOAjp0eqVFj5": {"at": 1741625675526, "lat": 20.6852608, "lng": -103.3113873, "status": "STARTED"}, "-OL-yjNJCn6DmlLn6Esl": {"at": 1741625684079, "lat": 20.685175, "lng": -103.3113515, "status": "STARTED"}, "-OL-ylRIFcJieMRK1W9m": {"at": 1741625692526, "lat": 20.6851457, "lng": -103.3112577, "status": "STARTED"}, "-OL-yn-4jnQUHmBWgqmA": {"at": 1741625698912, "lat": 20.6851, "lng": -103.3111711, "status": "STARTED"}, "-OL-yoMy8f5nXKiSu5oZ": {"at": 1741625704537, "lat": 20.6850652, "lng": -103.3110821, "status": "STARTED"}, "-OL-yqClWP_CaCWLnac_": {"at": 1741625712075, "lat": 20.6850385, "lng": -103.3109849, "status": "STARTED"}, "-OL-ys3FCMkXwaHnAsTq": {"at": 1741625719659, "lat": 20.6850175, "lng": -103.3108855, "status": "STARTED"}, "-OL-yuMIO_5ltJduWtvN": {"at": 1741625729070, "lat": 20.6850275, "lng": -103.3107827, "status": "STARTED"}, "-OL-ywCPQU6_VUfWfJsy": {"at": 1741625736629, "lat": 20.6849774, "lng": -103.3106935, "status": "STARTED"}, "-OL-yyAKA-BZHuviXPdX": {"at": 1741625744688, "lat": 20.6849654, "lng": -103.3105902, "status": "STARTED"}, "-OL-z4NE5vv-4p8G60R_": {"at": 1741625774185, "lat": 20.6849858, "lng": -103.3104927, "status": "STARTED"}, "-OL-z5rTYbc2hVYqRw3I": {"at": 1741625780281, "lat": 20.6849449, "lng": -103.3104054, "status": "STARTED"}, "-OL-z8C2aQ3JC1nydECW": {"at": 1741625789854, "lat": 20.6848858, "lng": -103.3103233, "status": "STARTED"}, "-OL-zA5nEtSHzc22hd-m": {"at": 1741625797646, "lat": 20.6848355, "lng": -103.3102396, "status": "STARTED"}, "-OL-zBpIm-Snj0DnGJrH": {"at": 1741625804718, "lat": 20.6847968, "lng": -103.3101474, "status": "STARTED"}, "-OL-zDWE2yezxXofeFk6": {"at": 1741625811626, "lat": 20.6847572, "lng": -103.31006, "status": "STARTED"}, "-OL-zEzVUUbOaN10Erf6": {"at": 1741625817659, "lat": 20.6847254, "lng": -103.3099655, "status": "STARTED"}, "-OL-zGnfwBxvmjyGe3cD": {"at": 1741625825094, "lat": 20.6847142, "lng": -103.309868, "status": "STARTED"}, "-OL-zIfbwpDuU7MUBsgs": {"at": 1741625832769, "lat": 20.6847036, "lng": -103.309765, "status": "STARTED"}, "-OL-zLKdh_czgD4KJxWm": {"at": 1741625843651, "lat": 20.684697, "lng": -103.3096681, "status": "STARTED"}, "-OL-zORFnWJVFww5xa4U": {"at": 1741625856363, "lat": 20.6847019, "lng": -103.3095601, "status": "STARTED"}, "-OL-zQC6DH27ddO9Wm4S": {"at": 1741625863585, "lat": 20.6846718, "lng": -103.309467, "status": "STARTED"}, "-OL-zRlZ7Gu0AW_kB7iB": {"at": 1741625870014, "lat": 20.6846451, "lng": -103.3093739, "status": "STARTED"}, "-OL-zTKruocdIPYsTy6B": {"at": 1741625876434, "lat": 20.684625, "lng": -103.3092723, "status": "STARTED"}, "-OL-zUshq7ar_gXvgjzz": {"at": 1741625882760, "lat": 20.6845921, "lng": -103.3091722, "status": "STARTED"}, "-OL-zWWt-u1tVQlbkKMo": {"at": 1741625889492, "lat": 20.6845701, "lng": -103.3090698, "status": "STARTED"}, "-OL-zYDsB1jDVC0Eb9BZ": {"at": 1741625896467, "lat": 20.6845435, "lng": -103.3089767, "status": "STARTED"}, "-OL-zZs00rj7vNQ4DeuX": {"at": 1741625903195, "lat": 20.6845085, "lng": -103.3088814, "status": "STARTED"}, "-OL-zaUaApFFVyenvjJY": {"at": 1741625909825, "lat": 20.6844757, "lng": -103.3087869, "status": "STARTED"}, "-OL-zc71zVwfvexKHaMZ": {"at": 1741625916509, "lat": 20.684433, "lng": -103.3086956, "status": "STARTED"}, "-OL-ze1phDYkzAFJb20w": {"at": 1741625924367, "lat": 20.6843924, "lng": -103.3086094, "status": "STARTED"}, "-OL-zfq_JMdxgnH_FMdM": {"at": 1741625931775, "lat": 20.6843581, "lng": -103.3085186, "status": "STARTED"}, "-OL-zhdUcQvpsLloJDLg": {"at": 1741625939129, "lat": 20.6843148, "lng": -103.3084315, "status": "STARTED"}, "-OL-zjC1jlku6W2-2lCj": {"at": 1741625945501, "lat": 20.6843092, "lng": -103.3083313, "status": "STARTED"}, "-OL-zkdYhk46eMK1HZ8x": {"at": 1741625951422, "lat": 20.6842803, "lng": -103.3082383, "status": "STARTED"}, "-OL-zmOsyO90CWd-P98v": {"at": 1741625958610, "lat": 20.6842331, "lng": -103.3081511, "status": "STARTED"}, "-OL-zo6a6p_Wkl3Ax1-M": {"at": 1741625965632, "lat": 20.6842048, "lng": -103.3080535, "status": "STARTED"}, "-OL-zpi69pbumTbNnmTC": {"at": 1741625972194, "lat": 20.6841641, "lng": -103.3079671, "status": "STARTED"}, "-OL-zrSLNbYUOwcDRWEF": {"at": 1741625979312, "lat": 20.6841272, "lng": -103.3078729, "status": "STARTED"}, "-OL-zsoMC3sfIi1Rqumt": {"at": 1741625984881, "lat": 20.6842036, "lng": -103.3078193, "status": "STARTED"}, "-OL-zu4yflUNpVCecGhr": {"at": 1741625990104, "lat": 20.6842767, "lng": -103.307761, "status": "STARTED"}, "-OL-zwrxFnPwY-aSH6Qw": {"at": 1741626001496, "lat": 20.6842095, "lng": -103.3076912, "status": "STARTED"}, "-OL-zybs-WxcqtALZsiZ": {"at": 1741626008658, "lat": 20.6841164, "lng": -103.307679, "status": "STARTED"}, "-OL0-032wGS6w6Y8dEB4": {"at": 1741626018653, "lat": 20.6840839, "lng": -103.3075829, "status": "STARTED"}, "-OL0-28rGBqvlwPAdMdQ": {"at": 1741626027218, "lat": 20.6840378, "lng": -103.3074935, "status": "STARTED"}, "-OL0-3e7MrX_yhv71a1t": {"at": 1741626033378, "lat": 20.6839925, "lng": -103.3074048, "status": "STARTED"}, "-OL0-5KUUHqWym60wmDG": {"at": 1741626040249, "lat": 20.6839472, "lng": -103.3073146, "status": "STARTED"}, "-OL0-760U4g2ZNlC7x0_": {"at": 1741626047516, "lat": 20.6838981, "lng": -103.3072236, "status": "STARTED"}, "-OL0-8iAxsgxe9q6Uzj6": {"at": 1741626054117, "lat": 20.6838748, "lng": -103.3071246, "status": "STARTED"}, "-OL0-ADp-2nZ5ECJ-Kmh": {"at": 1741626060304, "lat": 20.6838352, "lng": -103.3070316, "status": "STARTED"}, "-OL0-Bm8MvBuezPMw7-7": {"at": 1741626066660, "lat": 20.683814, "lng": -103.3069319, "status": "STARTED"}, "-OL0-DMEIGE_V2qb5h2R": {"at": 1741626073129, "lat": 20.6838132, "lng": -103.3068277, "status": "STARTED"}, "-OL0-FC6ngGHJbtq0Gh8": {"at": 1741626080674, "lat": 20.6838164, "lng": -103.3067223, "status": "STARTED"}, "-OL0-GXoVccZtjKI3Z_a": {"at": 1741626086158, "lat": 20.6837818, "lng": -103.3066243, "status": "STARTED"}, "-OL0-I7306f1kkZj7y8p": {"at": 1741626092638, "lat": 20.6837536, "lng": -103.306533, "status": "STARTED"}, "-OL0-JwSC-tdUutWbRT5": {"at": 1741626100088, "lat": 20.6837195, "lng": -103.3064391, "status": "STARTED"}, "-OL0-LXpkzWpGuKaZyuV": {"at": 1741626106640, "lat": 20.6837005, "lng": -103.30633, "status": "STARTED"}, "-OL0-NF1oiIZ2Ox6DYrb": {"at": 1741626113628, "lat": 20.6836686, "lng": -103.3062299, "status": "STARTED"}, "-OL0-OrOhM0G3gaFgrmP": {"at": 1741626120243, "lat": 20.6836302, "lng": -103.3061323, "status": "STARTED"}, "-OL0-QueqHmAJCEJyRQa": {"at": 1741626128645, "lat": 20.683589, "lng": -103.306046, "status": "STARTED"}, "-OL0-SXj8qzcEsuP3yuH": {"at": 1741626135306, "lat": 20.6836594, "lng": -103.3059819, "status": "STARTED"}, "-OL0-U-D79wiKt__2l6O": {"at": 1741626141288, "lat": 20.6837414, "lng": -103.3059236, "status": "STARTED"}, "-OL0-VnJikey7-AGLrZk": {"at": 1741626148655, "lat": 20.683829, "lng": -103.3058681, "status": "STARTED"}, "-OL0-XT1glQfJuvA2KLy": {"at": 1741626155484, "lat": 20.6839039, "lng": -103.3058101, "status": "STARTED"}, "-OL0-ZKALLMMJ0uqCar5": {"at": 1741626163109, "lat": 20.6840017, "lng": -103.305791, "status": "STARTED"}, "-OL0-_m9yIgLoRPSwAld": {"at": 1741626169059, "lat": 20.6840985, "lng": -103.3057785, "status": "STARTED"}, "-OL0-bOlQO10k94Cx_2X": {"at": 1741626175692, "lat": 20.6841857, "lng": -103.3057365, "status": "STARTED"}, "-OL0-d2CODNLJZKbb0Ix": {"at": 1741626182439, "lat": 20.6842786, "lng": -103.3057083, "status": "STARTED"}, "-OL0-eSUGxF47qBM29a4": {"at": 1741626188217, "lat": 20.6843688, "lng": -103.3057106, "status": "STARTED"}, "-OL0-fw_JSUP4eGrc60W": {"at": 1741626194303, "lat": 20.6844571, "lng": -103.3056659, "status": "STARTED"}, "-OL0-hcfNFJ-cn8zhtqm": {"at": 1741626201221, "lat": 20.6845427, "lng": -103.3056363, "status": "STARTED"}, "-OL0-jnoce5xxuwRUz6w": {"at": 1741626210127, "lat": 20.6846302, "lng": -103.3056104, "status": "STARTED"}, "-OL0-ldF6SEGeOX-iYO6": {"at": 1741626217643, "lat": 20.684725, "lng": -103.3055574, "status": "STARTED"}, "-OL0-npkuKLcXgY-sSqR": {"at": 1741626226634, "lat": 20.6848096, "lng": -103.3056044, "status": "STARTED"}, "-OL0-pmvInerSIDPgMtn": {"at": 1741626234645, "lat": 20.6848882, "lng": -103.3056741, "status": "STARTED"}, "-OL0-rfWz-mmwZTA0nd4": {"at": 1741626242363, "lat": 20.6849731, "lng": -103.3057184, "status": "STARTED"}, "-OL0-t1M4TNKgsgnIZfT": {"at": 1741626247921, "lat": 20.6850455, "lng": -103.305786, "status": "STARTED"}, "-OL0-ukF3Cf8uD6rPa3v": {"at": 1741626254955, "lat": 20.6851022, "lng": -103.3058644, "status": "STARTED"}, "-OL0-wQC10LT3JIA6V81": {"at": 1741626261800, "lat": 20.6851171, "lng": -103.3059624, "status": "STARTED"}, "-OL0-y51PqhP2WA1ol92": {"at": 1741626268637, "lat": 20.6851358, "lng": -103.3060686, "status": "STARTED"}, "-OL0-zgJBNaFYMdEEH7p": {"at": 1741626275183, "lat": 20.6851587, "lng": -103.3061667, "status": "STARTED"}, "-OL000Ob90Lpoo2vfzLz": {"at": 1741626282178, "lat": 20.6851843, "lng": -103.3062601, "status": "STARTED"}, "-OL004Mhw-W6KjM05hl-": {"at": 1741626298440, "lat": 20.6852386, "lng": -103.3063542, "status": "STARTED"}, "-OL005s8N5ZRxn-vN6eT": {"at": 1741626304611, "lat": 20.6852918, "lng": -103.306439, "status": "STARTED"}, "-OL007ZEZcQIfJGX4B6Q": {"at": 1741626311530, "lat": 20.6853121, "lng": -103.3065411, "status": "STARTED"}, "-OL009OdOlutSBex79Bu": {"at": 1741626319043, "lat": 20.6853247, "lng": -103.3066389, "status": "STARTED"}, "-OL00IYAHE64SKN43TIz": {"at": 1741626356518, "lat": 20.68534, "lng": -103.3067267, "status": "REACHED"}}, "-OL06mnlviTeVjzLa7Re": {"-OL0LWnKYCEy1hcrp3U2": {"at": 1741631921674, "lat": 20.760075, "lng": -103.3307317, "status": "ACCEPTED"}, "-OL0LWoVJro0B8kVublv": {"at": 1741631921749, "lat": 20.760075, "lng": -103.3307317, "status": "ACCEPTED"}, "-OL0Lkc8ArWkSkxLUN3N": {"at": 1741631982397, "lat": 20.7601706, "lng": -103.3307619, "status": "ACCEPTED"}, "-OL0LksEfVHSQ8OOkSkY": {"at": 1741631983428, "lat": 20.7601928, "lng": -103.3307722, "status": "ARRIVED"}, "-OL0NMm2QXJE4SCXBJFg": {"at": 1741632403259, "lat": 20.6857107, "lng": -103.3130264, "status": "STARTED"}, "-OL0NMv3uKcOuyBwnVta": {"at": 1741632405546, "lat": 20.764403, "lng": -103.3383117, "status": "STARTED"}, "-OL0O6EOPH7DMayadKwz": {"at": 1741632598812, "lat": 20.6837515, "lng": -103.3223714, "status": "REACHED"}}, "-OL1alYJR5_c1SYYyxmx": {"-OL1auwxwdUzq3L1NwZ_": {"at": 1741652996239, "lat": 20.6859594, "lng": -103.31262, "status": "ACCEPTED"}, "-OL1awyb_poadX2GRItc": {"at": 1741653004538, "lat": 20.6860554, "lng": -103.3126433, "status": "ACCEPTED"}, "-OL1b06diNLjDnc3zhdn": {"at": 1741653021499, "lat": 20.6860874, "lng": -103.3127363, "status": "ACCEPTED"}, "-OL1b1pdXigGTH8rzuFY": {"at": 1741653028540, "lat": 20.6861804, "lng": -103.3127309, "status": "ACCEPTED"}, "-OL1b3m6IwxPJyvEqiZm": {"at": 1741653036505, "lat": 20.6861043, "lng": -103.3126605, "status": "ACCEPTED"}, "-OL1b6DApVm0RWvw061J": {"at": 1741653046494, "lat": 20.6860647, "lng": -103.3127524, "status": "ACCEPTED"}, "-OL1bA7WN-V_maiIzFTD": {"at": 1741653062516, "lat": 20.6859731, "lng": -103.3127518, "status": "ACCEPTED"}, "-OL1bDYCecFL0znHvbBn": {"at": 1741653076512, "lat": 20.6858862, "lng": -103.3128183, "status": "ACCEPTED"}, "-OL1bHwXXiutYHGbANY7": {"at": 1741653094516, "lat": 20.685815, "lng": -103.3129353, "status": "ACCEPTED"}, "-OL1bHwhRgQCTZ3810Ks": {"at": 1741653094527, "lat": 20.685815, "lng": -103.3129353, "status": "ARRIVED"}, "-OL1bIkDdI2cYPafve3j": {"at": 1741653097825, "lat": 20.6858051, "lng": -103.3130068, "status": "STARTED"}, "-OL1dJC4Vxq4brqwY1Up": {"at": 1741653622136, "lat": 20.6833629, "lng": -103.313149, "status": "REACHED"}}, "-OL535EPJUf8X_wm8Jt9": {"-OL5UcLLYEDFKCXkgoTB": {"at": 1741718193861, "lat": 20.7599922, "lng": -103.3302931, "status": "ACCEPTED"}, "-OL5UeD5qTq3zW4-rSEC": {"at": 1741718201524, "lat": 20.7600022, "lng": -103.3303268, "status": "STARTED"}, "-OL5UnE61eDEz7wCRPPc": {"at": 1741718238452, "lat": 20.7599774, "lng": -103.3304773, "status": "STARTED"}, "-OL5UqPGanikPabd9rAz": {"at": 1741718251455, "lat": 20.7598823, "lng": -103.3305697, "status": "STARTED"}, "-OL5UtZg5HkeEbsd3w59": {"at": 1741718264399, "lat": 20.7599632, "lng": -103.3306486, "status": "STARTED"}, "-OL5UuIYOD_kkRFv9dLo": {"at": 1741718267409, "lat": 20.7600544, "lng": -103.330649, "status": "STARTED"}, "-OL5UvlnpKmDlubOmO1t": {"at": 1741718273442, "lat": 20.7601441, "lng": -103.3306842, "status": "STARTED"}, "-OL5Ux-5lSOgfWVcnUeK": {"at": 1741718278452, "lat": 20.7602551, "lng": -103.3307169, "status": "STARTED"}, "-OL5UxygWO0GRZmMlU6T": {"at": 1741718282458, "lat": 20.7603298, "lng": -103.3308002, "status": "STARTED"}, "-OL5UzC1QOAboB9dUyyn": {"at": 1741718287473, "lat": 20.760413, "lng": -103.3308561, "status": "STARTED"}, "-OL5V-dgtSLMOGPiJ5vE": {"at": 1741718293402, "lat": 20.7605113, "lng": -103.3308923, "status": "STARTED"}, "-OL5V0sWMoqGHZXTfVqA": {"at": 1741718298447, "lat": 20.76062, "lng": -103.3309401, "status": "STARTED"}, "-OL5V1aPnEhF2QBkVvjw": {"at": 1741718301385, "lat": 20.7607191, "lng": -103.3309665, "status": "STARTED"}, "-OL5V2_vF2jRUs6CK4K9": {"at": 1741718305450, "lat": 20.760833, "lng": -103.3309818, "status": "STARTED"}, "-OL5V3JfINjIWF4rej24": {"at": 1741718308442, "lat": 20.7609346, "lng": -103.3309832, "status": "STARTED"}, "-OL5V43ITh3hj9U4SWCT": {"at": 1741718311489, "lat": 20.7610592, "lng": -103.3309704, "status": "STARTED"}, "-OL5V4Y1hmC0MOK-okOx": {"at": 1741718313457, "lat": 20.7611491, "lng": -103.33096, "status": "STARTED"}, "-OL5V50oqCA4cjtvFzMA": {"at": 1741718315427, "lat": 20.7612432, "lng": -103.3309554, "status": "STARTED"}, "-OL5V5k_hKKYD6mLj6fM": {"at": 1741718318419, "lat": 20.7613384, "lng": -103.3309335, "status": "STARTED"}, "-OL5V7EPk8mcTkR-449E": {"at": 1741718324488, "lat": 20.7614396, "lng": -103.3308981, "status": "STARTED"}, "-OL5V7xTD2Ke38sjVF00": {"at": 1741718327436, "lat": 20.7615106, "lng": -103.3309781, "status": "STARTED"}, "-OL5V8Rtkw6WDUMLCWgG": {"at": 1741718329448, "lat": 20.7615582, "lng": -103.3310625, "status": "STARTED"}, "-OL5V8wUF83gZtwzuQxX": {"at": 1741718331469, "lat": 20.7616114, "lng": -103.3311599, "status": "STARTED"}, "-OL5V9QWzlZhz62Mu7mp": {"at": 1741718333455, "lat": 20.7616564, "lng": -103.3312527, "status": "STARTED"}, "-OL5V9vGilWYCPcFPXFA": {"at": 1741718335488, "lat": 20.7616921, "lng": -103.3313578, "status": "STARTED"}, "-OL5VAP5aA-JiwNC20oj": {"at": 1741718337461, "lat": 20.7617203, "lng": -103.3314608, "status": "STARTED"}, "-OL5VAtM8ZJPaYVVCCq6": {"at": 1741718339462, "lat": 20.7617446, "lng": -103.3315594, "status": "STARTED"}, "-OL5VBcjfPghsT4XlfTy": {"at": 1741718342494, "lat": 20.7617824, "lng": -103.3316921, "status": "STARTED"}, "-OL5VCM90LKLGR5_7x36": {"at": 1741718345464, "lat": 20.7618068, "lng": -103.331832, "status": "STARTED"}, "-OL5VCqDNoHscMrG6O1o": {"at": 1741718347453, "lat": 20.7618096, "lng": -103.3319295, "status": "STARTED"}, "-OL5VDLMlc7KE_IVdnu1": {"at": 1741718349509, "lat": 20.7618093, "lng": -103.332026, "status": "STARTED"}, "-OL5VE2VJ876pVi70cQ_": {"at": 1741718352399, "lat": 20.7618198, "lng": -103.3321624, "status": "STARTED"}, "-OL5VEnCk616plMWKkuv": {"at": 1741718355452, "lat": 20.7618149, "lng": -103.3322861, "status": "STARTED"}, "-OL5VFWkpv1ocABwd_oG": {"at": 1741718358430, "lat": 20.7618145, "lng": -103.3323993, "status": "STARTED"}, "-OL5VGG9QGx19YZe2_iF": {"at": 1741718361465, "lat": 20.7618039, "lng": -103.3325214, "status": "STARTED"}, "-OL5VH-3NtoPdsGOX3_h": {"at": 1741718364467, "lat": 20.7617988, "lng": -103.3326396, "status": "STARTED"}, "-OL5VHiuumAC3GlSqF0y": {"at": 1741718367465, "lat": 20.7618857, "lng": -103.3327085, "status": "STARTED"}, "-OL5VISohuthVpoSCa6O": {"at": 1741718370467, "lat": 20.7620074, "lng": -103.3327645, "status": "STARTED"}, "-OL5VJBBY_GhX99xf2Hx": {"at": 1741718373435, "lat": 20.7621205, "lng": -103.3328486, "status": "STARTED"}, "-OL5VJftJSfYw91U6Gtd": {"at": 1741718375464, "lat": 20.7621966, "lng": -103.3329012, "status": "STARTED"}, "-OL5VKATQPLigbrrsXsn": {"at": 1741718377485, "lat": 20.7622776, "lng": -103.3329551, "status": "STARTED"}, "-OL5VKeUde9Nv-eeiQqi": {"at": 1741718379469, "lat": 20.7623478, "lng": -103.3330708, "status": "STARTED"}, "-OL5VL8_uhMuBVo1nd7W": {"at": 1741718381460, "lat": 20.76239, "lng": -103.3332303, "status": "STARTED"}, "-OL5VLce0la_WakYbh_5": {"at": 1741718383449, "lat": 20.762426, "lng": -103.3333779, "status": "STARTED"}, "-OL5VM7Z3cxRMPh6g3xZ": {"at": 1741718385491, "lat": 20.7624483, "lng": -103.3334856, "status": "STARTED"}, "-OL5VPY7AozKYC556f-8": {"at": 1741718399478, "lat": 20.7624556, "lng": -103.3335921, "status": "STARTED"}, "-OL5VQ0qTBQUEe9MkX6V": {"at": 1741718401445, "lat": 20.7624809, "lng": -103.333687, "status": "STARTED"}, "-OL5VQWgSfYOu68KOneJ": {"at": 1741718403482, "lat": 20.7625095, "lng": -103.3338348, "status": "STARTED"}, "-OL5VR-RpggFAMeuoIJF": {"at": 1741718405451, "lat": 20.7625591, "lng": -103.3340286, "status": "STARTED"}, "-OL5VRF58WM4Q-22owIS": {"at": 1741718406453, "lat": 20.7625859, "lng": -103.334136, "status": "STARTED"}, "-OL5VRjSvEB_4WaSLxmf": {"at": 1741718408460, "lat": 20.762633, "lng": -103.3343094, "status": "STARTED"}, "-OL5VSDuiuHtsUogf9aq": {"at": 1741718410473, "lat": 20.7626692, "lng": -103.3344266, "status": "STARTED"}, "-OL5VSyEhx39ile7bLnN": {"at": 1741718413501, "lat": 20.7627134, "lng": -103.3345422, "status": "STARTED"}, "-OL5VTRu5SphPOLE4Zh9": {"at": 1741718415465, "lat": 20.7627423, "lng": -103.3346334, "status": "STARTED"}, "-OL5VTwdZkU1xcvQ8Ent": {"at": 1741718417496, "lat": 20.7627715, "lng": -103.3347317, "status": "STARTED"}, "-OL5VUepB5vu3Wmxxfjv": {"at": 1741718420452, "lat": 20.7628131, "lng": -103.3348242, "status": "STARTED"}, "-OL5VVdrZHqA7dT3wDZn": {"at": 1741718424486, "lat": 20.7628547, "lng": -103.334956, "status": "STARTED"}, "-OL5VW8Ftxbv0rjSGzZ2": {"at": 1741718426495, "lat": 20.7628981, "lng": -103.3350616, "status": "STARTED"}, "-OL5VWcIvc3pdI_Fx0Av": {"at": 1741718428481, "lat": 20.7629451, "lng": -103.3351563, "status": "STARTED"}, "-OL5VXMcV4Ln67v9ivG7": {"at": 1741718431511, "lat": 20.7629982, "lng": -103.3352818, "status": "STARTED"}, "-OL5VXqIQOoV4hpH9nO2": {"at": 1741718433473, "lat": 20.7630428, "lng": -103.335389, "status": "STARTED"}, "-OL5VYKYAILNQ_Mh0StR": {"at": 1741718435474, "lat": 20.7630845, "lng": -103.3354863, "status": "STARTED"}, "-OL5VZ3agOOPOA39QJf2": {"at": 1741718438479, "lat": 20.7631317, "lng": -103.335612, "status": "STARTED"}, "-OL5VZYvksA_EIhrVht6": {"at": 1741718440490, "lat": 20.7631705, "lng": -103.3357118, "status": "STARTED"}, "-OL5V_2-dnS90gpwdTic": {"at": 1741718442479, "lat": 20.7632065, "lng": -103.3358113, "status": "STARTED"}, "-OL5V_kTAeQ6934yGopy": {"at": 1741718445389, "lat": 20.7632365, "lng": -103.3359078, "status": "STARTED"}, "-OL5VajEBIxlxR4dNbH3": {"at": 1741718449406, "lat": 20.7632919, "lng": -103.3360163, "status": "STARTED"}, "-OL5VbThhy2z-03jSPyy": {"at": 1741718452443, "lat": 20.7633333, "lng": -103.3361258, "status": "STARTED"}, "-OL5Vc4diuHSW-q2G_MZ": {"at": 1741718455138, "lat": 20.7633788, "lng": -103.3362232, "status": "STARTED"}, "-OL5VcP41TDIIhMainD3": {"at": 1741718456444, "lat": 20.7634303, "lng": -103.3363192, "status": "STARTED"}, "-OL5VctrB-Y0X65XEOez": {"at": 1741718458479, "lat": 20.7634731, "lng": -103.3364124, "status": "STARTED"}, "-OL5VdOfqVOaRNjtV3uk": {"at": 1741718460516, "lat": 20.7635096, "lng": -103.3365032, "status": "STARTED"}, "-OL5Ve7W_iWw0heqojsd": {"at": 1741718463511, "lat": 20.7635667, "lng": -103.3366273, "status": "STARTED"}, "-OL5VebBUHkcEyWoI_i_": {"at": 1741718465476, "lat": 20.7636006, "lng": -103.3367176, "status": "STARTED"}, "-OL5Vf5Nmgzrxii4wP1-": {"at": 1741718467472, "lat": 20.7636286, "lng": -103.3368165, "status": "STARTED"}, "-OL5Vf_c7YZGhO6N6teA": {"at": 1741718469472, "lat": 20.763663, "lng": -103.3369104, "status": "STARTED"}, "-OL5Vg3l44XGdBSmqTjM": {"at": 1741718471465, "lat": 20.7637036, "lng": -103.3370055, "status": "STARTED"}, "-OL5VgZ6uoI-4k2pdaTh": {"at": 1741718473472, "lat": 20.7637581, "lng": -103.3371121, "status": "STARTED"}, "-OL5Vh2RFWEMrj7XKEv4": {"at": 1741718475477, "lat": 20.7638101, "lng": -103.3372233, "status": "STARTED"}, "-OL5VhXKkDr2i9MNY8U2": {"at": 1741718477453, "lat": 20.7638537, "lng": -103.337346, "status": "STARTED"}, "-OL5Vi0ezqtGUjg-9GOk": {"at": 1741718479459, "lat": 20.7639035, "lng": -103.33746, "status": "STARTED"}, "-OL5ViVwXs5MfM_--Bjq": {"at": 1741718481460, "lat": 20.7639633, "lng": -103.3375917, "status": "STARTED"}, "-OL5Vj0KpoJFMvyn1Qzt": {"at": 1741718483530, "lat": 20.7640287, "lng": -103.3377146, "status": "STARTED"}, "-OL5VjUuTdTrpFoxDDx6": {"at": 1741718485490, "lat": 20.7640752, "lng": -103.3378001, "status": "STARTED"}, "-OL5VjzxSEz__yax0aMU": {"at": 1741718487541, "lat": 20.7641189, "lng": -103.3378852, "status": "STARTED"}, "-OL5Vki6t6tUup1dxp1V": {"at": 1741718490496, "lat": 20.7641753, "lng": -103.3379655, "status": "STARTED"}, "-OL5VlC-vWzcUCRaenpP": {"at": 1741718492472, "lat": 20.7642318, "lng": -103.3380449, "status": "STARTED"}, "-OL5Vlfv0xQFmmjg80OQ": {"at": 1741718494451, "lat": 20.7642933, "lng": -103.3381605, "status": "STARTED"}, "-OL5VmABoGA9jDwqIqF0": {"at": 1741718496453, "lat": 20.7643542, "lng": -103.3382537, "status": "STARTED"}, "-OL5Vmuh5Ptq691ZtbeT": {"at": 1741718499493, "lat": 20.7644307, "lng": -103.3383739, "status": "STARTED"}, "-OL5VnNm6llBaq6RQ54M": {"at": 1741718501418, "lat": 20.7644836, "lng": -103.3384849, "status": "STARTED"}, "-OL5VnsRU_y3YuIkRhY7": {"at": 1741718503444, "lat": 20.7645462, "lng": -103.3385824, "status": "STARTED"}, "-OL5VoNBXhxMDuQe3MCo": {"at": 1741718505476, "lat": 20.7645915, "lng": -103.338669, "status": "STARTED"}, "-OL5Vp6np6cVbwI-ZY8X": {"at": 1741718508522, "lat": 20.7646464, "lng": -103.3387725, "status": "STARTED"}, "-OL5Vpq4ktpsHX4D_q6_": {"at": 1741718511485, "lat": 20.7647055, "lng": -103.3388604, "status": "STARTED"}, "-OL5W-4imw2w5PrO5AHK": {"at": 1741718553447, "lat": 20.7647754, "lng": -103.3389745, "status": "STARTED"}, "-OL5W-oZ4bJn-wvw9or2": {"at": 1741718556444, "lat": 20.7648399, "lng": -103.339055, "status": "STARTED"}, "-OL5W0Z9M2Up4HeIn0LO": {"at": 1741718559490, "lat": 20.7648997, "lng": -103.3391866, "status": "STARTED"}, "-OL5W11ynSLBfcNxuHMq": {"at": 1741718561463, "lat": 20.7649792, "lng": -103.3393195, "status": "STARTED"}, "-OL5W1XL2m-mrx4Y9h7H": {"at": 1741718563469, "lat": 20.7650605, "lng": -103.339431, "status": "STARTED"}, "-OL5W20Ki5cVogVPrLBb": {"at": 1741718565454, "lat": 20.7651096, "lng": -103.3395223, "status": "STARTED"}, "-OL5WRcs5o8y_nEnQ1YN": {"at": 1741718670384, "lat": 20.7651869, "lng": -103.3395802, "status": "STARTED"}, "-OL5WScRE8-J9xNYFrdr": {"at": 1741718674453, "lat": 20.7650779, "lng": -103.3397, "status": "STARTED"}, "-OL5WT6WMSK1IgcsOtTU": {"at": 1741718676441, "lat": 20.7649457, "lng": -103.3397555, "status": "STARTED"}, "-OL5WTb0MdSBJ2tLzZoM": {"at": 1741718678456, "lat": 20.7647999, "lng": -103.3397842, "status": "STARTED"}, "-OL5WU5TInjrRwp5C_mw": {"at": 1741718680470, "lat": 20.7646125, "lng": -103.3397863, "status": "STARTED"}, "-OL5WUM5RkexvG_eqg_H": {"at": 1741718681535, "lat": 20.7645005, "lng": -103.3397822, "status": "STARTED"}, "-OL5WU_B2eigf0Jynta2": {"at": 1741718682437, "lat": 20.7643779, "lng": -103.3397688, "status": "STARTED"}, "-OL5WUolBGhyLOBMoOPK": {"at": 1741718683433, "lat": 20.7642477, "lng": -103.3397504, "status": "STARTED"}, "-OL5WV3WzxXAWXr4Ch-D": {"at": 1741718684442, "lat": 20.764112, "lng": -103.3397333, "status": "STARTED"}, "-OL5WVJDnb0v2x2izcAa": {"at": 1741718685447, "lat": 20.7639846, "lng": -103.3397186, "status": "STARTED"}, "-OL5WVYD-fnUdgQCr1Ob": {"at": 1741718686406, "lat": 20.7638659, "lng": -103.3396996, "status": "STARTED"}, "-OL5WVn2LbVT9632X-mM": {"at": 1741718687419, "lat": 20.7637436, "lng": -103.3396779, "status": "STARTED"}, "-OL5WW1PnKxgRhlgeisy": {"at": 1741718688403, "lat": 20.7636194, "lng": -103.3396592, "status": "STARTED"}, "-OL5WWH5oCrJW_TjMp43": {"at": 1741718689405, "lat": 20.7634994, "lng": -103.3396367, "status": "STARTED"}, "-OL5WWWrvI3BB-Jiivvc": {"at": 1741718690416, "lat": 20.7633806, "lng": -103.3396181, "status": "STARTED"}, "-OL5WWlcx4qkwcK5lbg6": {"at": 1741718691422, "lat": 20.7632593, "lng": -103.3395906, "status": "STARTED"}, "-OL5WX0zOJ7S-S8ZQtlP": {"at": 1741718692470, "lat": 20.7631372, "lng": -103.3395631, "status": "STARTED"}, "-OL5WXGPHjYrFHubbDbM": {"at": 1741718693458, "lat": 20.7630154, "lng": -103.3395321, "status": "STARTED"}, "-OL5WXVvFdjaPnobrOFs": {"at": 1741718694451, "lat": 20.7628818, "lng": -103.3395065, "status": "STARTED"}, "-OL5WXjxVzYpIPzwijub": {"at": 1741718695414, "lat": 20.7627363, "lng": -103.3394837, "status": "STARTED"}, "-OL5WY-Aj3VKu7fsNlhG": {"at": 1741718696452, "lat": 20.7625889, "lng": -103.3394633, "status": "STARTED"}, "-OL5WYEaV9d1kmDLHF4b": {"at": 1741718697438, "lat": 20.7624337, "lng": -103.3394473, "status": "STARTED"}, "-OL5WYUz1tpb0wBb6gW6": {"at": 1741718698488, "lat": 20.7622727, "lng": -103.3394316, "status": "STARTED"}, "-OL5WYjHGTSFkoMHiT4E": {"at": 1741718699467, "lat": 20.7620824, "lng": -103.3394167, "status": "STARTED"}, "-OL5WYydafs7wA_PPfJ3": {"at": 1741718700449, "lat": 20.761903, "lng": -103.3394093, "status": "STARTED"}, "-OL5WZDHwtfx91UqZtFd": {"at": 1741718701450, "lat": 20.7617336, "lng": -103.3394185, "status": "STARTED"}, "-OL5WZT2Tnj0utRisSx2": {"at": 1741718702459, "lat": 20.7615697, "lng": -103.3394372, "status": "STARTED"}, "-OL5WZh7WK9f1mHLGSK-": {"at": 1741718703423, "lat": 20.761406, "lng": -103.339487, "status": "STARTED"}, "-OL5WZx8JQqKRnetJ8oG": {"at": 1741718704450, "lat": 20.7612417, "lng": -103.3395523, "status": "STARTED"}, "-OL5W_Bv4JC7L-gNDn7E": {"at": 1741718705460, "lat": 20.7610882, "lng": -103.3396188, "status": "STARTED"}, "-OL5W_RITsV8stIEUQQD": {"at": 1741718706442, "lat": 20.760934, "lng": -103.3396955, "status": "STARTED"}, "-OL5W_fp5Qgh0c27GHUs": {"at": 1741718707438, "lat": 20.7607911, "lng": -103.3397776, "status": "STARTED"}, "-OL5W_vhtjqLG9o4We19": {"at": 1741718708454, "lat": 20.7606542, "lng": -103.339866, "status": "STARTED"}, "-OL5WaAfplO_TV7GgBE4": {"at": 1741718709476, "lat": 20.7605158, "lng": -103.3399655, "status": "STARTED"}, "-OL5WaQIQFqBWQoWjrQt": {"at": 1741718710476, "lat": 20.7603831, "lng": -103.3400789, "status": "STARTED"}, "-OL5Wae8jZHfScWtPB8w": {"at": 1741718711426, "lat": 20.7602633, "lng": -103.3402016, "status": "STARTED"}, "-OL5WauN3wIJT157XqgW": {"at": 1741718712465, "lat": 20.7601427, "lng": -103.3403323, "status": "STARTED"}, "-OL5Wb8tX2QxmOIq6htf": {"at": 1741718713457, "lat": 20.7600331, "lng": -103.3404504, "status": "STARTED"}, "-OL5WbOSUFR8Wb-yGb7G": {"at": 1741718714454, "lat": 20.7599361, "lng": -103.340583, "status": "STARTED"}, "-OL5Wbd1LDM4x6fEMJVK": {"at": 1741718715451, "lat": 20.7598437, "lng": -103.340734, "status": "STARTED"}, "-OL5WbsnHyjeD-V0IIuU": {"at": 1741718716460, "lat": 20.7597563, "lng": -103.3408876, "status": "STARTED"}, "-OL5Wc78zKPTDCmXBSGv": {"at": 1741718717442, "lat": 20.7596875, "lng": -103.3410354, "status": "STARTED"}, "-OL5WcMwIC4pyVqlyAKI": {"at": 1741718718451, "lat": 20.7596065, "lng": -103.3411984, "status": "STARTED"}, "-OL5WcbfSoURMcc_sScN": {"at": 1741718719460, "lat": 20.7595218, "lng": -103.3413647, "status": "STARTED"}, "-OL5WcrgsKzOfjDk7Y-n": {"at": 1741718720484, "lat": 20.7594298, "lng": -103.341522, "status": "STARTED"}, "-OL5Wd5ywG0JJuEIaieW": {"at": 1741718721463, "lat": 20.7593357, "lng": -103.3416822, "status": "STARTED"}, "-OL5WdM-xxsRgD6x9WJT": {"at": 1741718722489, "lat": 20.7592489, "lng": -103.3418052, "status": "STARTED"}, "-OL5Wda8x2ylRCbJa8mO": {"at": 1741718723458, "lat": 20.7591522, "lng": -103.3419432, "status": "STARTED"}, "-OL5WdpjEKKlRQ8fdUhm": {"at": 1741718724455, "lat": 20.7590411, "lng": -103.3420761, "status": "STARTED"}, "-OL5We4AneIpO43PcAiM": {"at": 1741718725443, "lat": 20.7589312, "lng": -103.3422129, "status": "STARTED"}, "-OL5WeJC6vYmizv2YrqV": {"at": 1741718726405, "lat": 20.7588172, "lng": -103.3423263, "status": "STARTED"}, "-OL5WeZnNuOvc-AVqUPE": {"at": 1741718727468, "lat": 20.7586923, "lng": -103.3424307, "status": "STARTED"}, "-OL5Weod1JX0na_53va_": {"at": 1741718728479, "lat": 20.7585506, "lng": -103.3425223, "status": "STARTED"}, "-OL5Wf2e3d1EaHKmL_1P": {"at": 1741718729443, "lat": 20.7584024, "lng": -103.342622, "status": "STARTED"}, "-OL5WfJ9YD1vNy4brfHy": {"at": 1741718730499, "lat": 20.7582713, "lng": -103.3427235, "status": "STARTED"}, "-OL5WfYNjDc2IhBpIJ9a": {"at": 1741718731472, "lat": 20.7581428, "lng": -103.3427983, "status": "STARTED"}, "-OL5WfmBEW0N25SH2xfg": {"at": 1741718732420, "lat": 20.7579927, "lng": -103.3428755, "status": "STARTED"}, "-OL5Wg0tSxevnfyLv3Ls": {"at": 1741718733426, "lat": 20.757841, "lng": -103.3429389, "status": "STARTED"}, "-OL5WgHIkUpP8-igo8Xv": {"at": 1741718734476, "lat": 20.7576888, "lng": -103.3429962, "status": "STARTED"}, "-OL5WgX0oUxOTQNvS8Y7": {"at": 1741718735482, "lat": 20.7575454, "lng": -103.3430418, "status": "STARTED"}, "-OL5WgkfJuYKqQ5ZR9IT": {"at": 1741718736420, "lat": 20.7574502, "lng": -103.3430888, "status": "STARTED"}, "-OL5Wh0-BcAHqWbzmGoy": {"at": 1741718737464, "lat": 20.7573496, "lng": -103.3431176, "status": "STARTED"}, "-OL5WhVWN-EIsv0Nccrb": {"at": 1741718739482, "lat": 20.7571768, "lng": -103.3431403, "status": "STARTED"}, "-OL5WhjMadJ4_EnM1Zod": {"at": 1741718740431, "lat": 20.7570862, "lng": -103.3431484, "status": "STARTED"}, "-OL5Whz2bLAdhX4YpyaT": {"at": 1741718741436, "lat": 20.7569924, "lng": -103.3431522, "status": "STARTED"}, "-OL5WiDR5uW-9S6tgpWG": {"at": 1741718742420, "lat": 20.7569015, "lng": -103.3431549, "status": "STARTED"}, "-OL5WiTBL0pVquipz0HZ": {"at": 1741718743429, "lat": 20.7568099, "lng": -103.3431583, "status": "STARTED"}, "-OL5WixCmvuonoFrTzXh": {"at": 1741718745414, "lat": 20.7566393, "lng": -103.3431595, "status": "STARTED"}, "-OL5WjScWuYW67J3EhZi": {"at": 1741718747489, "lat": 20.7564851, "lng": -103.3431609, "status": "STARTED"}, "-OL5Wjvu5rL7emIp1-cx": {"at": 1741718749427, "lat": 20.7563182, "lng": -103.3431628, "status": "STARTED"}, "-OL5WkAIgRnNj-U4MWSA": {"at": 1741718750411, "lat": 20.7562284, "lng": -103.3431567, "status": "STARTED"}, "-OL5WkQMD2ThlwyoC-Na": {"at": 1741718751440, "lat": 20.7561368, "lng": -103.3431323, "status": "STARTED"}, "-OL5WkfT3zHTfbPeuOBq": {"at": 1741718752470, "lat": 20.7560375, "lng": -103.3431163, "status": "STARTED"}, "-OL5WkumYwmZxHAhX2Ar": {"at": 1741718753450, "lat": 20.7559234, "lng": -103.3430979, "status": "STARTED"}, "-OL5Wl9_9S2Cndl9SvNk": {"at": 1741718754462, "lat": 20.7558128, "lng": -103.3430833, "status": "STARTED"}, "-OL5WlOf0tYbv6X2i-1W": {"at": 1741718755428, "lat": 20.7556967, "lng": -103.3430686, "status": "STARTED"}, "-OL5Wldht60KOvHWsjJG": {"at": 1741718756454, "lat": 20.7555827, "lng": -103.3430585, "status": "STARTED"}, "-OL5WltUHMkjXuhL0tA7": {"at": 1741718757464, "lat": 20.7554657, "lng": -103.3430524, "status": "STARTED"}, "-OL5Wm8U9isQ14EW7Rh3": {"at": 1741718758487, "lat": 20.7553398, "lng": -103.3430429, "status": "STARTED"}, "-OL5WmNOJVY1BXeDOJI_": {"at": 1741718759441, "lat": 20.7552083, "lng": -103.3430294, "status": "STARTED"}, "-OL5WmcMfA_Korx1POWH": {"at": 1741718760464, "lat": 20.7550719, "lng": -103.3430235, "status": "STARTED"}, "-OL5WmrP_SuCqNADfrGf": {"at": 1741718761426, "lat": 20.7549275, "lng": -103.3430318, "status": "STARTED"}, "-OL5Wn6J9UlrQ-t_S1uz": {"at": 1741718762444, "lat": 20.7547702, "lng": -103.343042, "status": "STARTED"}, "-OL5WnLHyX4q2VyghX1g": {"at": 1741718763402, "lat": 20.7546087, "lng": -103.3430476, "status": "STARTED"}, "-OL5WnaXxM32aL4RFzj7": {"at": 1741718764443, "lat": 20.7544409, "lng": -103.3430444, "status": "STARTED"}, "-OL5Wnq0poA_oTEt-k75": {"at": 1741718765434, "lat": 20.7542762, "lng": -103.3430502, "status": "STARTED"}, "-OL5Wo4WD_5SLKdBbkGq": {"at": 1741718766426, "lat": 20.7541152, "lng": -103.3430652, "status": "STARTED"}, "-OL5WoK8qA4NCyOX_nXa": {"at": 1741718767426, "lat": 20.7539349, "lng": -103.3431249, "status": "STARTED"}, "-OL5Wo_Gne_HLA9lZbNP": {"at": 1741718768458, "lat": 20.7537683, "lng": -103.3431843, "status": "STARTED"}, "-OL5WonzRTsL8kySwoyj": {"at": 1741718769399, "lat": 20.7536109, "lng": -103.343232, "status": "STARTED"}, "-OL5Wp3C-sWEyMF2Dv9p": {"at": 1741718770437, "lat": 20.753459, "lng": -103.3432839, "status": "STARTED"}, "-OL5WpJB4yvAKiaSnJC6": {"at": 1741718771461, "lat": 20.753325, "lng": -103.3433499, "status": "STARTED"}, "-OL5WpY12qHLaMJwCIwE": {"at": 1741718772410, "lat": 20.7531854, "lng": -103.3434128, "status": "STARTED"}, "-OL5Wpo-nfSQtClOJtVe": {"at": 1741718773496, "lat": 20.75305, "lng": -103.343476, "status": "STARTED"}, "-OL5Wq2Fdn15_3-ieWFb": {"at": 1741718774473, "lat": 20.7529036, "lng": -103.343528, "status": "STARTED"}, "-OL5WqHb8edFN7Jed1PT": {"at": 1741718775456, "lat": 20.7527567, "lng": -103.3435715, "status": "STARTED"}, "-OL5WqX7i7k7Q8B8cnBA": {"at": 1741718776449, "lat": 20.7526179, "lng": -103.3436141, "status": "STARTED"}, "-OL5WqlvFwOQwH9dZvva": {"at": 1741718777459, "lat": 20.7524731, "lng": -103.3436495, "status": "STARTED"}, "-OL5Wr05FZjkJKp0sh3Z": {"at": 1741718778430, "lat": 20.7523165, "lng": -103.3436738, "status": "STARTED"}, "-OL5WrG-UHXY_rDruO4g": {"at": 1741718779449, "lat": 20.7521571, "lng": -103.3436841, "status": "STARTED"}, "-OL5WrVo0JYQzB92a8pr": {"at": 1741718780461, "lat": 20.7520024, "lng": -103.3436759, "status": "STARTED"}, "-OL5Wrk86Jq2ukVb7h-V": {"at": 1741718781441, "lat": 20.7518468, "lng": -103.3436606, "status": "STARTED"}, "-OL5WrznzTD0gKHw-50H": {"at": 1741718782443, "lat": 20.7516875, "lng": -103.3436353, "status": "STARTED"}, "-OL5WsFDPpnSo0046Wfv": {"at": 1741718783494, "lat": 20.7515292, "lng": -103.3435949, "status": "STARTED"}, "-OL5WsUU1Rx6Fno_v41P": {"at": 1741718784470, "lat": 20.751381, "lng": -103.3435469, "status": "STARTED"}, "-OL5WsjMPUKSpSwvQazS": {"at": 1741718785488, "lat": 20.7512439, "lng": -103.3434934, "status": "STARTED"}, "-OL5Wsy3Xk0h4nBWtCas": {"at": 1741718786429, "lat": 20.7510926, "lng": -103.3434433, "status": "STARTED"}, "-OL5WtCfpAvc4wcTFNz1": {"at": 1741718787427, "lat": 20.7509716, "lng": -103.3433914, "status": "STARTED"}, "-OL5WtSYGAewTNRDzFkS": {"at": 1741718788443, "lat": 20.7508527, "lng": -103.3433295, "status": "STARTED"}, "-OL5WthGbQUcxX7LJomw": {"at": 1741718789450, "lat": 20.7507391, "lng": -103.3432743, "status": "STARTED"}, "-OL5WtxAiBadmJbjelnn": {"at": 1741718790468, "lat": 20.7506296, "lng": -103.3432279, "status": "STARTED"}, "-OL5WuAc06ESgwOiDWer": {"at": 1741718791392, "lat": 20.7505101, "lng": -103.3431839, "status": "STARTED"}, "-OL5WuRMZtuI9KxFiPeA": {"at": 1741718792463, "lat": 20.7503939, "lng": -103.3431292, "status": "STARTED"}, "-OL5WufSa_-WkowWjwQR": {"at": 1741718793430, "lat": 20.7502697, "lng": -103.3430736, "status": "STARTED"}, "-OL5Wuvs346zJHEuQ2hg": {"at": 1741718794480, "lat": 20.750142, "lng": -103.3430239, "status": "STARTED"}, "-OL5Wv9WMtGS8g1xMqPw": {"at": 1741718795418, "lat": 20.750016, "lng": -103.3429711, "status": "STARTED"}, "-OL5WvPzwXS8IgnBmhrp": {"at": 1741718796471, "lat": 20.7498824, "lng": -103.3429225, "status": "STARTED"}, "-OL5WveUaoG_8DS-12N-": {"at": 1741718797464, "lat": 20.7497457, "lng": -103.3428799, "status": "STARTED"}, "-OL5WvtmcZtYGGZBluwc": {"at": 1741718798443, "lat": 20.7496115, "lng": -103.3428621, "status": "STARTED"}, "-OL5Ww8NX0cHyf30qOzC": {"at": 1741718799440, "lat": 20.7494677, "lng": -103.3428567, "status": "STARTED"}, "-OL5WwOBmPaPGBeumNN3": {"at": 1741718800453, "lat": 20.749328, "lng": -103.3428496, "status": "STARTED"}, "-OL5WwctVGcjhKkj6RIn": {"at": 1741718801458, "lat": 20.7491884, "lng": -103.3428437, "status": "STARTED"}, "-OL5Wws22H6e04kuhDvD": {"at": 1741718802427, "lat": 20.749057, "lng": -103.3428506, "status": "STARTED"}, "-OL5Wx7A5437QBXcEl2-": {"at": 1741718803460, "lat": 20.7489241, "lng": -103.3428699, "status": "STARTED"}, "-OL5WxMsL2k-24sK6mCk": {"at": 1741718804464, "lat": 20.7487736, "lng": -103.3428747, "status": "STARTED"}, "-OL5Wxb1zWkixOzGWN7d": {"at": 1741718805435, "lat": 20.748614, "lng": -103.342886, "status": "STARTED"}, "-OL5Wxrbcx0-AS70sYLL": {"at": 1741718806496, "lat": 20.7484738, "lng": -103.3429243, "status": "STARTED"}, "-OL5Wy5S3ZuqD3J7g_Bj": {"at": 1741718807446, "lat": 20.7483312, "lng": -103.3429755, "status": "STARTED"}, "-OL5WyLNUgJFVjcI9ZEN": {"at": 1741718808464, "lat": 20.7481931, "lng": -103.3430272, "status": "STARTED"}, "-OL5Wya9IEWFpLwng_SI": {"at": 1741718809474, "lat": 20.7480391, "lng": -103.3430976, "status": "STARTED"}, "-OL5WypPQCXP6bq0ECDm": {"at": 1741718810451, "lat": 20.7478812, "lng": -103.3431906, "status": "STARTED"}, "-OL5Wz47wW0_HAcMJNqe": {"at": 1741718811457, "lat": 20.7477118, "lng": -103.3432934, "status": "STARTED"}, "-OL5WzJyvd9f-MdQ5BK-": {"at": 1741718812471, "lat": 20.7475806, "lng": -103.3434009, "status": "STARTED"}, "-OL5WzZ6fqkStpNt7ysi": {"at": 1741718813439, "lat": 20.7474318, "lng": -103.3435133, "status": "STARTED"}, "-OL5WznWjyEJ56otPoeK": {"at": 1741718814426, "lat": 20.7472926, "lng": -103.3436342, "status": "STARTED"}, "-OL5X-2kzdbe3nOuTZ23": {"at": 1741718815465, "lat": 20.7471545, "lng": -103.3437511, "status": "STARTED"}, "-OL5X-IPOELIYqQyDgWq": {"at": 1741718816466, "lat": 20.747041, "lng": -103.3438926, "status": "STARTED"}, "-OL5X-XtQGIWs1cxlk8I": {"at": 1741718817458, "lat": 20.7469428, "lng": -103.3440526, "status": "STARTED"}, "-OL5X-mnO9czYOrblgIu": {"at": 1741718818476, "lat": 20.7468486, "lng": -103.3442022, "status": "STARTED"}, "-OL5X01CWeWS_RiBdzE3": {"at": 1741718819462, "lat": 20.7467611, "lng": -103.3443448, "status": "STARTED"}, "-OL5X0Gc9-tQjknSqxOl": {"at": 1741718820449, "lat": 20.7466737, "lng": -103.3444798, "status": "STARTED"}, "-OL5X0VywZXxj2F-jKzn": {"at": 1741718821429, "lat": 20.7465894, "lng": -103.3446359, "status": "STARTED"}, "-OL5X0kSbuTrRfe-Hoe8": {"at": 1741718822421, "lat": 20.7464942, "lng": -103.344791, "status": "STARTED"}, "-OL5X1-Nj6u7Xu9uq-1B": {"at": 1741718823435, "lat": 20.7464019, "lng": -103.3449356, "status": "STARTED"}, "-OL5X1EnK_vJRN41zow_": {"at": 1741718824428, "lat": 20.7463142, "lng": -103.3450624, "status": "STARTED"}, "-OL5X1UPQFrZPqpIf1j7": {"at": 1741718825427, "lat": 20.746229, "lng": -103.3451789, "status": "STARTED"}, "-OL5X1jOle9132yHdT6w": {"at": 1741718826450, "lat": 20.7461365, "lng": -103.345294, "status": "STARTED"}, "-OL5X1yn3Xl1lJPaDkFY": {"at": 1741718827436, "lat": 20.746031, "lng": -103.3454076, "status": "STARTED"}, "-OL5X2DDicwErrrw0U4s": {"at": 1741718828423, "lat": 20.7459303, "lng": -103.3455101, "status": "STARTED"}, "-OL5X2TDewBTaCLMpQTy": {"at": 1741718829446, "lat": 20.7458481, "lng": -103.3456041, "status": "STARTED"}, "-OL5X2hqpoB-d7sn88MU": {"at": 1741718830445, "lat": 20.7457479, "lng": -103.345689, "status": "STARTED"}, "-OL5X2xElhnWx_BK7Eqy": {"at": 1741718831432, "lat": 20.7456388, "lng": -103.3457693, "status": "STARTED"}, "-OL5X3C4GGxscDUrTDg7": {"at": 1741718832445, "lat": 20.745533, "lng": -103.3458274, "status": "STARTED"}, "-OL5X3RoGWt3PQNya1Id": {"at": 1741718833452, "lat": 20.7454351, "lng": -103.3458818, "status": "STARTED"}, "-OL5X3gLyQL9nqi9tnoB": {"at": 1741718834447, "lat": 20.7453253, "lng": -103.3459317, "status": "STARTED"}, "-OL5X3wGNzpiccJFx4M2": {"at": 1741718835466, "lat": 20.7452244, "lng": -103.3459877, "status": "STARTED"}, "-OL5X4AsoroJBE_tfY27": {"at": 1741718836465, "lat": 20.7451268, "lng": -103.346052, "status": "STARTED"}, "-OL5X4QjDAECQiiOleNH": {"at": 1741718837478, "lat": 20.7450252, "lng": -103.3461187, "status": "STARTED"}, "-OL5X4eJJVMYVFSXzB2y": {"at": 1741718838413, "lat": 20.7449299, "lng": -103.3461765, "status": "STARTED"}, "-OL5X4v-ERQDsuiqNi1h": {"at": 1741718839480, "lat": 20.7448385, "lng": -103.3462279, "status": "STARTED"}, "-OL5X58t2ZCril_mXAD8": {"at": 1741718840432, "lat": 20.7447383, "lng": -103.3462765, "status": "STARTED"}, "-OL5X5OYR9uaNnkDOTZb": {"at": 1741718841436, "lat": 20.7446165, "lng": -103.3463265, "status": "STARTED"}, "-OL5X5dZg-GHsShaNjHy": {"at": 1741718842461, "lat": 20.7444536, "lng": -103.3463603, "status": "STARTED"}, "-OL5X5t6cAGiIcH9n7KM": {"at": 1741718843455, "lat": 20.7442996, "lng": -103.3463661, "status": "STARTED"}, "-OL5X67gTLBtJGY-ZVP4": {"at": 1741718844452, "lat": 20.7441497, "lng": -103.3463855, "status": "STARTED"}, "-OL5X6Mt9TMMX_wLuXrt": {"at": 1741718845426, "lat": 20.7440084, "lng": -103.3464168, "status": "STARTED"}, "-OL5X6bc9MhXOccrqfxf": {"at": 1741718846433, "lat": 20.7438582, "lng": -103.3464362, "status": "STARTED"}, "-OL5X6rSYvPLc0CCFa93": {"at": 1741718847445, "lat": 20.7437034, "lng": -103.3464482, "status": "STARTED"}, "-OL5X75Ro8CARnLqsrE5": {"at": 1741718848405, "lat": 20.7435387, "lng": -103.3464629, "status": "STARTED"}, "-OL5X7LC7KqJyKCBR86Q": {"at": 1741718849414, "lat": 20.7433667, "lng": -103.3464727, "status": "STARTED"}, "-OL5X7a1utN1dM-dKVIC": {"at": 1741718850426, "lat": 20.7432044, "lng": -103.3464573, "status": "STARTED"}, "-OL5X7pLrCiEUOoIHAB0": {"at": 1741718851406, "lat": 20.743042, "lng": -103.3464159, "status": "STARTED"}, "-OL5X84A-wSia4kil28T": {"at": 1741718852420, "lat": 20.7428778, "lng": -103.3463744, "status": "STARTED"}, "-OL5X8K8u6iS1_V9lORl": {"at": 1741718853442, "lat": 20.7427132, "lng": -103.3463201, "status": "STARTED"}, "-OL5X8ZvnDr3_j5RiBfu": {"at": 1741718854451, "lat": 20.7425601, "lng": -103.3462725, "status": "STARTED"}, "-OL5X8ooRVbxpqBx0BhJ": {"at": 1741718855468, "lat": 20.7424113, "lng": -103.3462078, "status": "STARTED"}, "-OL5X943sQbo7XjaIqBA": {"at": 1741718856508, "lat": 20.7422642, "lng": -103.3461484, "status": "STARTED"}, "-OL5X9IdnQ0OI3f-0B3U": {"at": 1741718857441, "lat": 20.7421244, "lng": -103.3460894, "status": "STARTED"}, "-OL5X9XnJqKWXfHrZZxJ": {"at": 1741718858411, "lat": 20.7419851, "lng": -103.346015, "status": "STARTED"}, "-OL5X9mffRMGmZgTnqXT": {"at": 1741718859427, "lat": 20.7418439, "lng": -103.3459225, "status": "STARTED"}, "-OL5XA1fnLezIplBkLgL": {"at": 1741718860452, "lat": 20.7417079, "lng": -103.3458254, "status": "STARTED"}, "-OL5XAHiYjKOWfDjNnFb": {"at": 1741718861479, "lat": 20.7415778, "lng": -103.3457097, "status": "STARTED"}, "-OL5XAWU75Rn2MD89tAI": {"at": 1741718862424, "lat": 20.7414599, "lng": -103.3455921, "status": "STARTED"}, "-OL5XAlM6CnYcp5pFSQx": {"at": 1741718863440, "lat": 20.7413377, "lng": -103.3454723, "status": "STARTED"}, "-OL5XB-l3ImObRKdZLpf": {"at": 1741718864425, "lat": 20.7412188, "lng": -103.3453507, "status": "STARTED"}, "-OL5XBGI_no7RKOXiYNg": {"at": 1741718865483, "lat": 20.7411073, "lng": -103.3452325, "status": "STARTED"}, "-OL5XBVS3SyZzQF8qH-E": {"at": 1741718866451, "lat": 20.7409968, "lng": -103.3451066, "status": "STARTED"}, "-OL5XBjymCYQZUw6Uxrf": {"at": 1741718867446, "lat": 20.7408816, "lng": -103.3449896, "status": "STARTED"}, "-OL5XC-KXZVCWzxOk0cv": {"at": 1741718868493, "lat": 20.7407645, "lng": -103.3448791, "status": "STARTED"}, "-OL5XCDxrRh82-PQYxNo": {"at": 1741718869429, "lat": 20.7406535, "lng": -103.3447687, "status": "STARTED"}, "-OL5XCTdcldse1Rt05LS": {"at": 1741718870433, "lat": 20.740553, "lng": -103.3446639, "status": "STARTED"}, "-OL5XChov8sqN6cTimpj": {"at": 1741718871404, "lat": 20.7404504, "lng": -103.3445487, "status": "STARTED"}, "-OL5XCz8EFiDxRLjVf8u": {"at": 1741718872514, "lat": 20.7403559, "lng": -103.3444262, "status": "STARTED"}, "-OL5XDCSHtAlbphhbkqu": {"at": 1741718873429, "lat": 20.7402549, "lng": -103.3443042, "status": "STARTED"}, "-OL5XDST72kLWsDwSJU3": {"at": 1741718874454, "lat": 20.7401448, "lng": -103.3441879, "status": "STARTED"}, "-OL5XDhIlQLVkLn-prMc": {"at": 1741718875467, "lat": 20.7400328, "lng": -103.3440757, "status": "STARTED"}, "-OL5XDwHDlrRmDhBdsZ7": {"at": 1741718876427, "lat": 20.7399291, "lng": -103.3439612, "status": "STARTED"}, "-OL5XEBUHPsvVdOt737A": {"at": 1741718877463, "lat": 20.7398262, "lng": -103.343865, "status": "STARTED"}, "-OL5XERI45RB5mA5yKUq": {"at": 1741718878475, "lat": 20.7397205, "lng": -103.3437746, "status": "STARTED"}, "-OL5XEfln6wRgvIGSLjm": {"at": 1741718879466, "lat": 20.7396168, "lng": -103.3436882, "status": "STARTED"}, "-OL5XEvUKn5BHMCcwkLi": {"at": 1741718880472, "lat": 20.7395102, "lng": -103.3435942, "status": "STARTED"}, "-OL5XF9zkbB880Kjw44K": {"at": 1741718881464, "lat": 20.7394067, "lng": -103.3434826, "status": "STARTED"}, "-OL5XFPM_z1oUHYEJELG": {"at": 1741718882447, "lat": 20.7393003, "lng": -103.3433777, "status": "STARTED"}, "-OL5XFeJNc54BY97WH1_": {"at": 1741718883468, "lat": 20.7391879, "lng": -103.3432759, "status": "STARTED"}, "-OL5XFtbzCBNrGI_TCBm": {"at": 1741718884448, "lat": 20.7390836, "lng": -103.3431841, "status": "STARTED"}, "-OL5XG8Fb4LA06ynkJnB": {"at": 1741718885448, "lat": 20.7389882, "lng": -103.3430813, "status": "STARTED"}, "-OL5XGO4-jnqYdTuRfY1": {"at": 1741718886461, "lat": 20.7388752, "lng": -103.3429888, "status": "STARTED"}, "-OL5XGcWt7kdmUqi0ZLZ": {"at": 1741718887450, "lat": 20.7387689, "lng": -103.342888, "status": "STARTED"}, "-OL5XGsMWkdUAArpL0zj": {"at": 1741718888463, "lat": 20.738655, "lng": -103.3427948, "status": "STARTED"}, "-OL5XH7MSRqpnVZw6oAp": {"at": 1741718889488, "lat": 20.7385299, "lng": -103.3427167, "status": "STARTED"}, "-OL5XHN15ewn93k8DZOC": {"at": 1741718890491, "lat": 20.7384043, "lng": -103.3426301, "status": "STARTED"}, "-OL5XHauJSw2g0WGIEg1": {"at": 1741718891442, "lat": 20.7382819, "lng": -103.3425465, "status": "STARTED"}, "-OL5XHqvHb43Cq-rpoKU": {"at": 1741718892467, "lat": 20.7381717, "lng": -103.3424668, "status": "STARTED"}, "-OL5XI4tivVWM7TlhT0V": {"at": 1741718893426, "lat": 20.7380405, "lng": -103.3424113, "status": "STARTED"}, "-OL5XIKUaFlv7RfZ1kbS": {"at": 1741718894423, "lat": 20.7379067, "lng": -103.3423566, "status": "STARTED"}, "-OL5XI_RnXc0PvFJ93rX": {"at": 1741718895444, "lat": 20.7377901, "lng": -103.3422988, "status": "STARTED"}, "-OL5XIp6mnWP2Q53N7ih": {"at": 1741718896448, "lat": 20.7376499, "lng": -103.3422402, "status": "STARTED"}, "-OL5XJ3ls3F5iIpfVwkL": {"at": 1741718897449, "lat": 20.7375033, "lng": -103.342172, "status": "STARTED"}, "-OL5XJJo01qOU78ARNLb": {"at": 1741718898477, "lat": 20.7373601, "lng": -103.342121, "status": "STARTED"}, "-OL5XJYxgE-pB-1-8Exa": {"at": 1741718899445, "lat": 20.7372097, "lng": -103.3420777, "status": "STARTED"}, "-OL5XJnd1qowr6KFIEiv": {"at": 1741718900449, "lat": 20.7370574, "lng": -103.3420511, "status": "STARTED"}, "-OL5XK2v7RETZ8UJqeEK": {"at": 1741718901492, "lat": 20.7369065, "lng": -103.3420321, "status": "STARTED"}, "-OL5XKJ977qnY0aXE7rb": {"at": 1741718902530, "lat": 20.7367642, "lng": -103.3420055, "status": "STARTED"}, "-OL5XKYeEAXSHYEEdCVe": {"at": 1741718903522, "lat": 20.7366149, "lng": -103.3420088, "status": "STARTED"}, "-OL5XKn385OVB2rPut5I": {"at": 1741718904509, "lat": 20.7364636, "lng": -103.3420146, "status": "STARTED"}, "-OL5XL0xFdzwP667h3vc": {"at": 1741718905462, "lat": 20.7363133, "lng": -103.342011, "status": "STARTED"}, "-OL5XLG8H0F0ShT7froN": {"at": 1741718906433, "lat": 20.7361583, "lng": -103.3420079, "status": "STARTED"}, "-OL5XLWnYUW6iUpZYRsg": {"at": 1741718907500, "lat": 20.7360007, "lng": -103.3420212, "status": "STARTED"}, "-OL5XLkc4hHHIWNg_QIz": {"at": 1741718908448, "lat": 20.7358497, "lng": -103.3420613, "status": "STARTED"}, "-OL5XM-sCDI4Xs-RxtMX": {"at": 1741718909488, "lat": 20.7357002, "lng": -103.3420926, "status": "STARTED"}, "-OL5XMFCQHmaE8KMH3AP": {"at": 1741718910470, "lat": 20.735562, "lng": -103.3421248, "status": "STARTED"}, "-OL5XMUG5hZz2I34dajH": {"at": 1741718911430, "lat": 20.7354322, "lng": -103.342163, "status": "STARTED"}, "-OL5XMiY8mm0I5T-_moR": {"at": 1741718912412, "lat": 20.7352997, "lng": -103.3422039, "status": "STARTED"}, "-OL5XMyheMMGRINoC7W4": {"at": 1741718913445, "lat": 20.7351675, "lng": -103.342247, "status": "STARTED"}, "-OL5XNDh4CqH4ylQgPbu": {"at": 1741718914470, "lat": 20.7350316, "lng": -103.3422949, "status": "STARTED"}, "-OL5XNTGxHTBiWyuHB5I": {"at": 1741718915465, "lat": 20.7348952, "lng": -103.3423426, "status": "STARTED"}, "-OL5XNiHKjsJ779SnNk0": {"at": 1741718916490, "lat": 20.7347521, "lng": -103.3423904, "status": "STARTED"}, "-OL5XNxPLMvd0W2UxH0G": {"at": 1741718917459, "lat": 20.7346117, "lng": -103.3424386, "status": "STARTED"}, "-OL5XOBxOznGRXRJr_il": {"at": 1741718918453, "lat": 20.7344782, "lng": -103.3424842, "status": "STARTED"}, "-OL5XORpTx9xOTyMu94X": {"at": 1741718919470, "lat": 20.7343453, "lng": -103.3425521, "status": "STARTED"}, "-OL5XOgCBZW95Uf6mE5F": {"at": 1741718920454, "lat": 20.7342191, "lng": -103.3426, "status": "STARTED"}, "-OL5XOw9vPMlFhg5AVQc": {"at": 1741718921475, "lat": 20.7340957, "lng": -103.3426375, "status": "STARTED"}, "-OL5XPASeX7Tp0EUKfpn": {"at": 1741718922454, "lat": 20.7339686, "lng": -103.3426759, "status": "STARTED"}, "-OL5XPP_FwiLGYcOjp76": {"at": 1741718923422, "lat": 20.7338429, "lng": -103.3427112, "status": "STARTED"}, "-OL5XPepafVhYuL0dm4B": {"at": 1741718924460, "lat": 20.7337201, "lng": -103.3427484, "status": "STARTED"}, "-OL5XPuWf2bsrMCnlo6i": {"at": 1741718925466, "lat": 20.73361, "lng": -103.3427928, "status": "STARTED"}, "-OL5XQ98tQ0_PdcJd87R": {"at": 1741718926466, "lat": 20.7335104, "lng": -103.3428317, "status": "STARTED"}, "-OL5XQPAwFOi9QwfWv78": {"at": 1741718927492, "lat": 20.7334118, "lng": -103.3428625, "status": "STARTED"}, "-OL5XQdRfVfjKRGM3B7f": {"at": 1741718928466, "lat": 20.7333202, "lng": -103.3428956, "status": "STARTED"}, "-OL5XQsyj3R6JVP4XIPS": {"at": 1741718929462, "lat": 20.7332326, "lng": -103.3429306, "status": "STARTED"}, "-OL5XR7lF3nif23nY0m_": {"at": 1741718930473, "lat": 20.7331479, "lng": -103.3429653, "status": "STARTED"}, "-OL5XRbjdttemni6w-lV": {"at": 1741718932456, "lat": 20.7329941, "lng": -103.3430182, "status": "STARTED"}, "-OL5XS6BgMqvqDr__QUq": {"at": 1741718934468, "lat": 20.7328854, "lng": -103.3430351, "status": "STARTED"}, "-OL5XcommztfHdBEfnsi": {"at": 1741718982442, "lat": 20.7327935, "lng": -103.3430502, "status": "STARTED"}, "-OL5XfkzbYXyJrBJHs80": {"at": 1741718994488, "lat": 20.7326864, "lng": -103.3431008, "status": "STARTED"}, "-OL5XgEowneICKwCx4hd": {"at": 1741718996460, "lat": 20.7325675, "lng": -103.343135, "status": "STARTED"}, "-OL5XgjJyeVY6lhm3WQi": {"at": 1741718998477, "lat": 20.7324324, "lng": -103.3431797, "status": "STARTED"}, "-OL5XhDZmVXUW3C9we8X": {"at": 1741719000476, "lat": 20.7322695, "lng": -103.3432353, "status": "STARTED"}, "-OL5XhSzrmZjdo9V3uS6": {"at": 1741719001463, "lat": 20.732178, "lng": -103.3432689, "status": "STARTED"}, "-OL5XhhpKIZacUuNxysd": {"at": 1741719002478, "lat": 20.7320885, "lng": -103.3433024, "status": "STARTED"}, "-OL5XhxOzqH_OWwk1G-5": {"at": 1741719003474, "lat": 20.731994, "lng": -103.3433289, "status": "STARTED"}, "-OL5XiBiyDD4de9_cVHn": {"at": 1741719004455, "lat": 20.7319006, "lng": -103.3433567, "status": "STARTED"}, "-OL5XiSHUf2iFpKlSw_B": {"at": 1741719005515, "lat": 20.7318029, "lng": -103.3433849, "status": "STARTED"}, "-OL5Xig2W8Oy9IMElW6z": {"at": 1741719006460, "lat": 20.7317086, "lng": -103.3434159, "status": "STARTED"}, "-OL5XivcS9k0J8Za9zWl": {"at": 1741719007457, "lat": 20.7316102, "lng": -103.3434459, "status": "STARTED"}, "-OL5XjAy3FS9qt5Z_ckG": {"at": 1741719008496, "lat": 20.731501, "lng": -103.3434797, "status": "STARTED"}, "-OL5XjQBRZnzbfJt-x4B": {"at": 1741719009476, "lat": 20.7313954, "lng": -103.3435191, "status": "STARTED"}, "-OL5XjefQs1zRDxGKayV": {"at": 1741719010468, "lat": 20.7312863, "lng": -103.3435596, "status": "STARTED"}, "-OL5XjtyQo5LZxtOtPcV": {"at": 1741719011447, "lat": 20.7311748, "lng": -103.3435998, "status": "STARTED"}, "-OL5Xk8m8_JO02LWLMoX": {"at": 1741719012458, "lat": 20.731055, "lng": -103.343638, "status": "STARTED"}, "-OL5XkOGpk_5UI46PSI9": {"at": 1741719013449, "lat": 20.7309379, "lng": -103.3436735, "status": "STARTED"}, "-OL5Xkd-iCU3hywtBTvv": {"at": 1741719014456, "lat": 20.7308177, "lng": -103.3437045, "status": "STARTED"}, "-OL5XksM93XFHpHcYYAN": {"at": 1741719015440, "lat": 20.7307011, "lng": -103.3437345, "status": "STARTED"}, "-OL5Xl76BpoFMB6v1K0t": {"at": 1741719016447, "lat": 20.7305934, "lng": -103.3437685, "status": "STARTED"}, "-OL5XlMT5T6ClKBQo49o": {"at": 1741719017430, "lat": 20.7304899, "lng": -103.3438015, "status": "STARTED"}, "-OL5XlbfO2z7L77C3TNS": {"at": 1741719018467, "lat": 20.7303884, "lng": -103.3438321, "status": "STARTED"}, "-OL5Xlr6SNX5l0Gin7gk": {"at": 1741719019455, "lat": 20.7302913, "lng": -103.3438622, "status": "STARTED"}, "-OL5Xm5iMjXTZtP9GkWZ": {"at": 1741719020455, "lat": 20.7301964, "lng": -103.3438833, "status": "STARTED"}, "-OL5XmLnSnaXFIeFPn9u": {"at": 1741719021484, "lat": 20.7301033, "lng": -103.3439089, "status": "STARTED"}, "-OL5Xm_VVYoWW7R6DRbO": {"at": 1741719022424, "lat": 20.7300162, "lng": -103.3439499, "status": "STARTED"}, "-OL5XmqwDBUJeLrgDeUA": {"at": 1741719023541, "lat": 20.7299208, "lng": -103.343989, "status": "STARTED"}, "-OL5Xn4UcL9GSvnevzhO": {"at": 1741719024472, "lat": 20.7298206, "lng": -103.3440219, "status": "STARTED"}, "-OL5XnJG0b1GgugPrRQR": {"at": 1741719025418, "lat": 20.7297204, "lng": -103.3440509, "status": "STARTED"}, "-OL5Xn_WBOIdVB09on-X": {"at": 1741719026522, "lat": 20.7296264, "lng": -103.3440831, "status": "STARTED"}, "-OL5Xo3JyoaDcAlOPHbO": {"at": 1741719028492, "lat": 20.7294782, "lng": -103.3441367, "status": "STARTED"}, "-OL5XoXnPi6I34JCBVZr": {"at": 1741719030444, "lat": 20.7293683, "lng": -103.3441705, "status": "STARTED"}, "-OL5YByDxCycsmt8Fijj": {"at": 1741719130502, "lat": 20.7292717, "lng": -103.3442445, "status": "STARTED"}, "-OL5YChnu3hMNkw8OjQK": {"at": 1741719133548, "lat": 20.7291649, "lng": -103.3443136, "status": "STARTED"}, "-OL5YDAMMd-BjEdTe9hC": {"at": 1741719135439, "lat": 20.7290387, "lng": -103.3444122, "status": "STARTED"}, "-OL5YDeaNsragn2ciMjt": {"at": 1741719137438, "lat": 20.7289653, "lng": -103.3445622, "status": "STARTED"}, "-OL5YE9_AlUJVAxWFv0w": {"at": 1741719139485, "lat": 20.7289408, "lng": -103.3447348, "status": "STARTED"}, "-OL5YEfQI88rc0EkRexG": {"at": 1741719141586, "lat": 20.7289107, "lng": -103.3448558, "status": "STARTED"}, "-OL5YF8DzpnagQrxKRm2": {"at": 1741719143494, "lat": 20.7288487, "lng": -103.3449665, "status": "STARTED"}, "-OL5YFbb8S6Gk8hFgazB": {"at": 1741719145439, "lat": 20.7287535, "lng": -103.3450646, "status": "STARTED"}, "-OL5YG6hqfCu1PKKd5vb": {"at": 1741719147493, "lat": 20.7286429, "lng": -103.3451512, "status": "STARTED"}, "-OL5YGbBdYXQt6N57QFZ": {"at": 1741719149509, "lat": 20.7285326, "lng": -103.3452284, "status": "STARTED"}, "-OL5YH5V_jq_QWQTmjCL": {"at": 1741719151513, "lat": 20.7284197, "lng": -103.3452812, "status": "STARTED"}, "-OL5YH_-BIip-4H7eWG-": {"at": 1741719153463, "lat": 20.7282809, "lng": -103.3452719, "status": "STARTED"}, "-OL5YHoxRomH33rnXx16": {"at": 1741719154486, "lat": 20.7281886, "lng": -103.3452544, "status": "STARTED"}, "-OL5YI3EUMyH9kYbJ-Ve": {"at": 1741719155464, "lat": 20.7280896, "lng": -103.3452232, "status": "STARTED"}, "-OL5YIJjdjjvdtRnNN9r": {"at": 1741719156519, "lat": 20.7279917, "lng": -103.345188, "status": "STARTED"}, "-OL5YIYWs2lqISfaOxGy": {"at": 1741719157466, "lat": 20.7279035, "lng": -103.3451314, "status": "STARTED"}, "-OL5YImwYW78NlSHpQYu": {"at": 1741719158452, "lat": 20.727828, "lng": -103.3450695, "status": "STARTED"}, "-OL5YJInyEEI-V7EysLn": {"at": 1741719160555, "lat": 20.7277206, "lng": -103.3449568, "status": "STARTED"}, "-OL5YJmf8_7PMRlrTTOM": {"at": 1741719162531, "lat": 20.7276155, "lng": -103.3448617, "status": "STARTED"}, "-OL5YKFok8JcaEGk0mQt": {"at": 1741719164461, "lat": 20.7274573, "lng": -103.3448501, "status": "STARTED"}, "-OL5YKVIUbwTjpk2BVQg": {"at": 1741719165451, "lat": 20.7273643, "lng": -103.3448745, "status": "STARTED"}, "-OL5YKklEmN_iNTXupX7": {"at": 1741719166506, "lat": 20.7272647, "lng": -103.3449081, "status": "STARTED"}, "-OL5YL-uhXlO8kULGfsI": {"at": 1741719167538, "lat": 20.7271545, "lng": -103.3449463, "status": "STARTED"}, "-OL5YLECfXltvczUMIGY": {"at": 1741719168454, "lat": 20.727049, "lng": -103.3449908, "status": "STARTED"}, "-OL5YLTsP_U-In2OHKxQ": {"at": 1741719169456, "lat": 20.7269439, "lng": -103.3450308, "status": "STARTED"}, "-OL5YLiW0W6m_B6_1Tc3": {"at": 1741719170457, "lat": 20.7268294, "lng": -103.3450737, "status": "STARTED"}, "-OL5YLy-FpNatLDYbIaR": {"at": 1741719171448, "lat": 20.7267095, "lng": -103.3451171, "status": "STARTED"}, "-OL5YMCnqQLGkHPPI52Y": {"at": 1741719172459, "lat": 20.7265797, "lng": -103.3451592, "status": "STARTED"}, "-OL5YMSTGjL6xCmg86l6": {"at": 1741719173462, "lat": 20.7264459, "lng": -103.3451956, "status": "STARTED"}, "-OL5YMhVIlhPhA_iadCM": {"at": 1741719174488, "lat": 20.7263126, "lng": -103.3452276, "status": "STARTED"}, "-OL5YMwcdVJ3vdZgy-xz": {"at": 1741719175456, "lat": 20.72618, "lng": -103.345253, "status": "STARTED"}, "-OL5YNBK3-FP8_EoZKa3": {"at": 1741719176462, "lat": 20.7260506, "lng": -103.3452855, "status": "STARTED"}, "-OL5YNR7Rmm1Yu2Aoqsr": {"at": 1741719177472, "lat": 20.7259249, "lng": -103.3453158, "status": "STARTED"}, "-OL5YNfbSW-zc9y00wRX": {"at": 1741719178463, "lat": 20.7258044, "lng": -103.3453423, "status": "STARTED"}, "-OL5YNvZcp6t9bSZjyEz": {"at": 1741719179484, "lat": 20.7256867, "lng": -103.3453642, "status": "STARTED"}, "-OL5YO9m-F8Cl3MTq8mX": {"at": 1741719180457, "lat": 20.7255745, "lng": -103.3453757, "status": "STARTED"}, "-OL5YOPkORjYxJiVVp03": {"at": 1741719181480, "lat": 20.7254588, "lng": -103.3453859, "status": "STARTED"}, "-OL5YOf0sSdswnVrsREN": {"at": 1741719182521, "lat": 20.7253468, "lng": -103.3453955, "status": "STARTED"}, "-OL5YOtlBu6FEo48tfP-": {"at": 1741719183465, "lat": 20.7252353, "lng": -103.3454037, "status": "STARTED"}, "-OL5YP8FMlFHihvNJTsB": {"at": 1741719184457, "lat": 20.7251194, "lng": -103.3454089, "status": "STARTED"}, "-OL5YPO1-zAaqD4DZmJg": {"at": 1741719185466, "lat": 20.7250054, "lng": -103.3454147, "status": "STARTED"}, "-OL5YPcg4ZiJLwz4XcKc": {"at": 1741719186468, "lat": 20.7248934, "lng": -103.3454229, "status": "STARTED"}, "-OL5YPuhZ3EcJLNGBKpI": {"at": 1741719187621, "lat": 20.7247847, "lng": -103.3454256, "status": "STARTED"}, "-OL5YQ81rYUJKuqlIsmU": {"at": 1741719188538, "lat": 20.7246733, "lng": -103.3454297, "status": "STARTED"}, "-OL5YQOzpp54Qk5YcNI7": {"at": 1741719189623, "lat": 20.7245535, "lng": -103.3454349, "status": "STARTED"}, "-OL5YQbtu4f3P5l8jptB": {"at": 1741719190514, "lat": 20.7244349, "lng": -103.3454396, "status": "STARTED"}, "-OL5YQtWtNzKnEik9s0R": {"at": 1741719191641, "lat": 20.7243149, "lng": -103.3454417, "status": "STARTED"}, "-OL5YRAatdtiLXh6nOAe": {"at": 1741719192798, "lat": 20.7241993, "lng": -103.3454364, "status": "STARTED"}, "-OL5YRQzZcV0FeettOiS": {"at": 1741719193848, "lat": 20.7240828, "lng": -103.3454286, "status": "STARTED"}, "-OL5YRaDJiaWJvq8Lh_f": {"at": 1741719194503, "lat": 20.7239654, "lng": -103.3454189, "status": "STARTED"}, "-OL5YRrNVGVjX2nsXhZt": {"at": 1741719195600, "lat": 20.7238487, "lng": -103.345417, "status": "STARTED"}, "-OL5YS4_qiAUKP7bWCmF": {"at": 1741719196510, "lat": 20.7237348, "lng": -103.3454122, "status": "STARTED"}, "-OL5YSJey8nLrRlRTK-8": {"at": 1741719197474, "lat": 20.7236203, "lng": -103.3454072, "status": "STARTED"}, "-OL5YSZE5CQpQv6RFzLZ": {"at": 1741719198472, "lat": 20.7235096, "lng": -103.3454068, "status": "STARTED"}, "-OL5YSntHhxUR4JsbaNX": {"at": 1741719199473, "lat": 20.7234024, "lng": -103.3454053, "status": "STARTED"}, "-OL5YT2ulJDfcVUcq-1E": {"at": 1741719200497, "lat": 20.723293, "lng": -103.3454093, "status": "STARTED"}, "-OL5YTHTAs92UygG0Y2B": {"at": 1741719201430, "lat": 20.723182, "lng": -103.3454099, "status": "STARTED"}, "-OL5YTY0fBF7o_4t7Tv7": {"at": 1741719202490, "lat": 20.7230718, "lng": -103.3454085, "status": "STARTED"}, "-OL5YTmV4i8vAtkuqPq9": {"at": 1741719203481, "lat": 20.7229606, "lng": -103.3454098, "status": "STARTED"}, "-OL5YU0Fozf--7UhSkF9": {"at": 1741719204425, "lat": 20.7228488, "lng": -103.3454156, "status": "STARTED"}, "-OL5YUGS057QeCTOGA47": {"at": 1741719205461, "lat": 20.7227372, "lng": -103.3454179, "status": "STARTED"}, "-OL5YUWmdELqUBFiUDTC": {"at": 1741719206506, "lat": 20.7226273, "lng": -103.3454191, "status": "STARTED"}, "-OL5YUkU5EzdDRTpD4To": {"at": 1741719207448, "lat": 20.7225092, "lng": -103.3454116, "status": "STARTED"}, "-OL5YV06kLEMmu1IpZUW": {"at": 1741719208511, "lat": 20.7223933, "lng": -103.3454114, "status": "STARTED"}, "-OL5YVFVu7gmFSyrxJmX": {"at": 1741719209497, "lat": 20.7222724, "lng": -103.3454211, "status": "STARTED"}, "-OL5YVXE_9J58IaS4fBd": {"at": 1741719210621, "lat": 20.7221502, "lng": -103.3454213, "status": "STARTED"}, "-OL5YViZp6yZPwIUesfX": {"at": 1741719211421, "lat": 20.7220216, "lng": -103.3454206, "status": "STARTED"}, "-OL5YVyr-7LRkWWDUbHz": {"at": 1741719212464, "lat": 20.7218945, "lng": -103.3454203, "status": "STARTED"}, "-OL5YWESsfYHEkMJVMsG": {"at": 1741719213513, "lat": 20.7217666, "lng": -103.3454241, "status": "STARTED"}, "-OL5YWT6xPucagf7qhTs": {"at": 1741719214462, "lat": 20.7216371, "lng": -103.3454256, "status": "STARTED"}, "-OL5YWhk_5aVxbmqwpDU": {"at": 1741719215464, "lat": 20.7215005, "lng": -103.3454321, "status": "STARTED"}, "-OL5YWxCeDUMisMEt6gE": {"at": 1741719216453, "lat": 20.7213653, "lng": -103.3454358, "status": "STARTED"}, "-OL5YXBk7n8JpPwt-Dc6": {"at": 1741719217448, "lat": 20.7212272, "lng": -103.3454372, "status": "STARTED"}, "-OL5YXRiHSkDW4RGrA07": {"at": 1741719218470, "lat": 20.7210914, "lng": -103.3454378, "status": "STARTED"}, "-OL5YXgDVXhMsWyn4uLd": {"at": 1741719219463, "lat": 20.7209502, "lng": -103.3454391, "status": "STARTED"}, "-OL5YXwUBs1y0DmaHyMz": {"at": 1741719220504, "lat": 20.720807, "lng": -103.3454377, "status": "STARTED"}, "-OL5YYAur3yfWxlL44vI": {"at": 1741719221491, "lat": 20.7206614, "lng": -103.3454351, "status": "STARTED"}, "-OL5YYQOdZlno_0dREF3": {"at": 1741719222481, "lat": 20.7205165, "lng": -103.3454331, "status": "STARTED"}, "-OL5YYevAV0Pm1AG87sX": {"at": 1741719223475, "lat": 20.7203716, "lng": -103.3454323, "status": "STARTED"}, "-OL5YYv1W7ndqislNbtm": {"at": 1741719224507, "lat": 20.7202313, "lng": -103.345433, "status": "STARTED"}, "-OL5YZ94m7IeCiTvlCdd": {"at": 1741719225470, "lat": 20.7200888, "lng": -103.3454313, "status": "STARTED"}, "-OL5YZOdb0-m5FJTgQYG": {"at": 1741719226465, "lat": 20.7199492, "lng": -103.3454306, "status": "STARTED"}, "-OL5YZdNNp2cMMoXt93h": {"at": 1741719227472, "lat": 20.7198051, "lng": -103.3454313, "status": "STARTED"}, "-OL5YZt0ZV3HtsvhJbZn": {"at": 1741719228473, "lat": 20.7196555, "lng": -103.3454327, "status": "STARTED"}, "-OL5Y_7521k4Ji4UusQ_": {"at": 1741719229439, "lat": 20.7195054, "lng": -103.3454371, "status": "STARTED"}, "-OL5Y_MzWIPHzTr4SYcv": {"at": 1741719230455, "lat": 20.7193527, "lng": -103.345441, "status": "STARTED"}, "-OL5Y_c39yfB9tcLM0hY": {"at": 1741719231483, "lat": 20.7191989, "lng": -103.3454468, "status": "STARTED"}, "-OL5Y_rgbRfnobnBEOJQ": {"at": 1741719232484, "lat": 20.7190474, "lng": -103.3454552, "status": "STARTED"}, "-OL5Ya68gPgaAOEpv_I9": {"at": 1741719233474, "lat": 20.7189015, "lng": -103.3454657, "status": "STARTED"}, "-OL5YaLXETHlb4F2sO6L": {"at": 1741719234458, "lat": 20.7187611, "lng": -103.3454776, "status": "STARTED"}, "-OL5YaaEnuklPlyJhU_O": {"at": 1741719235463, "lat": 20.718619, "lng": -103.3454854, "status": "STARTED"}, "-OL5Yaq3ZFLDnbrhuf5U": {"at": 1741719236476, "lat": 20.7184814, "lng": -103.3455013, "status": "STARTED"}, "-OL5Yb4dxvE-kETMVZ32": {"at": 1741719237473, "lat": 20.7183482, "lng": -103.3455196, "status": "STARTED"}, "-OL5YbKXmquG_FS1VhCR": {"at": 1741719238488, "lat": 20.7182128, "lng": -103.3455338, "status": "STARTED"}, "-OL5YbZooDw47DW-hKEc": {"at": 1741719239467, "lat": 20.7180841, "lng": -103.3455484, "status": "STARTED"}, "-OL5Ybosn_lD0c_gqmyo": {"at": 1741719240496, "lat": 20.7179658, "lng": -103.345559, "status": "STARTED"}, "-OL5Yc3ZT0G3k49m04pw": {"at": 1741719241500, "lat": 20.7178625, "lng": -103.3455657, "status": "STARTED"}, "-OL5YcYFK0PzpvVwxiQg": {"at": 1741719243464, "lat": 20.7177007, "lng": -103.3455972, "status": "STARTED"}, "-OL5Yd1EAf6x_2wkGgsx": {"at": 1741719245447, "lat": 20.717582, "lng": -103.3456192, "status": "STARTED"}, "-OL5YhBgzsCLuOds3WBN": {"at": 1741719262501, "lat": 20.7174591, "lng": -103.3456419, "status": "STARTED"}, "-OL5YhfQot4zDeLl0V5o": {"at": 1741719264465, "lat": 20.7173397, "lng": -103.3456561, "status": "STARTED"}, "-OL5YiAPPJTO5APpX5DG": {"at": 1741719266513, "lat": 20.7172057, "lng": -103.3456721, "status": "STARTED"}, "-OL5Yidui55l0ZVTtReY": {"at": 1741719268466, "lat": 20.7170537, "lng": -103.3456943, "status": "STARTED"}, "-OL5Yj8IfqK6bHRsIwuQ": {"at": 1741719270475, "lat": 20.716903, "lng": -103.3457154, "status": "STARTED"}, "-OL5YjbvfuGvuWTCFfph": {"at": 1741719272436, "lat": 20.7167449, "lng": -103.3457406, "status": "STARTED"}, "-OL5Yk6E7ExWZYrozbPm": {"at": 1741719274439, "lat": 20.7165821, "lng": -103.345753, "status": "STARTED"}, "-OL5Ykar9-hi2-4dz6sK": {"at": 1741719276463, "lat": 20.7164104, "lng": -103.3457692, "status": "STARTED"}, "-OL5Yl4k9vnHmNfpBVVX": {"at": 1741719278441, "lat": 20.7162488, "lng": -103.3457787, "status": "STARTED"}, "-OL5YlaYdSMxwnLEnW_v": {"at": 1741719280539, "lat": 20.7160867, "lng": -103.3457884, "status": "STARTED"}, "-OL5Ym3S0QPmGmoYq6ZC": {"at": 1741719282453, "lat": 20.7159282, "lng": -103.3458152, "status": "STARTED"}, "-OL5YmYmHkpPzra0fRw0": {"at": 1741719284458, "lat": 20.7157608, "lng": -103.3458382, "status": "STARTED"}, "-OL5Yn27UaaVE2OW6Tvi": {"at": 1741719286464, "lat": 20.7155902, "lng": -103.3458694, "status": "STARTED"}, "-OL5YnXhRZncE1v9VaLc": {"at": 1741719288485, "lat": 20.7154059, "lng": -103.3458906, "status": "STARTED"}, "-OL5Ynlni-Phzu4EDo8g": {"at": 1741719289452, "lat": 20.7153089, "lng": -103.3458993, "status": "STARTED"}, "-OL5Yo0fptDi_8EzBZuB": {"at": 1741719290468, "lat": 20.7152117, "lng": -103.3459058, "status": "STARTED"}, "-OL5YoWBurY8BlUn8my-": {"at": 1741719292484, "lat": 20.7150417, "lng": -103.3459269, "status": "STARTED"}, "-OL5Yp-MJmCASOiTvDHk": {"at": 1741719294479, "lat": 20.7148781, "lng": -103.3459436, "status": "STARTED"}, "-OL5YpUJVdfXQ2ZW1DD0": {"at": 1741719296460, "lat": 20.714717, "lng": -103.3459621, "status": "STARTED"}, "-OL5YpykBhqWdEcNEWyL": {"at": 1741719298472, "lat": 20.7145545, "lng": -103.3459879, "status": "STARTED"}, "-OL5YqSeZc040UQORySa": {"at": 1741719300450, "lat": 20.7143854, "lng": -103.346012, "status": "STARTED"}, "-OL5YqxqlzX6ODNNOsLN": {"at": 1741719302510, "lat": 20.7142201, "lng": -103.3460408, "status": "STARTED"}, "-OL5YrQmdLhflBH0b32I": {"at": 1741719304427, "lat": 20.7140722, "lng": -103.3460723, "status": "STARTED"}, "-OL5YrvVlRc6EJ7saxTZ": {"at": 1741719306457, "lat": 20.7139404, "lng": -103.3461134, "status": "STARTED"}, "-OL5YsPz_XNhgi7KbWV5": {"at": 1741719308472, "lat": 20.7138001, "lng": -103.3461665, "status": "STARTED"}, "-OL5YsuJ4XGWOk7eWpCC": {"at": 1741719310476, "lat": 20.7136364, "lng": -103.3461912, "status": "STARTED"}, "-OL5Yt87Bmbr5_aNGvP6": {"at": 1741719311424, "lat": 20.7135434, "lng": -103.3461985, "status": "STARTED"}, "-OL5YtON9TK4tzf9inJ-": {"at": 1741719312465, "lat": 20.7134467, "lng": -103.3462087, "status": "STARTED"}, "-OL5Ytd6wB_1Rj0by0PB": {"at": 1741719313470, "lat": 20.713355, "lng": -103.3462176, "status": "STARTED"}, "-OL5Yu762UGzV5YYrSPC": {"at": 1741719315455, "lat": 20.7131829, "lng": -103.3462439, "status": "STARTED"}, "-OL5YubEA_Kk4kEg7PK5": {"at": 1741719317447, "lat": 20.7130201, "lng": -103.3462587, "status": "STARTED"}, "-OL5Yv55R35foxPC0fpq": {"at": 1741719319421, "lat": 20.7128573, "lng": -103.3462797, "status": "STARTED"}, "-OL5YvaH4CUgCy1sJ_WD": {"at": 1741719321482, "lat": 20.7127061, "lng": -103.3463034, "status": "STARTED"}, "-OL5Yw4KWG6drQE5wwqJ": {"at": 1741719323469, "lat": 20.7126073, "lng": -103.3463174, "status": "STARTED"}, "-OL5Z6IoHwo5CY1JICjB": {"at": 1741719369452, "lat": 20.7124879, "lng": -103.3463423, "status": "STARTED"}, "-OL5Z6nNyniUKRD0emJO": {"at": 1741719371472, "lat": 20.7123669, "lng": -103.3463599, "status": "STARTED"}, "-OL5Z7HruLJXo3dUhs29": {"at": 1741719373487, "lat": 20.7121948, "lng": -103.3463667, "status": "STARTED"}, "-OL5Z7lPq5W3Rdv3Hp-g": {"at": 1741719375442, "lat": 20.7120138, "lng": -103.3463817, "status": "STARTED"}, "-OL5Z80HP_dOpzmxo-m5": {"at": 1741719376458, "lat": 20.7119191, "lng": -103.3463973, "status": "STARTED"}, "-OL5Z8FlmTDtF8BVOtQd": {"at": 1741719377449, "lat": 20.7118211, "lng": -103.3464116, "status": "STARTED"}, "-OL5Z8W3za2yYGJPwjLz": {"at": 1741719378493, "lat": 20.7117219, "lng": -103.3464277, "status": "STARTED"}, "-OL5Z8k7sWMr0HnEgsvK": {"at": 1741719379457, "lat": 20.7116164, "lng": -103.3464401, "status": "STARTED"}, "-OL5Z8zb-k4SyZtpKnKM": {"at": 1741719380447, "lat": 20.711508, "lng": -103.3464527, "status": "STARTED"}, "-OL5Z9EaZaBOsKdmT2kd": {"at": 1741719381471, "lat": 20.7114038, "lng": -103.3464649, "status": "STARTED"}, "-OL5Z9TyO2W7yd3C9tsD": {"at": 1741719382454, "lat": 20.7112989, "lng": -103.3464791, "status": "STARTED"}, "-OL5Z9ijmFg6Htpjw7U2": {"at": 1741719383464, "lat": 20.7111862, "lng": -103.3464904, "status": "STARTED"}, "-OL5Z9yKveUpfLpN3fiU": {"at": 1741719384461, "lat": 20.7110683, "lng": -103.3465046, "status": "STARTED"}, "-OL5ZADCA7WIYcbieMYd": {"at": 1741719385477, "lat": 20.7109555, "lng": -103.3465207, "status": "STARTED"}, "-OL5ZASNEFzFzB1EEirK": {"at": 1741719386449, "lat": 20.710844, "lng": -103.346534, "status": "STARTED"}, "-OL5ZAhG-0Top2vtKYKg": {"at": 1741719387466, "lat": 20.7107352, "lng": -103.3465476, "status": "STARTED"}, "-OL5ZAwu0ow5ffkk25Q1": {"at": 1741719388466, "lat": 20.7106311, "lng": -103.3465634, "status": "STARTED"}, "-OL5ZBB3LjDKw_3YHJsz": {"at": 1741719389437, "lat": 20.7105262, "lng": -103.3465754, "status": "STARTED"}, "-OL5ZBR-uK2ARCn4UyBo": {"at": 1741719390457, "lat": 20.710417, "lng": -103.3465896, "status": "STARTED"}, "-OL5ZBfl-WrhrHAS9VX1": {"at": 1741719391465, "lat": 20.7103044, "lng": -103.346603, "status": "STARTED"}, "-OL5ZBvncdIkyhdS9tY-": {"at": 1741719392488, "lat": 20.7101881, "lng": -103.3466094, "status": "STARTED"}, "-OL5ZCA-rZLPNmILjyk9": {"at": 1741719393464, "lat": 20.7100754, "lng": -103.3466234, "status": "STARTED"}, "-OL5ZCPYnPT9H2xdx-gM": {"at": 1741719394460, "lat": 20.7099623, "lng": -103.3466397, "status": "STARTED"}, "-OL5ZCefXpQK4XAh5wdb": {"at": 1741719395491, "lat": 20.7098599, "lng": -103.3466479, "status": "STARTED"}, "-OL5ZCttvsbxH27SShru": {"at": 1741719396466, "lat": 20.7097498, "lng": -103.3466671, "status": "STARTED"}, "-OL5ZD8MA4X0GiTtXFTR": {"at": 1741719397455, "lat": 20.7096371, "lng": -103.3466845, "status": "STARTED"}, "-OL5ZDOO-KjETToxuxbi": {"at": 1741719398481, "lat": 20.709521, "lng": -103.3466984, "status": "STARTED"}, "-OL5ZDcxIwgkqiWqTCbM": {"at": 1741719399477, "lat": 20.7094029, "lng": -103.3467074, "status": "STARTED"}, "-OL5ZDsK2LG-DtfE1ntx": {"at": 1741719400461, "lat": 20.7092926, "lng": -103.3467264, "status": "STARTED"}, "-OL5ZE72-4Cuckwg-9l2": {"at": 1741719401467, "lat": 20.7091735, "lng": -103.3467417, "status": "STARTED"}, "-OL5ZEMcPRfv_fSwgli0": {"at": 1741719402465, "lat": 20.7090447, "lng": -103.3467508, "status": "STARTED"}, "-OL5ZEb9nJH84_rvGrLD": {"at": 1741719403459, "lat": 20.7089334, "lng": -103.3467621, "status": "STARTED"}, "-OL5ZEqcs6WcaoGOX2Uz": {"at": 1741719404449, "lat": 20.7088213, "lng": -103.3467784, "status": "STARTED"}, "-OL5ZF5IDQya69M0ioRK": {"at": 1741719405441, "lat": 20.7087132, "lng": -103.3467907, "status": "STARTED"}, "-OL5ZFL9pQ-DcUSj7oYQ": {"at": 1741719406467, "lat": 20.7086038, "lng": -103.3468114, "status": "STARTED"}, "-OL5ZF_LI3F8YTrIs0nz": {"at": 1741719407439, "lat": 20.7084871, "lng": -103.3468267, "status": "STARTED"}, "-OL5ZFpHScWe_8kb1lLk": {"at": 1741719408455, "lat": 20.7083617, "lng": -103.3468314, "status": "STARTED"}, "-OL5ZG3oZAfvmM5WyRfW": {"at": 1741719409453, "lat": 20.7082427, "lng": -103.3468323, "status": "STARTED"}, "-OL5ZGJp__ikdkBrZHRo": {"at": 1741719410477, "lat": 20.7081406, "lng": -103.346838, "status": "STARTED"}, "-OL5ZG_VAoqa07oGlmtu": {"at": 1741719411544, "lat": 20.708051, "lng": -103.3468495, "status": "STARTED"}, "-OL5ZH2ZVnBHdPhjn0VU": {"at": 1741719413468, "lat": 20.7078874, "lng": -103.3468618, "status": "STARTED"}, "-OL5ZHXTYwvUl_BbbPCo": {"at": 1741719415447, "lat": 20.7077516, "lng": -103.3468727, "status": "STARTED"}, "-OL5ZI1GSzTelUK0U_vZ": {"at": 1741719417481, "lat": 20.7076449, "lng": -103.3469012, "status": "STARTED"}, "-OL5ZR2ENa2ryAh5BvG7": {"at": 1741719454408, "lat": 20.7075375, "lng": -103.3469573, "status": "STARTED"}, "-OL5ZRnLVgpLHa1H-Wt1": {"at": 1741719457487, "lat": 20.7074303, "lng": -103.3469775, "status": "STARTED"}, "-OL5ZSHdhZ2I90DuSlK-": {"at": 1741719459490, "lat": 20.7073112, "lng": -103.3469896, "status": "STARTED"}, "-OL5ZSoNXqOZrZklkauF": {"at": 1741719461649, "lat": 20.7071757, "lng": -103.3470149, "status": "STARTED"}, "-OL5ZT1OG9B_2UvAP9gW": {"at": 1741719462545, "lat": 20.7070827, "lng": -103.3470248, "status": "STARTED"}, "-OL5ZTVbqoeIW8yaSONt": {"at": 1741719464479, "lat": 20.7069172, "lng": -103.3470369, "status": "STARTED"}, "-OL5ZTzmkhVO8MSKoopF": {"at": 1741719466474, "lat": 20.7067813, "lng": -103.3470446, "status": "STARTED"}, "-OL5ZUTh67Tx3H5RCSHe": {"at": 1741719468454, "lat": 20.7066591, "lng": -103.347046, "status": "STARTED"}, "-OL5ZUy8Aa0j6X2Tep5H": {"at": 1741719470466, "lat": 20.7065454, "lng": -103.3470705, "status": "STARTED"}, "-OL5ZVScqHu-VCrL45jl": {"at": 1741719472480, "lat": 20.7064306, "lng": -103.3470803, "status": "STARTED"}, "-OL5ZVwbNNo3qhaRxdh2": {"at": 1741719474463, "lat": 20.706289, "lng": -103.3470948, "status": "STARTED"}, "-OL5ZWQoyLstY9bd-bDa": {"at": 1741719476460, "lat": 20.7061363, "lng": -103.3471059, "status": "STARTED"}, "-OL5ZWyc5452b65ptmyK": {"at": 1741719478689, "lat": 20.705987, "lng": -103.3471337, "status": "STARTED"}, "-OL5ZXXZdsd6n90UlrtC": {"at": 1741719480989, "lat": 20.7058199, "lng": -103.3471611, "status": "STARTED"}, "-OL5ZXuzFcCxbTPn9QWq": {"at": 1741719482551, "lat": 20.7056508, "lng": -103.3472207, "status": "STARTED"}, "-OL5ZYByW0x_y-Wpp1c7": {"at": 1741719483703, "lat": 20.7055607, "lng": -103.3472692, "status": "STARTED"}, "-OL5ZYVWQbGpSeNOgb9O": {"at": 1741719484954, "lat": 20.7054824, "lng": -103.3473346, "status": "STARTED"}, "-OL5ZYkOKP9BBesKQP_J": {"at": 1741719485970, "lat": 20.7054138, "lng": -103.3474153, "status": "STARTED"}, "-OL5ZZ1Dq6YcsqpJKakV": {"at": 1741719487110, "lat": 20.7053395, "lng": -103.347492, "status": "STARTED"}, "-OL5ZZE4q34HZfe2Iixe": {"at": 1741719487933, "lat": 20.7052561, "lng": -103.3475494, "status": "STARTED"}, "-OL5ZZeQwWkRlgAPQN1w": {"at": 1741719489683, "lat": 20.7051079, "lng": -103.347609, "status": "STARTED"}, "-OL5Z_7_9jWkGzovmT6N": {"at": 1741719491612, "lat": 20.7049826, "lng": -103.3475979, "status": "STARTED"}, "-OL5Z_a0PTQWa2H5q8yH": {"at": 1741719493498, "lat": 20.7049112, "lng": -103.3475369, "status": "STARTED"}, "-OL5ZjDuc0UpuXIf9c6G": {"at": 1741719532978, "lat": 20.7048447, "lng": -103.3474228, "status": "STARTED"}, "-OL5ZjilQtotn8VCw3kt": {"at": 1741719535018, "lat": 20.704785, "lng": -103.347332, "status": "STARTED"}, "-OL5Zk9E_7CxdALHxG5o": {"at": 1741719536776, "lat": 20.7047345, "lng": -103.3472258, "status": "STARTED"}, "-OL5ZkdR1rm_yXcUf9Ik": {"at": 1741719538772, "lat": 20.7047205, "lng": -103.3471021, "status": "STARTED"}, "-OL5Zl6EI_I-tcPlfaia": {"at": 1741719540680, "lat": 20.7047192, "lng": -103.3469741, "status": "STARTED"}, "-OL5Zla_KGIvwsTiV8Y4": {"at": 1741719542686, "lat": 20.7047214, "lng": -103.3468284, "status": "STARTED"}, "-OL5Zm1Xz_hEj06nJjl5": {"at": 1741719544474, "lat": 20.7047289, "lng": -103.346672, "status": "STARTED"}, "-OL5ZmXb40IVCcDbYlkf": {"at": 1741719546527, "lat": 20.7047253, "lng": -103.3465033, "status": "STARTED"}, "-OL5Zn-jQeDb3ucZS13i": {"at": 1741719548456, "lat": 20.7046837, "lng": -103.3463396, "status": "STARTED"}, "-OL5ZnWmUSC14XrtYq7x": {"at": 1741719550571, "lat": 20.7046099, "lng": -103.3461727, "status": "STARTED"}, "-OL5ZnktQl0ciN7BtOI4": {"at": 1741719551538, "lat": 20.7045707, "lng": -103.3460846, "status": "STARTED"}, "-OL5ZnzEf9IKB9MtRC8H": {"at": 1741719552456, "lat": 20.7045305, "lng": -103.345987, "status": "STARTED"}, "-OL5ZoCpFCpEjrvCbo2u": {"at": 1741719553389, "lat": 20.7044876, "lng": -103.345886, "status": "STARTED"}, "-OL5ZoSijRCGPfcQaKVG": {"at": 1741719554406, "lat": 20.7044378, "lng": -103.3457781, "status": "STARTED"}, "-OL5ZohO70Iiq2MvkHq9": {"at": 1741719555410, "lat": 20.7043895, "lng": -103.3456616, "status": "STARTED"}, "-OL5Zoxknlre0UgnBEth": {"at": 1741719556456, "lat": 20.704336, "lng": -103.3455444, "status": "STARTED"}, "-OL5ZpCew5gIlK7x-t5j": {"at": 1741719557474, "lat": 20.7042782, "lng": -103.3454257, "status": "STARTED"}, "-OL5ZpS8LvUZLkYqZz-d": {"at": 1741719558465, "lat": 20.704228, "lng": -103.3453001, "status": "STARTED"}, "-OL5Zpg7J9JFrC6H6fM2": {"at": 1741719559424, "lat": 20.7041793, "lng": -103.3451635, "status": "STARTED"}, "-OL5ZpwkceAGR14UE-1o": {"at": 1741719560489, "lat": 20.7041278, "lng": -103.3450277, "status": "STARTED"}, "-OL5ZqAkUjOpSE76D35h": {"at": 1741719561448, "lat": 20.7040662, "lng": -103.3448937, "status": "STARTED"}, "-OL5ZqQaXVGdGuED85Jr": {"at": 1741719562463, "lat": 20.7039969, "lng": -103.3447669, "status": "STARTED"}, "-OL5Zqf7kUhvlbuzObol": {"at": 1741719563456, "lat": 20.7039314, "lng": -103.3446305, "status": "STARTED"}, "-OL5Zqul0-J52KspgC-K": {"at": 1741719564457, "lat": 20.7038692, "lng": -103.3444975, "status": "STARTED"}, "-OL5Zr9xeCk4Ot5O5smO": {"at": 1741719565493, "lat": 20.7038121, "lng": -103.3443641, "status": "STARTED"}, "-OL5ZrOseOhF94IV85_M": {"at": 1741719566449, "lat": 20.7037406, "lng": -103.344222, "status": "STARTED"}, "-OL5ZreRBrAF1_1abO4p": {"at": 1741719567499, "lat": 20.703671, "lng": -103.3440806, "status": "STARTED"}, "-OL5ZrtYdjMbPRN2OxB-": {"at": 1741719568475, "lat": 20.703603, "lng": -103.3439432, "status": "STARTED"}, "-OL5Zs7lXQTTRHNwj6zP": {"at": 1741719569450, "lat": 20.7035334, "lng": -103.3437975, "status": "STARTED"}, "-OL5ZsNn5ycns1r-WAU7": {"at": 1741719570475, "lat": 20.7034662, "lng": -103.3436538, "status": "STARTED"}, "-OL5ZscGD5_vY7m_LaN9": {"at": 1741719571465, "lat": 20.7033948, "lng": -103.343503, "status": "STARTED"}, "-OL5ZsrpoOoWjs1xkpuJ": {"at": 1741719572462, "lat": 20.7033346, "lng": -103.343352, "status": "STARTED"}, "-OL5Zt6GTVPrSEY6T0o6": {"at": 1741719573450, "lat": 20.7032765, "lng": -103.3432146, "status": "STARTED"}, "-OL5ZtNfSr0zB0IBaJxT": {"at": 1741719574564, "lat": 20.7032213, "lng": -103.3430837, "status": "STARTED"}, "-OL5ZtccRdyA1tpWtA7o": {"at": 1741719575584, "lat": 20.7031588, "lng": -103.3429489, "status": "STARTED"}, "-OL5ZtpkAokZcEgZdoXn": {"at": 1741719576424, "lat": 20.7030917, "lng": -103.3428204, "status": "STARTED"}, "-OL5Zu4jocJbYVnv2vSy": {"at": 1741719577447, "lat": 20.7030319, "lng": -103.3426942, "status": "STARTED"}, "-OL5ZuL9sbEWsjkIQ-jF": {"at": 1741719578498, "lat": 20.7029781, "lng": -103.3425653, "status": "STARTED"}, "-OL5Zu_myViLmEO9gxuV": {"at": 1741719579499, "lat": 20.7029181, "lng": -103.3424308, "status": "STARTED"}, "-OL5ZupUEDLJxaBhe6Ex": {"at": 1741719580500, "lat": 20.7028499, "lng": -103.3422894, "status": "STARTED"}, "-OL5Zv4nN0gXYTuF6JLf": {"at": 1741719581547, "lat": 20.7027891, "lng": -103.3421535, "status": "STARTED"}, "-OL5ZvJmMYATYShKY9ag": {"at": 1741719582503, "lat": 20.7027252, "lng": -103.3420168, "status": "STARTED"}, "-OL5ZvZpXhYX58GtxJks": {"at": 1741719583534, "lat": 20.702665, "lng": -103.3418784, "status": "STARTED"}, "-OL5ZvnXwDA8htLgrSf6": {"at": 1741719584474, "lat": 20.7026021, "lng": -103.3417413, "status": "STARTED"}, "-OL5Zw1yGIh35JuulIpT": {"at": 1741719585462, "lat": 20.7025409, "lng": -103.3416019, "status": "STARTED"}, "-OL5ZwHaHcZt8PT0xWp6": {"at": 1741719586462, "lat": 20.7024821, "lng": -103.3414591, "status": "STARTED"}, "-OL5ZwXFh4erRUs8VFQ4": {"at": 1741719587464, "lat": 20.7024168, "lng": -103.3413107, "status": "STARTED"}, "-OL5ZwmjYqa_T3sDn25t": {"at": 1741719588519, "lat": 20.7023491, "lng": -103.3411608, "status": "STARTED"}, "-OL5Zx1OYvor7NjXbhDO": {"at": 1741719589521, "lat": 20.7022808, "lng": -103.3410179, "status": "STARTED"}, "-OL5ZxGbpmy1Akkgujq9": {"at": 1741719590496, "lat": 20.7022176, "lng": -103.340872, "status": "STARTED"}, "-OL5ZxVk9HxSrnjg8yTz": {"at": 1741719591465, "lat": 20.7021531, "lng": -103.3407309, "status": "STARTED"}, "-OL5Zxki8yKyChFr_KCr": {"at": 1741719592486, "lat": 20.7020915, "lng": -103.3406043, "status": "STARTED"}, "-OL5Zy-oyJjgDISKE_1p": {"at": 1741719593517, "lat": 20.7020267, "lng": -103.3404876, "status": "STARTED"}, "-OL5ZyEDgDjeB8DrAcV8": {"at": 1741719594438, "lat": 20.701965, "lng": -103.3403711, "status": "STARTED"}, "-OL5ZyURHDL1mF9OWuNW": {"at": 1741719595476, "lat": 20.7019063, "lng": -103.3402479, "status": "STARTED"}, "-OL5ZyioKkWmQYr4K1GZ": {"at": 1741719596460, "lat": 20.7018502, "lng": -103.3401224, "status": "STARTED"}, "-OL5ZyyVAkPOXWp1-vzN": {"at": 1741719597462, "lat": 20.701791, "lng": -103.3399934, "status": "STARTED"}, "-OL5ZzD5S9xMZYFMYmCp": {"at": 1741719598462, "lat": 20.7017373, "lng": -103.3398675, "status": "STARTED"}, "-OL5ZzSj5ar75rhKwbZl": {"at": 1741719599463, "lat": 20.7016846, "lng": -103.3397484, "status": "STARTED"}, "-OL5ZziG9aRv89IqToy4": {"at": 1741719600521, "lat": 20.7016422, "lng": -103.3396338, "status": "STARTED"}, "-OL5ZzwlGQ-nB-AY3dzJ": {"at": 1741719601449, "lat": 20.701599, "lng": -103.3395218, "status": "STARTED"}, "-OL5_-BkBfRSK0eMnBmn": {"at": 1741719602472, "lat": 20.7015509, "lng": -103.3394127, "status": "STARTED"}, "-OL5_-QgAathRN_UhNKo": {"at": 1741719603429, "lat": 20.7015008, "lng": -103.3392957, "status": "STARTED"}, "-OL5_-fo-Mlk-4S3jQmM": {"at": 1741719604461, "lat": 20.7014491, "lng": -103.3391815, "status": "STARTED"}, "-OL5_-wDy2AFpQ2vSiw1": {"at": 1741719605504, "lat": 20.7013977, "lng": -103.3390685, "status": "STARTED"}, "-OL5_0AjFeZN2qOq6abM": {"at": 1741719606504, "lat": 20.7013538, "lng": -103.3389656, "status": "STARTED"}, "-OL5_0QNhIwc0f-KjjNA": {"at": 1741719607504, "lat": 20.7013129, "lng": -103.3388652, "status": "STARTED"}, "-OL5_0eHEuSPBt5aq-Xf": {"at": 1741719608454, "lat": 20.7012527, "lng": -103.3387652, "status": "STARTED"}, "-OL5_0uBgYhClxXehBhl": {"at": 1741719609477, "lat": 20.7011902, "lng": -103.3386552, "status": "STARTED"}, "-OL5_18VPc6R7rJfhOiR": {"at": 1741719610456, "lat": 20.7011323, "lng": -103.3385449, "status": "STARTED"}, "-OL5_1OBXE8-1bn_hRgY": {"at": 1741719611461, "lat": 20.7010761, "lng": -103.3384323, "status": "STARTED"}, "-OL5_1cgvgGNmEpFKDQq": {"at": 1741719612452, "lat": 20.7010194, "lng": -103.3383174, "status": "STARTED"}, "-OL5_1sJ6OF32Pt6QeuK": {"at": 1741719613452, "lat": 20.7009716, "lng": -103.3381969, "status": "STARTED"}, "-OL5_27QmhFM0omWIz4_": {"at": 1741719614483, "lat": 20.7009159, "lng": -103.3380878, "status": "STARTED"}, "-OL5_2Mr7i1ovQSQKGLR": {"at": 1741719615471, "lat": 20.700859, "lng": -103.337978, "status": "STARTED"}, "-OL5_2bomJJFYLFa-RWQ": {"at": 1741719616492, "lat": 20.7008112, "lng": -103.3378766, "status": "STARTED"}, "-OL5_2rHPMgn3k68DqkO": {"at": 1741719617482, "lat": 20.700762, "lng": -103.3377724, "status": "STARTED"}, "-OL5_35jtc64qV3DksQN": {"at": 1741719618471, "lat": 20.7007091, "lng": -103.3376666, "status": "STARTED"}, "-OL5_3LVdfQHj7Nd5Oue": {"at": 1741719619481, "lat": 20.7006587, "lng": -103.3375615, "status": "STARTED"}, "-OL5_3_dZX8nJhN2A9dp": {"at": 1741719620450, "lat": 20.7006072, "lng": -103.3374613, "status": "STARTED"}, "-OL5_3p_oK55WOgQfhKu": {"at": 1741719621468, "lat": 20.7005602, "lng": -103.3373643, "status": "STARTED"}, "-OL5_43y-xPYPu6LVqSV": {"at": 1741719622454, "lat": 20.7005193, "lng": -103.3372639, "status": "STARTED"}, "-OL5_4J0bceJzfVU_lWB": {"at": 1741719623417, "lat": 20.7004796, "lng": -103.3371654, "status": "STARTED"}, "-OL5_4ZLL7QoH4FpR-A9": {"at": 1741719624462, "lat": 20.7004339, "lng": -103.3370704, "status": "STARTED"}, "-OL5_4oZvXBncj4Wx-pi": {"at": 1741719625499, "lat": 20.7003807, "lng": -103.3369798, "status": "STARTED"}, "-OL5_52ilaCO84mdEuFS": {"at": 1741719626471, "lat": 20.7003251, "lng": -103.3368842, "status": "STARTED"}, "-OL5_5IQp4OO4_MbsoWJ": {"at": 1741719627476, "lat": 20.7002777, "lng": -103.3367838, "status": "STARTED"}, "-OL5_5Ylj3KPZZHvC7J5": {"at": 1741719628521, "lat": 20.7002342, "lng": -103.3366863, "status": "STARTED"}, "-OL5_5leZIgnasJt6W-y": {"at": 1741719629410, "lat": 20.7001913, "lng": -103.3365944, "status": "STARTED"}, "-OL5_6JV0jRGsMVKC4mo": {"at": 1741719631640, "lat": 20.7001256, "lng": -103.3364327, "status": "STARTED"}, "-OL5_6lEHCb4Rf__efsp": {"at": 1741719633480, "lat": 20.7000879, "lng": -103.3363359, "status": "STARTED"}, "-OL5_9cp5QRzPlC68sxn": {"at": 1741719645227, "lat": 20.7000296, "lng": -103.3361976, "status": "STARTED"}, "-OL5_A6z5IXI44uBD3fg": {"at": 1741719647224, "lat": 20.6999571, "lng": -103.3360775, "status": "STARTED"}, "-OL5_AE-NrjFdpX6gSRE": {"at": 1741719647672, "lat": 20.6999115, "lng": -103.3359937, "status": "STARTED"}, "-OL5_AUHmdmOUpEoKKLP": {"at": 1741719648714, "lat": 20.6998563, "lng": -103.3358777, "status": "STARTED"}, "-OL5_Am3VmTc75pCyjqi": {"at": 1741719649917, "lat": 20.6998169, "lng": -103.3357836, "status": "STARTED"}, "-OL5_B16fph9uZ4BOPZe": {"at": 1741719650943, "lat": 20.6997739, "lng": -103.3356945, "status": "STARTED"}, "-OL5_BUmLjJamT5SLYR7": {"at": 1741719652824, "lat": 20.6996806, "lng": -103.3355437, "status": "STARTED"}, "-OL5_BvNGcsw7X8WRrgI": {"at": 1741719654608, "lat": 20.699595, "lng": -103.3353871, "status": "STARTED"}, "-OL5_CO8tWMRCC9bb1ie": {"at": 1741719656513, "lat": 20.6995203, "lng": -103.33522, "status": "STARTED"}, "-OL5_Csl8ZGrjZpSe9Ix": {"at": 1741719658538, "lat": 20.6994425, "lng": -103.3350807, "status": "STARTED"}, "-OL5_DNEJH5X9_hM0coc": {"at": 1741719660542, "lat": 20.6993779, "lng": -103.3349221, "status": "STARTED"}, "-OL5_DpTfTAe8hwbColk": {"at": 1741719662423, "lat": 20.6993054, "lng": -103.3347485, "status": "STARTED"}, "-OL5_E5LcqBq3iMjqNTf": {"at": 1741719663503, "lat": 20.6992661, "lng": -103.3346599, "status": "STARTED"}, "-OL5_EKJJKr9N9KDCQj6": {"at": 1741719664461, "lat": 20.6992255, "lng": -103.3345738, "status": "STARTED"}, "-OL5_EohkS36RX7tASKU": {"at": 1741719666470, "lat": 20.6991407, "lng": -103.3344159, "status": "STARTED"}, "-OL5_F3DJjV4D0AvTcgU": {"at": 1741719667462, "lat": 20.6990958, "lng": -103.3343317, "status": "STARTED"}, "-OL5_FYh1MrdaC2d_-fD": {"at": 1741719669477, "lat": 20.6990074, "lng": -103.3341698, "status": "STARTED"}, "-OL5_FoIT930R-sGTo-9": {"at": 1741719670539, "lat": 20.6989659, "lng": -103.3340733, "status": "STARTED"}, "-OL5_G1cW3EdFWqj8EuW": {"at": 1741719671457, "lat": 20.6989168, "lng": -103.3339811, "status": "STARTED"}, "-OL5_GIj_QwTqvWI3cl8": {"at": 1741719672552, "lat": 20.6988684, "lng": -103.3338887, "status": "STARTED"}, "-OL5_GWha_HlwI1vYHe-": {"at": 1741719673445, "lat": 20.6988365, "lng": -103.333798, "status": "STARTED"}, "-OL5_GlV-IsJsKB4FdI0": {"at": 1741719674456, "lat": 20.6988004, "lng": -103.3337089, "status": "STARTED"}, "-OL5_H0U6zUy849qKbHV": {"at": 1741719675480, "lat": 20.6987578, "lng": -103.3336152, "status": "STARTED"}, "-OL5_HGRTPt-LdXKrJGx": {"at": 1741719676499, "lat": 20.6987208, "lng": -103.3335187, "status": "STARTED"}, "-OL5_HV8eh916XzNzkS1": {"at": 1741719677442, "lat": 20.6986756, "lng": -103.3334228, "status": "STARTED"}, "-OL5_HjxIYJBi2iot2m2": {"at": 1741719678454, "lat": 20.698638, "lng": -103.3333259, "status": "STARTED"}, "-OL5_HztOA_q_RBKLm69": {"at": 1741719679474, "lat": 20.6985934, "lng": -103.3332355, "status": "STARTED"}, "-OL5_IEQQoFsODFrgo14": {"at": 1741719680467, "lat": 20.6985479, "lng": -103.3331493, "status": "STARTED"}, "-OL5_ITCLltz9IRX93sX": {"at": 1741719681414, "lat": 20.6985037, "lng": -103.3330606, "status": "STARTED"}, "-OL5_IiPgIEGnEO8vLLR": {"at": 1741719682450, "lat": 20.6984602, "lng": -103.3329694, "status": "STARTED"}, "-OL5_IyCPnvH7E3NlY40": {"at": 1741719683461, "lat": 20.6984183, "lng": -103.3328805, "status": "STARTED"}, "-OL5_JSYYZ-ZuBp2o956": {"at": 1741719685467, "lat": 20.6983299, "lng": -103.3327059, "status": "STARTED"}, "-OL5_JhfhR0G7uXGJRI_": {"at": 1741719686500, "lat": 20.698285, "lng": -103.332618, "status": "STARTED"}, "-OL5_JvwQQVmhsAeLm2W": {"at": 1741719687413, "lat": 20.6982462, "lng": -103.3325298, "status": "STARTED"}, "-OL5_KSQ1ql3IigIqKox": {"at": 1741719689556, "lat": 20.6981654, "lng": -103.3323422, "status": "STARTED"}, "-OL5_KhrOLy-qJY90gqq": {"at": 1741719690608, "lat": 20.6981341, "lng": -103.3322307, "status": "STARTED"}, "-OL5_Kw2KgTiJMwI0le-": {"at": 1741719691514, "lat": 20.6980965, "lng": -103.3321357, "status": "STARTED"}, "-OL5_LPStq7sVnn9z2OS": {"at": 1741719693461, "lat": 20.6980241, "lng": -103.3319998, "status": "STARTED"}, "-OL5_LtlbtGmp1u6eJNn": {"at": 1741719695466, "lat": 20.6979565, "lng": -103.3319054, "status": "STARTED"}, "-OL5_MO9jVaBO7wyrim_": {"at": 1741719697474, "lat": 20.6979093, "lng": -103.3318184, "status": "STARTED"}, "-OL5_MsCfYFAaU18khWG": {"at": 1741719699462, "lat": 20.6978665, "lng": -103.3317282, "status": "STARTED"}, "-OL5_O5MNmyY0Hhq_mnu": {"at": 1741719704462, "lat": 20.6978095, "lng": -103.3316479, "status": "STARTED"}, "-OL5_OpSD8q31LB6nW-z": {"at": 1741719707477, "lat": 20.6977497, "lng": -103.3315053, "status": "STARTED"}, "-OL5_PJ_RqxwOexf41wn": {"at": 1741719709469, "lat": 20.6977089, "lng": -103.3313753, "status": "STARTED"}, "-OL5_Q37vS160biAMkds": {"at": 1741719712510, "lat": 20.697647, "lng": -103.3312515, "status": "STARTED"}, "-OL5_R0iDHOndHjS-lV_": {"at": 1741719716455, "lat": 20.6975931, "lng": -103.3311732, "status": "STARTED"}, "-OL5_RzqVsxmTvxyUPkm": {"at": 1741719720430, "lat": 20.6975499, "lng": -103.3310597, "status": "STARTED"}, "-OL5_SyVfsJog_LAnifa": {"at": 1741719724441, "lat": 20.6975106, "lng": -103.3309562, "status": "STARTED"}, "-OL5_TheHbE016307HWZ": {"at": 1741719727457, "lat": 20.6974445, "lng": -103.3308316, "status": "STARTED"}, "-OL5_UBxs7fnlipZn3WS": {"at": 1741719729462, "lat": 20.6973931, "lng": -103.3307394, "status": "STARTED"}, "-OL5_Uw-dfhzhFK02bgU": {"at": 1741719732473, "lat": 20.6973591, "lng": -103.3306423, "status": "STARTED"}, "-OL5_Vejz6MArnM_8XQj": {"at": 1741719735463, "lat": 20.6973232, "lng": -103.3305276, "status": "STARTED"}, "-OL5_WQWToib3VFX5UF8": {"at": 1741719738585, "lat": 20.6972587, "lng": -103.3303946, "status": "STARTED"}, "-OL5_WuC8o9xoYMNC2dO": {"at": 1741719740543, "lat": 20.6972178, "lng": -103.3303016, "status": "STARTED"}, "-OL5_XMwbRm1mu64Hjhg": {"at": 1741719742453, "lat": 20.6971536, "lng": -103.3301751, "status": "STARTED"}, "-OL5_XrdBeL321Flt8bE": {"at": 1741719744482, "lat": 20.6970925, "lng": -103.3300439, "status": "STARTED"}, "-OL5_YL_ngojXa8wKFFe": {"at": 1741719746461, "lat": 20.697023, "lng": -103.3299275, "status": "STARTED"}, "-OL5_Yq-bYmbFS-IHEkh": {"at": 1741719748472, "lat": 20.6969589, "lng": -103.3298177, "status": "STARTED"}, "-OL5_ZKk3nQhvHiOTy1X": {"at": 1741719750505, "lat": 20.6969024, "lng": -103.3297009, "status": "STARTED"}, "-OL5_ZoMt9uG2plJtGyv": {"at": 1741719752463, "lat": 20.6968369, "lng": -103.3295729, "status": "STARTED"}, "-OL5__JD7hI1uFJaJ9q8": {"at": 1741719754503, "lat": 20.6967789, "lng": -103.3294502, "status": "STARTED"}, "-OL5__m2Zd6oT_qCHoHp": {"at": 1741719756412, "lat": 20.6967219, "lng": -103.3293158, "status": "STARTED"}, "-OL5_aHCAt1WTn_iqg0l": {"at": 1741719758470, "lat": 20.696677, "lng": -103.3291857, "status": "STARTED"}, "-OL5_akd_8PJdxGRRg3H": {"at": 1741719760418, "lat": 20.6965835, "lng": -103.3290255, "status": "STARTED"}, "-OL5_bFrxnnsyJWb1FrK": {"at": 1741719762479, "lat": 20.6965025, "lng": -103.3288835, "status": "STARTED"}, "-OL5_bk7Y5FyrXQFWzgc": {"at": 1741719764481, "lat": 20.6964368, "lng": -103.3287494, "status": "STARTED"}, "-OL5_cEI43DPhR8sUlxB": {"at": 1741719766475, "lat": 20.6963831, "lng": -103.3286245, "status": "STARTED"}, "-OL5_ciJurvgH_D8sz6Z": {"at": 1741719768461, "lat": 20.6963158, "lng": -103.3284944, "status": "STARTED"}, "-OL5_dDP3k27gtPG4yac": {"at": 1741719770515, "lat": 20.6962514, "lng": -103.3283654, "status": "STARTED"}, "-OL5_dhJdwzIs2bKEA_p": {"at": 1741719772492, "lat": 20.6961843, "lng": -103.3282341, "status": "STARTED"}, "-OL5_eBBZURaDumRjXVU": {"at": 1741719774468, "lat": 20.6961094, "lng": -103.3281038, "status": "STARTED"}, "-OL5_efQn5ODP52M2H9B": {"at": 1741719776467, "lat": 20.6960479, "lng": -103.3279751, "status": "STARTED"}, "-OL5_fA-yElqD5h-_AQw": {"at": 1741719778488, "lat": 20.6959798, "lng": -103.3278457, "status": "STARTED"}, "-OL5_fe0JgQE-AtD3tBG": {"at": 1741719780474, "lat": 20.6959146, "lng": -103.3277051, "status": "STARTED"}, "-OL5_g8deKR5d6xJ45GI": {"at": 1741719782495, "lat": 20.6958469, "lng": -103.3275379, "status": "STARTED"}, "-OL5_gfAEC-OCUBVLrkA": {"at": 1741719784642, "lat": 20.6957813, "lng": -103.3273807, "status": "STARTED"}, "-OL5_h65KoG-IZ9AaMp8": {"at": 1741719786430, "lat": 20.6957122, "lng": -103.3272172, "status": "STARTED"}, "-OL5_hag3D-xs1cn733f": {"at": 1741719788453, "lat": 20.6956475, "lng": -103.3270671, "status": "STARTED"}, "-OL5_i4yoNZXssNCVWT5": {"at": 1741719790454, "lat": 20.695567, "lng": -103.3269165, "status": "STARTED"}, "-OL5_iKwWAMwEjV0Xes9": {"at": 1741719791477, "lat": 20.6955198, "lng": -103.326834, "status": "STARTED"}, "-OL5_iZvGqzDYjL1dluG": {"at": 1741719792436, "lat": 20.6954715, "lng": -103.3267486, "status": "STARTED"}, "-OL5_ipWK_j_euDj-w7O": {"at": 1741719793497, "lat": 20.6954215, "lng": -103.3266586, "status": "STARTED"}, "-OL5_j4qzsqLIQfzFAIk": {"at": 1741719794542, "lat": 20.6953686, "lng": -103.326577, "status": "STARTED"}, "-OL5_jK1pqQ4dTjpWgaJ": {"at": 1741719795515, "lat": 20.6953233, "lng": -103.3264898, "status": "STARTED"}, "-OL5_jnclWYe8CDQBHWg": {"at": 1741719797471, "lat": 20.6952483, "lng": -103.3263276, "status": "STARTED"}, "-OL5_kIXQZSXwPxhUoGS": {"at": 1741719799515, "lat": 20.6952084, "lng": -103.3262295, "status": "STARTED"}, "-OL5_rrHhxjxkoDVAdkT": {"at": 1741719830474, "lat": 20.6951711, "lng": -103.3261148, "status": "STARTED"}, "-OL5_sLiETEnh2TZjEaf": {"at": 1741719832487, "lat": 20.6950978, "lng": -103.3259723, "status": "STARTED"}, "-OL5_s_wbR5hFos3QrLb": {"at": 1741719833460, "lat": 20.6950502, "lng": -103.3258885, "status": "STARTED"}, "-OL5_t4_ewZqlEPw9czk": {"at": 1741719835485, "lat": 20.6949648, "lng": -103.3257317, "status": "STARTED"}, "-OL5_tK96KskORzvkKpw": {"at": 1741719836482, "lat": 20.6949302, "lng": -103.3256389, "status": "STARTED"}, "-OL5_tZFPLww8MTBb5gy": {"at": 1741719837448, "lat": 20.6949003, "lng": -103.3255218, "status": "STARTED"}, "-OL5_toItvoKNrFeEEKZ": {"at": 1741719838475, "lat": 20.6948586, "lng": -103.3253976, "status": "STARTED"}, "-OL5_u2g66Ern6tXhoxT": {"at": 1741719839461, "lat": 20.6948099, "lng": -103.3252708, "status": "STARTED"}, "-OL5_uJ8ChkYHgehQTYe": {"at": 1741719840513, "lat": 20.6947467, "lng": -103.325159, "status": "STARTED"}, "-OL5_uY7B_dcOpTWJc7Y": {"at": 1741719841472, "lat": 20.6946901, "lng": -103.3250444, "status": "STARTED"}, "-OL5_um_h5sQ7tTqdbpv": {"at": 1741*********, "lat": 20.6946335, "lng": -103.3249319, "status": "STARTED"}, "-OL5_v1DMEH9HjfKKIlL": {"at": 1741719843462, "lat": 20.6945774, "lng": -103.3248197, "status": "STARTED"}, "-OL5_vGprpwI_r0vnpQ_": {"at": 1741719844462, "lat": 20.694519, "lng": -103.3247099, "status": "STARTED"}, "-OL5_vWfWYZdeBteDbwy": {"at": 1741719845475, "lat": 20.6944637, "lng": -103.3245949, "status": "STARTED"}, "-OL5_vldkR-tTmJRNAhj": {"at": 1741719846498, "lat": 20.6944129, "lng": -103.3244855, "status": "STARTED"}, "-OL5_w-acnxb1rtHqO2S": {"at": 1741719847455, "lat": 20.6943631, "lng": -103.3243763, "status": "STARTED"}, "-OL5_wFTW9vUO2vW0YKr": {"at": 1741719848471, "lat": 20.6943012, "lng": -103.3242663, "status": "STARTED"}, "-OL5_wUcxDu7BV58NH47": {"at": 1741719849441, "lat": 20.694242, "lng": -103.3241578, "status": "STARTED"}, "-OL5_wkS1QfiwvUVtd_M": {"at": 1741719850518, "lat": 20.6941888, "lng": -103.3240522, "status": "STARTED"}, "-OL5_x0WvruR8tzM_jB2": {"at": 1741719851609, "lat": 20.6941356, "lng": -103.3239414, "status": "STARTED"}, "-OL5_xE7HUHcuzrtoKNg": {"at": 1741719852481, "lat": 20.6940877, "lng": -103.3238384, "status": "STARTED"}, "-OL5_xSpDIFmA2Mek_qR": {"at": 1741719853422, "lat": 20.6940399, "lng": -103.323749, "status": "STARTED"}, "-OL5_xi2T0PPA8BYCgmJ": {"at": 1741719854460, "lat": 20.6939907, "lng": -103.3236648, "status": "STARTED"}, "-OL5_yD9Vx6GRRelk5zb": {"at": 1741719856514, "lat": 20.6939002, "lng": -103.3235198, "status": "STARTED"}, "-OL5_yj2msWjAnahb4xC": {"at": 1741719858619, "lat": 20.6938362, "lng": -103.3234122, "status": "STARTED"}, "-OL5aB7gVXg0kE0BT1mn": {"at": 1741719913509, "lat": 20.6938062, "lng": -103.3233151, "status": "STARTED"}, "-OL5aBbffVhwS5fwlmLo": {"at": 1741719915461, "lat": 20.6936782, "lng": -103.3231837, "status": "STARTED"}, "-OL5aBraZcA6HzP_QIxe": {"at": 1741719916509, "lat": 20.6935861, "lng": -103.3231103, "status": "STARTED"}, "-OL5aC5CfG7nVArKnhGw": {"at": 1741719917446, "lat": 20.6934865, "lng": -103.3230373, "status": "STARTED"}, "-OL5aCLG6k5w3Tt5G3L9": {"at": 1741719918473, "lat": 20.6933796, "lng": -103.3229682, "status": "STARTED"}, "-OL5aCa2onrL--JyMMZg": {"at": 1741719919484, "lat": 20.6932627, "lng": -103.3228988, "status": "STARTED"}, "-OL5aCvMwwPx4azhBm5d": {"at": 1741719920847, "lat": 20.6931394, "lng": -103.3228325, "status": "STARTED"}, "-OL5aD61xhWjQNGKbVES": {"at": 1741719921595, "lat": 20.6930347, "lng": -103.322754, "status": "STARTED"}, "-OL5aDKi8EQUNnp795vl": {"at": 1741719922535, "lat": 20.6929384, "lng": -103.3226868, "status": "STARTED"}, "-OL5aDZNZRHwegIuFHqR": {"at": 1741719923472, "lat": 20.692846, "lng": -103.3226315, "status": "STARTED"}, "-OL5aDow_vI7xGmcn9Mm": {"at": 1741719924533, "lat": 20.6927385, "lng": -103.3225757, "status": "STARTED"}, "-OL5aE2jnvxBHwdgva8U": {"at": 1741719925479, "lat": 20.6926263, "lng": -103.3224989, "status": "STARTED"}, "-OL5aEI1G4u_jD_z7hmG": {"at": 1741719926458, "lat": 20.6925132, "lng": -103.3224232, "status": "STARTED"}, "-OL5aEXZHU3M-xowqiGi": {"at": 1741719927452, "lat": 20.692406, "lng": -103.322352, "status": "STARTED"}, "-OL5aEmPcw-zOsYYAAOT": {"at": 1741719928466, "lat": 20.6923018, "lng": -103.3222839, "status": "STARTED"}, "-OL5aF0xLCaFjdZyZJBq": {"at": 1741719929457, "lat": 20.6921946, "lng": -103.3222126, "status": "STARTED"}, "-OL5aFIUufIA1zYh-_Cq": {"at": 1741719930583, "lat": 20.6920864, "lng": -103.3221373, "status": "STARTED"}, "-OL5aFXMlYcovFGM4gYO": {"at": 1741719931535, "lat": 20.6919793, "lng": -103.3220721, "status": "STARTED"}, "-OL5aFmJYITVPVhxQR9b": {"at": 1741719932557, "lat": 20.6918713, "lng": -103.3220079, "status": "STARTED"}, "-OL5aG0FK29RIrZa-TiV": {"at": 1741719933512, "lat": 20.6917652, "lng": -103.3219415, "status": "STARTED"}, "-OL5aGG0d2dCzW7wYbOO": {"at": 1741719934522, "lat": 20.6916533, "lng": -103.321873, "status": "STARTED"}, "-OL5aGUxYAGn7NX1h8oD": {"at": 1741719935477, "lat": 20.6915471, "lng": -103.3218119, "status": "STARTED"}, "-OL5aGjig3tOOuOZ0jlT": {"at": 1741719936487, "lat": 20.6914459, "lng": -103.3217434, "status": "STARTED"}, "-OL5aGyflMQy8TzfG3LW": {"at": 1741719937444, "lat": 20.6913371, "lng": -103.3216466, "status": "STARTED"}, "-OL5aHCvj5DN8yEjOrIa": {"at": 1741719938419, "lat": 20.6912369, "lng": -103.3215745, "status": "STARTED"}, "-OL5aHTeOqwAQelB6hDT": {"at": 1741719939490, "lat": 20.6911408, "lng": -103.3215123, "status": "STARTED"}, "-OL5aHlEbJ21fTi2Hfq8": {"at": 1741719940680, "lat": 20.6910477, "lng": -103.3214475, "status": "STARTED"}, "-OL5aIC3SC2aTA5ZgneI": {"at": 1741719942460, "lat": 20.6909121, "lng": -103.3213583, "status": "STARTED"}, "-OL5aIhfWvD9og1cgwj5": {"at": 1741719944547, "lat": 20.6908005, "lng": -103.3213048, "status": "STARTED"}, "-OL5aJBbJ2SAj5JE-Fto": {"at": 1741719946526, "lat": 20.690688, "lng": -103.3212448, "status": "STARTED"}, "-OL5aJS5VU-sPtEBH_kt": {"at": 1741719947583, "lat": 20.690607, "lng": -103.3212022, "status": "STARTED"}, "-OL5aJfCfQR-312quryq": {"at": 1741719948485, "lat": 20.690516, "lng": -103.3211427, "status": "STARTED"}, "-OL5aJvXa2LhVJzmR7Me": {"at": 1741719949531, "lat": 20.6904321, "lng": -103.3210686, "status": "STARTED"}, "-OL5aK94FYP33wmE3kD_": {"at": 1741719950459, "lat": 20.6903356, "lng": -103.3209965, "status": "STARTED"}, "-OL5aKP4hoaCfXEkEXiM": {"at": 1741719951485, "lat": 20.6902278, "lng": -103.3209183, "status": "STARTED"}, "-OL5aKenm1q-0Ps5f4Pa": {"at": 1741719952554, "lat": 20.690125, "lng": -103.3208484, "status": "STARTED"}, "-OL5aKtLnEguyW4G55pk": {"at": 1741719953486, "lat": 20.6900249, "lng": -103.3207918, "status": "STARTED"}, "-OL5aL81mdDe-_usxs4f": {"at": 1741719954490, "lat": 20.6899337, "lng": -103.3207434, "status": "STARTED"}, "-OL5aLcCU9H9Y4vfdOBM": {"at": 1741719956486, "lat": 20.6897989, "lng": -103.3206716, "status": "STARTED"}, "-OL5aM68ozRVMn_RdscZ": {"at": 1741719958465, "lat": 20.6897006, "lng": -103.3206196, "status": "STARTED"}, "-OL5aMaH5nNhaf-IOtMq": {"at": 1741719960458, "lat": 20.6896048, "lng": -103.3205628, "status": "STARTED"}, "-OL5aN4dpOFlXKm30ocO": {"at": 1741719962466, "lat": 20.6895063, "lng": -103.3204764, "status": "STARTED"}, "-OL5aNZsBAwJOgxiEcIz": {"at": 1741719964464, "lat": 20.6894006, "lng": -103.320385, "status": "STARTED"}, "-OL5aO3Skp9rE0sZZzyW": {"at": 1741719966485, "lat": 20.6892941, "lng": -103.3203129, "status": "STARTED"}, "-OL5aOYp-RK_iDOOtpGw": {"at": 1741719968493, "lat": 20.6892145, "lng": -103.3202672, "status": "STARTED"}, "-OL5aPHbWSqsWa9UFO_c": {"at": 1741719971487, "lat": 20.6891273, "lng": -103.3202118, "status": "STARTED"}, "-OL5aQ1VkowiCLslUofu": {"at": 1741719974552, "lat": 20.689023, "lng": -103.3201221, "status": "STARTED"}, "-OL5aQjwoL_I7q2xJw_F": {"at": 1741719977461, "lat": 20.6889171, "lng": -103.3200539, "status": "STARTED"}, "-OL5aRU7aazctsiIXD_G": {"at": 1741719980481, "lat": 20.6887993, "lng": -103.3200165, "status": "STARTED"}, "-OL5aSDH5Zrc9So0xsfR": {"at": 1741719983498, "lat": 20.6886958, "lng": -103.3199615, "status": "STARTED"}, "-OL5aTBdjAmmexLL99Z2": {"at": 1741719987489, "lat": 20.6886136, "lng": -103.3199219, "status": "STARTED"}, "-OL5adPy2Ck06rDHXHsJ": {"at": 1741720033463, "lat": 20.688525, "lng": -103.3198729, "status": "STARTED"}, "-OL5adu64mejuCYQ1Yxf": {"at": 1741720035455, "lat": 20.6884418, "lng": -103.319824, "status": "STARTED"}, "-OL5aeO5HGk-gl6FfELs": {"at": 1741720037439, "lat": 20.6883404, "lng": -103.3198353, "status": "STARTED"}, "-OL5aedhQC7c_cztjbEH": {"at": 1741720038502, "lat": 20.6882762, "lng": -103.3199189, "status": "STARTED"}, "-OL5aessClIo4ZnR-0E6": {"at": 1741720039473, "lat": 20.6882125, "lng": -103.3200017, "status": "STARTED"}, "-OL5af7_C5lUPxYIckQz": {"at": 1741720040478, "lat": 20.6881493, "lng": -103.3200891, "status": "STARTED"}, "-OL5afMViR6G9WO-N1J7": {"at": 1741720041432, "lat": 20.6880714, "lng": -103.3201972, "status": "STARTED"}, "-OL5afbKT1HR45oSmIko": {"at": 1741720042446, "lat": 20.6879665, "lng": -103.320334, "status": "STARTED"}, "-OL5afrS8d7pGOSyVoSr": {"at": 1741720043474, "lat": 20.6878622, "lng": -103.3204538, "status": "STARTED"}, "-OL5ag69dJANtQbEMCNS": {"at": 1741720044483, "lat": 20.6877678, "lng": -103.3205512, "status": "STARTED"}, "-OL5agM0oO5208z5863_": {"at": 1741720045497, "lat": 20.6876769, "lng": -103.3206539, "status": "STARTED"}, "-OL5agaWaBxymjKYI0rz": {"at": 1741720046489, "lat": 20.6875898, "lng": -103.3207588, "status": "STARTED"}, "-OL5agp_YFm4UsG0S-fp": {"at": 1741720047454, "lat": 20.6875076, "lng": -103.3208611, "status": "STARTED"}, "-OL5ah3zUkomqaP0LBDb": {"at": 1741720048440, "lat": 20.687434, "lng": -103.3209598, "status": "STARTED"}, "-OL5ahKGsaV1htuR-KuV": {"at": 1741720049481, "lat": 20.6873596, "lng": -103.3210534, "status": "STARTED"}, "-OL5ah_UIe97tVTIfT7r": {"at": 1741720050519, "lat": 20.6872913, "lng": -103.321139, "status": "STARTED"}, "-OL5ahoq_1AwGUvNnXEO": {"at": 1741720051503, "lat": 20.6872297, "lng": -103.3212223, "status": "STARTED"}, "-OL5ai3JkTUXyaaT8XKF": {"at": 1741720052492, "lat": 20.6871717, "lng": -103.3212962, "status": "STARTED"}, "-OL5aiYM1ZvnZNsq-KsA": {"at": 1741720054479, "lat": 20.6870719, "lng": -103.3214471, "status": "STARTED"}, "-OL5aj226NBhcxDmrO7C": {"at": 1741720056508, "lat": 20.686984, "lng": -103.3215718, "status": "STARTED"}, "-OL5ajWqICbePh7EvlMb": {"at": 1741720058479, "lat": 20.6868914, "lng": -103.3216799, "status": "STARTED"}, "-OL5ak022Ph2KUPZr51H": {"at": 1741720060474, "lat": 20.6868107, "lng": -103.3217697, "status": "STARTED"}, "-OL5akl1pQBmBwxAPxON": {"at": 1741720063546, "lat": 20.6867416, "lng": -103.3218662, "status": "STARTED"}, "-OL5b-MsVKe9PoV1laf5": {"at": 1741720127472, "lat": 20.6866394, "lng": -103.3219408, "status": "STARTED"}, "-OL5b069zaf8kYR7SJfv": {"at": 1741720130498, "lat": 20.6864994, "lng": -103.3219382, "status": "STARTED"}, "-OL5b0_jGX4Z_K8VqDcS": {"at": 1741720132456, "lat": 20.686356, "lng": -103.3218754, "status": "STARTED"}, "-OL5b14KbFbuFloq2KFX": {"at": 1741720134478, "lat": 20.6862044, "lng": -103.3218215, "status": "STARTED"}, "-OL5b1YzwyOd8yyUPriP": {"at": 1741720136440, "lat": 20.6861157, "lng": -103.3217936, "status": "STARTED"}, "-OL5b2IfBgFVxnr5BHoM": {"at": 1741720139482, "lat": 20.6859892, "lng": -103.321746, "status": "STARTED"}, "-OL5b2mP4KKLDf85IZcQ": {"at": 1741720141458, "lat": 20.6858675, "lng": -103.3217493, "status": "STARTED"}, "-OL5b3GpTELJlD_Uqevh": {"at": 1741720143468, "lat": 20.6857217, "lng": -103.3217991, "status": "STARTED"}, "-OL5b3l-YqYESRF6LJIs": {"at": 1741720145464, "lat": 20.6855685, "lng": -103.3218521, "status": "STARTED"}, "-OL5b4FB-577c5SkL5eb": {"at": 1741720147461, "lat": 20.6854101, "lng": -103.3219274, "status": "STARTED"}, "-OL5b4UP5_o0xI_wPvjd": {"at": 1741720148434, "lat": 20.6853145, "lng": -103.321951, "status": "STARTED"}, "-OL5b4yvA93cXpuzCFfE": {"at": 1741720150451, "lat": 20.6851573, "lng": -103.3219934, "status": "STARTED"}, "-OL5b5TKUEZpfylAIg_e": {"at": 1741720152461, "lat": 20.6850596, "lng": -103.3220343, "status": "STARTED"}, "-OL5b6SOBkiO908jB3tU": {"at": 1741720156497, "lat": 20.6849699, "lng": -103.32212, "status": "STARTED"}, "-OL5b6wbUKZxjS1zXZAt": {"at": 1741720158495, "lat": 20.684876, "lng": -103.3221613, "status": "STARTED"}, "-OL5b7QOEYKS5YYOSXwi": {"at": 1741720160466, "lat": 20.6847789, "lng": -103.3222077, "status": "STARTED"}, "-OL5b7uuvB3d1-cpVqwO": {"at": 1741720162482, "lat": 20.6846481, "lng": -103.3222479, "status": "STARTED"}, "-OL5b8P9Z4fwF5_MLbnx": {"at": 1741720164483, "lat": 20.6844773, "lng": -103.3222891, "status": "STARTED"}, "-OL5b8tESzO2kc28Xumf": {"at": 1741720166472, "lat": 20.6843055, "lng": -103.3223292, "status": "STARTED"}, "-OL5b9ORDegqAq5Zky31": {"at": 1741720168532, "lat": 20.6841467, "lng": -103.3223652, "status": "STARTED"}, "-OL5b9smN-bRaiN77d59": {"at": 1741720170539, "lat": 20.6840186, "lng": -103.3224035, "status": "STARTED"}, "-OL5bArZMnJNhUYF4V1a": {"at": 1741720174557, "lat": 20.6839113, "lng": -103.322423, "status": "STARTED"}, "-OL5bBMqFqMD1IvZvon-": {"at": 1741720176623, "lat": 20.6838038, "lng": -103.3224368, "status": "STARTED"}, "-OL5bC4iOL_m01Me3aVi": {"at": 1741720179559, "lat": 20.6836941, "lng": -103.3224403, "status": "STARTED"}, "-OL5bCqYZ4T-nJ65zDLp": {"at": 1741720182673, "lat": 20.6836079, "lng": -103.3224962, "status": "STARTED"}, "-OL5bDnBMr7XSeX3fXsx": {"at": 1741720186564, "lat": 20.6835191, "lng": -103.3225206, "status": "STARTED"}, "-OL5bGwZQr6ccKNLEuIJ": {"at": 1741720199453, "lat": 20.6834257, "lng": -103.3225892, "status": "STARTED"}, "-OL5bHfYwfcVMhBbavZN": {"at": 1741720202460, "lat": 20.683311, "lng": -103.3226023, "status": "STARTED"}, "-OL5bIdtJ0sZL4Bj6pCV": {"at": 1741720206450, "lat": 20.6832182, "lng": -103.3226395, "status": "STARTED"}, "-OL5fo533Hrgrm8aiJ2a": {"at": 1741721387334, "lat": 20.6837515, "lng": -103.3223714, "status": "REACHED"}}, "-OLACpjOSiMkgkwhUHIp": {"-OLAcxjQGO1VO99ceskS": {"at": 1741804526674, "lat": 20.7599244, "lng": -103.3312579, "status": "ACCEPTED"}, "-OLAczKoSK1Qpn0nTWgH": {"at": 1741804533228, "lat": 20.7598508, "lng": -103.3311909, "status": "ACCEPTED"}, "-OLAd-eXDl66pX5x5SAS": {"at": 1741804538650, "lat": 20.7598148, "lng": -103.3311767, "status": "STARTED"}, "-OLAd2jE768S4x-cp6aA": {"at": 1741804551239, "lat": 20.75969, "lng": -103.3311759, "status": "STARTED"}, "-OLAd65iV0KDe7X0oCRM": {"at": 1741804565030, "lat": 20.7597591, "lng": -103.3311122, "status": "STARTED"}, "-OLAdB40xWQkE-vQhyFz": {"at": 1741804585401, "lat": 20.7598192, "lng": -103.3310354, "status": "STARTED"}, "-OLAdE4EPY_7qHFj0ihz": {"at": 1741804597702, "lat": 20.7598862, "lng": -103.3309699, "status": "STARTED"}, "-OLAdIl5nEUzEin0Ikxl": {"at": 1741804616892, "lat": 20.7599592, "lng": -103.3308979, "status": "STARTED"}, "-OLAdKzgmHEYvKcJ4Cn7": {"at": 1741804626019, "lat": 20.7600108, "lng": -103.3308186, "status": "STARTED"}, "-OLAdTREQMfOMrEE8Uv2": {"at": 1741804660614, "lat": 20.7600669, "lng": -103.3305624, "status": "STARTED"}, "-OLAdTk0QfIQ50DcRm_h": {"at": 1741804661880, "lat": 20.7601197, "lng": -103.3304651, "status": "STARTED"}, "-OLAdZxhk-tK_gNCpAXf": {"at": 1741804687332, "lat": 20.7600823, "lng": -103.3305589, "status": "STARTED"}, "-OLAd_BdDIFlWx6tqr8K": {"at": 1741804688289, "lat": 20.759992, "lng": -103.3306494, "status": "STARTED"}, "-OLAdb6hfaheCFQfHzIa": {"at": 1741804696165, "lat": 20.7600621, "lng": -103.3305847, "status": "STARTED"}, "-OLAdeKHtP5cIAjIvYKz": {"at": 1741804709321, "lat": 20.7600569, "lng": -103.3306909, "status": "STARTED"}, "-OLAe1-LAlPoHaPMd4Ta": {"at": 1741804806285, "lat": 20.7601374, "lng": -103.3307492, "status": "STARTED"}, "-OLAe2SpnpfajMVmBHio": {"at": 1741804812268, "lat": 20.7602468, "lng": -103.3308095, "status": "STARTED"}, "-OLAe3RMLiStM7SJnpee": {"at": 1741804816271, "lat": 20.7603224, "lng": -103.3308763, "status": "STARTED"}, "-OLAe4uIxfO8VSuzOnBx": {"at": 1741804822282, "lat": 20.7604129, "lng": -103.3309224, "status": "STARTED"}, "-OLAe5sglmVrgJoGVGzG": {"at": 1741804826276, "lat": 20.760496, "lng": -103.3309602, "status": "STARTED"}, "-OLAe76QyU5h1LHJE4ER": {"at": 1741804831315, "lat": 20.7605918, "lng": -103.3310087, "status": "STARTED"}, "-OLAe84cLqi2e0iVKCAU": {"at": 1741804835296, "lat": 20.7606705, "lng": -103.331061, "status": "STARTED"}, "-OLAfRG1F0UcVhS3wmJ-": {"at": 1741805175993, "lat": 20.7643871, "lng": -103.3383544, "status": "STARTED"}, "-OLAfUSWYvN5NGZEZtiT": {"at": 1741805189304, "lat": 20.7644444, "lng": -103.3384291, "status": "STARTED"}, "-OLAffKXI-MTz4rKWH-V": {"at": 1741805237947, "lat": 20.7645133, "lng": -103.338511, "status": "STARTED"}, "-OLAffvJe2pYN_CvLaJc": {"at": 1741805240365, "lat": 20.7645768, "lng": -103.3386397, "status": "STARTED"}, "-OLAfgSw7OLWBcYclhdo": {"at": 1741805242581, "lat": 20.7646209, "lng": -103.3387334, "status": "STARTED"}, "-OLAfhBU_whBhaZvAGTu": {"at": 1741805245559, "lat": 20.7646789, "lng": -103.3388281, "status": "STARTED"}, "-OLAfjbWr4fZAWFczYij": {"at": 1741805255481, "lat": 20.7647408, "lng": -103.3389175, "status": "STARTED"}, "-OLAg429PGZ8ps7LsXaM": {"at": 1741805343259, "lat": 20.7647811, "lng": -103.3390077, "status": "STARTED"}, "-OLAg4Y2Sk4vO43pDKyx": {"at": 1741805345305, "lat": 20.7648338, "lng": -103.3391008, "status": "STARTED"}, "-OLAg501N67kWMnXme6g": {"at": 1741805347227, "lat": 20.7649042, "lng": -103.339218, "status": "STARTED"}, "-OLAg5XDlJce9gVdhr49": {"at": 1741805349351, "lat": 20.7649695, "lng": -103.33932, "status": "STARTED"}, "-OLAg6-90YpjP-kOm0dh": {"at": 1741805351267, "lat": 20.7650206, "lng": -103.3394077, "status": "STARTED"}, "-OLAg6kkGH_yOi7pprrt": {"at": 1741805354377, "lat": 20.7650832, "lng": -103.3395137, "status": "STARTED"}, "-OLAgW7yIjvQqvxZMwDA": {"at": 1741805458326, "lat": 20.7651379, "lng": -103.3395997, "status": "STARTED"}, "-OLAgWrJZsL01k7WJxUw": {"at": 1741805461293, "lat": 20.7650839, "lng": -103.3396951, "status": "STARTED"}, "-OLAgXLo7Pr_vU28xrZX": {"at": 1741805463308, "lat": 20.7649805, "lng": -103.33975, "status": "STARTED"}, "-OLAgXpHIjc_yAwXCcCs": {"at": 1741805465259, "lat": 20.7648473, "lng": -103.3397654, "status": "STARTED"}, "-OLAgYJeVuUVkZmpc_5a": {"at": 1741805467267, "lat": 20.7646743, "lng": -103.3397783, "status": "STARTED"}, "-OLAgYZyYrT7mV__X9w4": {"at": 1741805468310, "lat": 20.7645721, "lng": -103.3397849, "status": "STARTED"}, "-OLAgYnr9IYHnuezoz5y": {"at": 1741805469264, "lat": 20.7644687, "lng": -103.3397875, "status": "STARTED"}, "-OLAgZ2j2SJDE5jilKCL": {"at": 1741805470279, "lat": 20.7643555, "lng": -103.3397862, "status": "STARTED"}, "-OLAgZHwwGfjrBX7-Gji": {"at": 1741805471253, "lat": 20.7642477, "lng": -103.3397802, "status": "STARTED"}, "-OLAgZXWefUlPobfD3wF": {"at": 1741805472250, "lat": 20.7641392, "lng": -103.3397731, "status": "STARTED"}, "-OLAgZmOjRfu1AhbVQLV": {"at": 1741805473265, "lat": 20.7640302, "lng": -103.3397633, "status": "STARTED"}, "-OLAg_1QkjlGF0qoMMNl": {"at": 1741805474291, "lat": 20.763927, "lng": -103.3397461, "status": "STARTED"}, "-OLAg_Go4zfmX5i2Gwmx": {"at": 1741805475276, "lat": 20.763829, "lng": -103.3397306, "status": "STARTED"}, "-OLAg_Wmo7UCS3O7XMXj": {"at": 1741805476299, "lat": 20.7637397, "lng": -103.3397169, "status": "STARTED"}, "-OLAga0QX4Q-dVWb_E4z": {"at": 1741805478324, "lat": 20.7635772, "lng": -103.3396938, "status": "STARTED"}, "-OLAgaTzU7wLde6HAIB5": {"at": 1741805480216, "lat": 20.7634482, "lng": -103.3396644, "status": "STARTED"}, "-OLAgbE4M3T1iHEnc1PA": {"at": 1741805483294, "lat": 20.763332, "lng": -103.3396384, "status": "STARTED"}, "-OLAglizIwj4WqrTlQiG": {"at": 1741805526295, "lat": 20.7632445, "lng": -103.3396042, "status": "STARTED"}, "-OLAgmTwPW7szjZA_rLU": {"at": 1741805529365, "lat": 20.7630976, "lng": -103.3395749, "status": "STARTED"}, "-OLAgmwouiNw5AgzZn9e": {"at": 1741805531277, "lat": 20.7629375, "lng": -103.3395507, "status": "STARTED"}, "-OLAgnBy5oI1ZH2zeNdX": {"at": 1741805532310, "lat": 20.7628328, "lng": -103.3395368, "status": "STARTED"}, "-OLAgnQvqwVN9R88I9uf": {"at": 1741805533268, "lat": 20.7627181, "lng": -103.3395217, "status": "STARTED"}, "-OLAgnfh6MzzZ3dQt-_J": {"at": 1741805534278, "lat": 20.7625937, "lng": -103.3395053, "status": "STARTED"}, "-OLAgnvfTFAonwC925rV": {"at": 1741805535300, "lat": 20.7624607, "lng": -103.3394859, "status": "STARTED"}, "-OLAgo9tU5PxRo1BYVPQ": {"at": 1741805536273, "lat": 20.7623193, "lng": -103.3394705, "status": "STARTED"}, "-OLAgoPCMXXDN5xazsrl": {"at": 1741805537253, "lat": 20.7621724, "lng": -103.3394601, "status": "STARTED"}, "-OLAgoeCnGbhv-8fMbns": {"at": 1741805538278, "lat": 20.762024, "lng": -103.339455, "status": "STARTED"}, "-OLAgotXDesNp9wHsckt": {"at": 1741805539258, "lat": 20.7618796, "lng": -103.339458, "status": "STARTED"}, "-OLAgp8LBdoVZHKbsnis": {"at": 1741805540271, "lat": 20.7617275, "lng": -103.3394725, "status": "STARTED"}, "-OLAgpOEdnr1p0PEH1tw": {"at": 1741805541287, "lat": 20.7615672, "lng": -103.3395039, "status": "STARTED"}, "-OLAgpcOclPkN-FamRnq": {"at": 1741805542258, "lat": 20.7614037, "lng": -103.3395506, "status": "STARTED"}, "-OLAgps9gBycRrawbGbB": {"at": 1741805543267, "lat": 20.7612379, "lng": -103.3396067, "status": "STARTED"}, "-OLAgq6x1OVuOCC6sMQr": {"at": 1741805544278, "lat": 20.761071, "lng": -103.3396696, "status": "STARTED"}, "-OLAgqMPNR4xEPq0-tAz": {"at": 1741805545266, "lat": 20.7609059, "lng": -103.3397437, "status": "STARTED"}, "-OLAgqbCEeRqbhvaf8xH": {"at": 1741805546277, "lat": 20.7607446, "lng": -103.3398292, "status": "STARTED"}, "-OLAgqqWj0OGns50dxuQ": {"at": 1741805547258, "lat": 20.7605929, "lng": -103.3399248, "status": "STARTED"}, "-OLAgr5U2EaDjvQRfBsd": {"at": 1741805548280, "lat": 20.7604501, "lng": -103.3400387, "status": "STARTED"}, "-OLAgrK_Cx9kKb3KZ2kq": {"at": 1741805549245, "lat": 20.7603127, "lng": -103.340171, "status": "STARTED"}, "-OLAgr_kAzqtBZNUMUhd": {"at": 1741805550280, "lat": 20.7601915, "lng": -103.3403087, "status": "STARTED"}, "-OLAgrp7_Lo8_QaMk6aH": {"at": 1741805551264, "lat": 20.7600758, "lng": -103.3404596, "status": "STARTED"}, "-OLAgs6NYfcxe5qqgGiy": {"at": 1741805552432, "lat": 20.7599709, "lng": -103.340618, "status": "STARTED"}, "-OLAgsJdapq6C6g_dvCB": {"at": 1741805553281, "lat": 20.7598673, "lng": -103.3407748, "status": "STARTED"}, "-OLAgsZ-buZyEZMZVB4w": {"at": 1741805554264, "lat": 20.7597732, "lng": -103.3409402, "status": "STARTED"}, "-OLAgsn_E8lOsY1RQMGb": {"at": 1741805555261, "lat": 20.7596809, "lng": -103.3411143, "status": "STARTED"}, "-OLAgt2tuyFRQFdH06sd": {"at": 1741805556306, "lat": 20.7595844, "lng": -103.341293, "status": "STARTED"}, "-OLAgtHsazFyj28BDOuO": {"at": 1741805557265, "lat": 20.7594862, "lng": -103.3414787, "status": "STARTED"}, "-OLAgtY2vbBmInF-Dgiq": {"at": 1741805558300, "lat": 20.7593925, "lng": -103.3416605, "status": "STARTED"}, "-OLAgtmdQr-ALa7orxY9": {"at": 1741805559297, "lat": 20.7592918, "lng": -103.3418288, "status": "STARTED"}, "-OLAgu0tdfWYiN9psfUh": {"at": 1741805560274, "lat": 20.7591716, "lng": -103.3419897, "status": "STARTED"}, "-OLAguG9COxT9_errT-K": {"at": 1741805561250, "lat": 20.7590389, "lng": -103.3421506, "status": "STARTED"}, "-OLAguVqYLZnSsuwTM-T": {"at": 1741805562255, "lat": 20.7589067, "lng": -103.3423121, "status": "STARTED"}, "-OLAgukgjQW9hjI8JC2H": {"at": 1741805563268, "lat": 20.7587755, "lng": -103.3424597, "status": "STARTED"}, "-OLAgv-ihXKJ9DGJ1g9i": {"at": 1741805564294, "lat": 20.7586301, "lng": -103.342593, "status": "STARTED"}, "-OLAgvFPHE81MsBxHHMc": {"at": 1741805565298, "lat": 20.7584736, "lng": -103.3427071, "status": "STARTED"}, "-OLAgvV9lay9YpXfCXUY": {"at": 1741805566306, "lat": 20.7583144, "lng": -103.342808, "status": "STARTED"}, "-OLAgvj9PCVBkCH1KiMm": {"at": 1741805567266, "lat": 20.7581489, "lng": -103.3428936, "status": "STARTED"}, "-OLAgvynCyBBpKl3pXQr": {"at": 1741805568268, "lat": 20.7579808, "lng": -103.3429654, "status": "STARTED"}, "-OLAgwDO0ZnX6I5Ndj4k": {"at": 1741805569264, "lat": 20.7578051, "lng": -103.3430288, "status": "STARTED"}, "-OLAgwTFkOxXTQo76LdW": {"at": 1741805570280, "lat": 20.757646, "lng": -103.3430849, "status": "STARTED"}, "-OLAgwi0mynwKSlFxCD2": {"at": 1741805571289, "lat": 20.7574882, "lng": -103.3431254, "status": "STARTED"}, "-OLAgwy8q3fKAwGWGmdp": {"at": 1741805572322, "lat": 20.7573353, "lng": -103.3431513, "status": "STARTED"}, "-OLAgxDpoMu29qTf8Qdz": {"at": 1741805573389, "lat": 20.7571832, "lng": -103.3431703, "status": "STARTED"}, "-OLAgxRvNelT4ug-NUc5": {"at": 1741805574290, "lat": 20.7570396, "lng": -103.343189, "status": "STARTED"}, "-OLAgxgVVw9QzX1Hqfma": {"at": 1741805575289, "lat": 20.756898, "lng": -103.3431998, "status": "STARTED"}, "-OLAgxw0KORAWdBN3jlW": {"at": 1741805576281, "lat": 20.756773, "lng": -103.3431998, "status": "STARTED"}, "-OLAgyAzUm_prXnVDhPx": {"at": 1741805577304, "lat": 20.7566749, "lng": -103.3431958, "status": "STARTED"}, "-OLAgyf4NT_8Ye-Oz8ny": {"at": 1741805579292, "lat": 20.7565467, "lng": -103.343181, "status": "STARTED"}, "-OLAhC4Mt6g3QUe3dBMi": {"at": 1741805638319, "lat": 20.7563954, "lng": -103.3431495, "status": "STARTED"}, "-OLAhCYMcy8aUjWHtByl": {"at": 1741805640240, "lat": 20.7562839, "lng": -103.3431368, "status": "STARTED"}, "-OLAhD3GS4Hx_ot8PTlQ": {"at": 1741805642345, "lat": 20.7561402, "lng": -103.3431175, "status": "STARTED"}, "-OLAhDHD31BQ2oTfiT1X": {"at": 1741805643239, "lat": 20.756041, "lng": -103.3431104, "status": "STARTED"}, "-OLAhDXj3i3uUlM2KfGY": {"at": 1741805644296, "lat": 20.7559296, "lng": -103.3430979, "status": "STARTED"}, "-OLAhDnNQhy41Ej8Q8gJ": {"at": 1741805645361, "lat": 20.7558051, "lng": -103.3430843, "status": "STARTED"}, "-OLAhE12wjlXhM1R5-Vf": {"at": 1741805646299, "lat": 20.7556673, "lng": -103.343078, "status": "STARTED"}, "-OLAhEGKdJgMBMxR97H6": {"at": 1741805647277, "lat": 20.7555177, "lng": -103.343073, "status": "STARTED"}, "-OLAhEVs8gEWC69LWJBt": {"at": 1741805648272, "lat": 20.7553558, "lng": -103.3430698, "status": "STARTED"}, "-OLAhEkhCzeXhlqlklRf": {"at": 1741805649285, "lat": 20.7551816, "lng": -103.3430664, "status": "STARTED"}, "-OLAhF0RHMSYrbvG3_ze": {"at": 1741805650356, "lat": 20.7549984, "lng": -103.3430702, "status": "STARTED"}, "-OLAhFG2RK6wi5raekbG": {"at": 1741805651355, "lat": 20.7548136, "lng": -103.3430754, "status": "STARTED"}, "-OLAhFV8M42RREV7UIlF": {"at": 1741805652322, "lat": 20.7546209, "lng": -103.3430943, "status": "STARTED"}, "-OLAhFjBLYIFv8XTcc4D": {"at": 1741805653284, "lat": 20.7544217, "lng": -103.3431298, "status": "STARTED"}, "-OLAhFzAZ_MCoERL8IVr": {"at": 1741805654307, "lat": 20.7542144, "lng": -103.3431664, "status": "STARTED"}, "-OLAhGDOfsgBKG2A47NM": {"at": 1741805655282, "lat": 20.7540045, "lng": -103.343218, "status": "STARTED"}, "-OLAhGT2AWJQH_R_IyFS": {"at": 1741805656283, "lat": 20.7537945, "lng": -103.3432781, "status": "STARTED"}, "-OLAhGhXdu9nylQx27Qc": {"at": 1741805657274, "lat": 20.7535775, "lng": -103.3433418, "status": "STARTED"}, "-OLAhGxHbiCUGVIb-1bq": {"at": 1741805658281, "lat": 20.7533693, "lng": -103.3434038, "status": "STARTED"}, "-OLAhHCDUqBmKadbnyWf": {"at": 1741805659302, "lat": 20.7531651, "lng": -103.3434688, "status": "STARTED"}, "-OLAhHRk7DW10OINbW-U": {"at": 1741805660296, "lat": 20.7529694, "lng": -103.3435365, "status": "STARTED"}, "-OLAhHgL1udwHBrlvqrc": {"at": 1741805661294, "lat": 20.7527764, "lng": -103.3435949, "status": "STARTED"}, "-OLAhHvxNcGAxQYxIvFY": {"at": 1741805662293, "lat": 20.7525864, "lng": -103.3436451, "status": "STARTED"}, "-OLAhIA_9xFJXmNdCzxT": {"at": 1741805663294, "lat": 20.752404, "lng": -103.343675, "status": "STARTED"}, "-OLAhIR-1Kqw4NSiNUjE": {"at": 1741805664345, "lat": 20.7522264, "lng": -103.3436863, "status": "STARTED"}, "-OLAhIeTzaaKqiIbsI4j": {"at": 1741805665270, "lat": 20.7520384, "lng": -103.3436809, "status": "STARTED"}, "-OLAhIuWiG_W8C9AY5AS": {"at": 1741805666298, "lat": 20.7518471, "lng": -103.343663, "status": "STARTED"}, "-OLAhJ8i-tbj7IgplDzG": {"at": 1741805667270, "lat": 20.7516545, "lng": -103.3436274, "status": "STARTED"}, "-OLAhJOHtJP3bvEDWEPl": {"at": 1741805668267, "lat": 20.7514664, "lng": -103.3435765, "status": "STARTED"}, "-OLAhJczyC-WIvYmQFaQ": {"at": 1741805669272, "lat": 20.7512819, "lng": -103.3435198, "status": "STARTED"}, "-OLAhJsvs8eV5OW7bvF1": {"at": 1741805670291, "lat": 20.7511115, "lng": -103.3434487, "status": "STARTED"}, "-OLAhK7JO3wFTneY9dNx": {"at": 1741805671276, "lat": 20.7509484, "lng": -103.3433731, "status": "STARTED"}, "-OLAhKNSIc0beqwmQZBm": {"at": 1741805672309, "lat": 20.7507898, "lng": -103.3433009, "status": "STARTED"}, "-OLAhKc7-B9T08qWjPEj": {"at": 1741805673313, "lat": 20.7506376, "lng": -103.3432331, "status": "STARTED"}, "-OLAhKrEzaMp8LqQombI": {"at": 1741805674279, "lat": 20.7504926, "lng": -103.3431659, "status": "STARTED"}, "-OLAhL6CXPQtYMe4g-a7": {"at": 1741805675302, "lat": 20.7503595, "lng": -103.3431039, "status": "STARTED"}, "-OLAhLLd33HC3J86sayr": {"at": 1741805676290, "lat": 20.7502342, "lng": -103.3430534, "status": "STARTED"}, "-OLAhLabZls8wbGgtneT": {"at": 1741805677311, "lat": 20.7501151, "lng": -103.3430063, "status": "STARTED"}, "-OLAhLqAZTStgVvMxSoj": {"at": 1741805678307, "lat": 20.7499949, "lng": -103.3429533, "status": "STARTED"}, "-OLAhM4ScefN2-Qzar1c": {"at": 1741805679285, "lat": 20.7498675, "lng": -103.3429073, "status": "STARTED"}, "-OLAhMK0GEsy_7-6oN1j": {"at": 1741805680282, "lat": 20.7497357, "lng": -103.3428705, "status": "STARTED"}, "-OLAhM_4ySy7I4d2-6-s": {"at": 1741805681308, "lat": 20.7496025, "lng": -103.3428428, "status": "STARTED"}, "-OLAhMoSJSYj_xtaQxg9": {"at": 1741805682293, "lat": 20.7494636, "lng": -103.3428245, "status": "STARTED"}, "-OLAhN3hSTYgCC4KsJcW": {"at": 1741805683333, "lat": 20.7493238, "lng": -103.3428192, "status": "STARTED"}, "-OLAhNI_cak33kQ4wAaM": {"at": 1741805684286, "lat": 20.7491784, "lng": -103.3428243, "status": "STARTED"}, "-OLAhNYJXB2C7eLW9-RT": {"at": 1741805685293, "lat": 20.749025, "lng": -103.3428277, "status": "STARTED"}, "-OLAhNmvyim3YVPP95fx": {"at": 1741805686292, "lat": 20.7488722, "lng": -103.3428384, "status": "STARTED"}, "-OLAhO2Ds8Ly-eTZmkra": {"at": 1741805687334, "lat": 20.7487119, "lng": -103.3428691, "status": "STARTED"}, "-OLAhOGsChxbNh05vAR1": {"at": 1741805688273, "lat": 20.748551, "lng": -103.3429072, "status": "STARTED"}, "-OLAhOWrFX1EBlaIq8fW": {"at": 1741805689295, "lat": 20.7483961, "lng": -103.3429549, "status": "STARTED"}, "-OLAhOlJc9XiwjSSw306": {"at": 1741805690285, "lat": 20.7482375, "lng": -103.3430142, "status": "STARTED"}, "-OLAhP07klTTKvao479v": {"at": 1741805691296, "lat": 20.7480807, "lng": -103.3430855, "status": "STARTED"}, "-OLAhPH5RCDxFGXoHwOz": {"at": 1741805692380, "lat": 20.7479249, "lng": -103.3431659, "status": "STARTED"}, "-OLAhPVHl6dxr7-9e6Sh": {"at": 1741805693290, "lat": 20.7477701, "lng": -103.3432598, "status": "STARTED"}, "-OLAhPlQ1Xgd_3Jr9hIG": {"at": 1741805694387, "lat": 20.7476277, "lng": -103.3433659, "status": "STARTED"}, "-OLAhPziCstinVUN9qNM": {"at": 1741805695303, "lat": 20.7474981, "lng": -103.3434827, "status": "STARTED"}, "-OLAhQESQ9hcXX2gj2qf": {"at": 1741805696310, "lat": 20.7473755, "lng": -103.3436095, "status": "STARTED"}, "-OLAhQU9umvPVyhSXjFN": {"at": 1741805697315, "lat": 20.7472574, "lng": -103.3437481, "status": "STARTED"}, "-OLAhQiN62RK7t5ED20r": {"at": 1741805698289, "lat": 20.7471413, "lng": -103.3438926, "status": "STARTED"}, "-OLAhQyNRjcwIqAQcwhe": {"at": 1741805699309, "lat": 20.7470383, "lng": -103.344041, "status": "STARTED"}, "-OLAhRCZBZOy6cuyvZSa": {"at": 1741805700285, "lat": 20.7469377, "lng": -103.3441914, "status": "STARTED"}, "-OLAhRS6wlKdII01OnIy": {"at": 1741805701280, "lat": 20.7468386, "lng": -103.3443435, "status": "STARTED"}, "-OLAhRh3tdqNIKhxEj6s": {"at": 1741805702300, "lat": 20.7467394, "lng": -103.3444896, "status": "STARTED"}, "-OLAhRwIB1MzNWxSqHdo": {"at": 1741805703274, "lat": 20.7466367, "lng": -103.3446361, "status": "STARTED"}, "-OLAhSAua9ZjVUARto7Y": {"at": 1741805704274, "lat": 20.7465375, "lng": -103.3447858, "status": "STARTED"}, "-OLAhSQWfho4gtWzpABO": {"at": 1741805705273, "lat": 20.7464377, "lng": -103.3449328, "status": "STARTED"}, "-OLAhSfY5txbMQWa_Q6L": {"at": 1741805706299, "lat": 20.746335, "lng": -103.3450695, "status": "STARTED"}, "-OLAhSvI59S7s4GnBAEQ": {"at": 1741805707308, "lat": 20.7462377, "lng": -103.3451934, "status": "STARTED"}, "-OLAhT9W9Q75JfB9JmZ5": {"at": 1741805708282, "lat": 20.7461415, "lng": -103.3453063, "status": "STARTED"}, "-OLAhTP8wtCGaWr2nw-4": {"at": 1741805709281, "lat": 20.7460508, "lng": -103.3454097, "status": "STARTED"}, "-OLAhTe89e2WOnKr10o2": {"at": 1741805710305, "lat": 20.7459642, "lng": -103.3455065, "status": "STARTED"}, "-OLAhTtIo5fCClTB68dC": {"at": 1741805711276, "lat": 20.7458857, "lng": -103.3455854, "status": "STARTED"}, "-OLAhU8MGC2YtqZZa-ZW": {"at": 1741805712303, "lat": 20.7458081, "lng": -103.3456473, "status": "STARTED"}, "-OLAhUc8gtGk27sh37Fg": {"at": 1741805714273, "lat": 20.7456723, "lng": -103.3457456, "status": "STARTED"}, "-OLAhV8Bw6m7w7uwhilD": {"at": 1741805716388, "lat": 20.7455675, "lng": -103.3458277, "status": "STARTED"}, "-OLAhVbV4ZNiChDwoUyb": {"at": 1741805718328, "lat": 20.745492, "lng": -103.3458829, "status": "STARTED"}, "-OLAhbyFTkbGy58hAZmN": {"at": 1741805748456, "lat": 20.7453809, "lng": -103.3459397, "status": "STARTED"}, "-OLAhcS9lFWOz8oM80le": {"at": 1741805750434, "lat": 20.7452849, "lng": -103.3460091, "status": "STARTED"}, "-OLAhcto5A_lRz7w9xiR": {"at": 1741805752268, "lat": 20.745173, "lng": -103.3460782, "status": "STARTED"}, "-OLAhdOM0ALsuWCjnyDf": {"at": 1741805754288, "lat": 20.7450498, "lng": -103.3461406, "status": "STARTED"}, "-OLAhdtYHcMKxKWiAejY": {"at": 1741805756348, "lat": 20.7449114, "lng": -103.3462025, "status": "STARTED"}, "-OLAheMg6Ge719pjhEyp": {"at": 1741805758276, "lat": 20.7447339, "lng": -103.3462848, "status": "STARTED"}, "-OLAhebYYdzAn2NTVF9m": {"at": 1741805759292, "lat": 20.7446117, "lng": -103.3463145, "status": "STARTED"}, "-OLAhesQMpGirvuVc9AF": {"at": 1741805760371, "lat": 20.7444919, "lng": -103.346349, "status": "STARTED"}, "-OLAhf5jtZB5M4eujUJg": {"at": 1741805761288, "lat": 20.7443775, "lng": -103.3463818, "status": "STARTED"}, "-OLAhfMO7zna0Ib1_lK7": {"at": 1741805762352, "lat": 20.7442674, "lng": -103.3464086, "status": "STARTED"}, "-OLAhfaWJffk6JmvKxD5": {"at": 1741805763321, "lat": 20.7441635, "lng": -103.3464303, "status": "STARTED"}, "-OLAhfpHkOUTBvNxEpH7": {"at": 1741805764266, "lat": 20.7440614, "lng": -103.3464468, "status": "STARTED"}, "-OLAhg77RWkc4CKGoIEk": {"at": 1741805765472, "lat": 20.7439574, "lng": -103.3464585, "status": "STARTED"}, "-OLAhgJS6Mc29BXDZC88": {"at": 1741805766262, "lat": 20.7438527, "lng": -103.3464668, "status": "STARTED"}, "-OLAhgZ2db4lhA2VGNYR": {"at": 1741805767258, "lat": 20.7437463, "lng": -103.346471, "status": "STARTED"}, "-OLAhgoUWfSmmKUWiE19": {"at": 1741805768309, "lat": 20.7436392, "lng": -103.3464656, "status": "STARTED"}, "-OLAhh3a5V0QSlJQQaU2": {"at": 1741805769342, "lat": 20.7435184, "lng": -103.3464578, "status": "STARTED"}, "-OLAhhIsJPGxXiqiaqjH": {"at": 1741805770319, "lat": 20.7433903, "lng": -103.3464469, "status": "STARTED"}, "-OLAhhXvoC8dcL3Vb6vm": {"at": 1741805771283, "lat": 20.7432597, "lng": -103.3464332, "status": "STARTED"}, "-OLAhhmlKxBcGJrTYwbA": {"at": 1741805772298, "lat": 20.7431276, "lng": -103.346413, "status": "STARTED"}, "-OLAhi1US_8xBJ7tNgdH": {"at": 1741805773303, "lat": 20.7429939, "lng": -103.3463833, "status": "STARTED"}, "-OLAhiHDAmq21wGW796B": {"at": 1741805774311, "lat": 20.7428557, "lng": -103.3463482, "status": "STARTED"}, "-OLAhiWwwrXj4tMyyKOh": {"at": 1741805775316, "lat": 20.7427122, "lng": -103.346298, "status": "STARTED"}, "-OLAhim5ReP0AABrRrKm": {"at": 1741805776350, "lat": 20.7425715, "lng": -103.3462428, "status": "STARTED"}, "-OLAhj-EI-Bg9_SZHCL9": {"at": 1741805777256, "lat": 20.7424299, "lng": -103.3461798, "status": "STARTED"}, "-OLAhjFlyD4FYgqyMlXk": {"at": 1741805778314, "lat": 20.7422881, "lng": -103.3461174, "status": "STARTED"}, "-OLAhjVBB2dK4ZM7khL5": {"at": 1741805779300, "lat": 20.7421452, "lng": -103.3460416, "status": "STARTED"}, "-OLAhjjzQIFJm2l5Wou_": {"at": 1741805780311, "lat": 20.7419992, "lng": -103.3459691, "status": "STARTED"}, "-OLAhjzlylhbu3Ot30qM": {"at": 1741805781320, "lat": 20.741863, "lng": -103.3458779, "status": "STARTED"}, "-OLAhkE3S0Ldfz3jOTs5": {"at": 1741805782301, "lat": 20.7417284, "lng": -103.3457813, "status": "STARTED"}, "-OLAhkTqoHBZyDqfJZOK": {"at": 1741805783310, "lat": 20.7415972, "lng": -103.3456808, "status": "STARTED"}, "-OLAhkiTwOo97q8kzagd": {"at": 1741805784310, "lat": 20.741479, "lng": -103.3455829, "status": "STARTED"}, "-OLAhky4kHVphej-65nP": {"at": 1741805785310, "lat": 20.7413656, "lng": -103.3454771, "status": "STARTED"}, "-OLAhlC_8plpsMGXXrJ7": {"at": 1741805786302, "lat": 20.7412504, "lng": -103.3453656, "status": "STARTED"}, "-OLAhlRlmPNF4vTXWsrn": {"at": 1741805787273, "lat": 20.7411368, "lng": -103.3452553, "status": "STARTED"}, "-OLAhlgt6VWVMfU4d6oE": {"at": 1741805788304, "lat": 20.7410294, "lng": -103.3451478, "status": "STARTED"}, "-OLAhlwEBWEcMLg74jBx": {"at": 1741805789287, "lat": 20.74092, "lng": -103.3450413, "status": "STARTED"}, "-OLAhmBhXImuntxpKyHl": {"at": 1741805790341, "lat": 20.740809, "lng": -103.3449419, "status": "STARTED"}, "-OLAhmQJiGHZ_QEK2Ftu": {"at": 1741805791277, "lat": 20.7407022, "lng": -103.3448392, "status": "STARTED"}, "-OLAhmfNWytSbBbLAZLv": {"at": 1741805792305, "lat": 20.7405957, "lng": -103.3447319, "status": "STARTED"}, "-OLAhmueXvPM6r1kcsWx": {"at": 1741805793283, "lat": 20.7404918, "lng": -103.3446293, "status": "STARTED"}, "-OLAhn9Q-zubInE2o2F3": {"at": 1741805794291, "lat": 20.7403829, "lng": -103.3445242, "status": "STARTED"}, "-OLAhnPIrhC6iK9qWy4u": {"at": 1741805795307, "lat": 20.740274, "lng": -103.3444165, "status": "STARTED"}, "-OLAhndsKWxr13iwid2r": {"at": 1741805796304, "lat": 20.7401651, "lng": -103.3443098, "status": "STARTED"}, "-OLAhntE9lujO27rwTmX": {"at": 1741805797287, "lat": 20.7400552, "lng": -103.3442002, "status": "STARTED"}, "-OLAho89P8iMiiYAW_k7": {"at": 1741805798307, "lat": 20.7399437, "lng": -103.3440874, "status": "STARTED"}, "-OLAhoNXSHagoFGQfEPK": {"at": 1741805799291, "lat": 20.7398348, "lng": -103.343972, "status": "STARTED"}, "-OLAhoc6XFX_3v_ej1_r": {"at": 1741805800287, "lat": 20.739719, "lng": -103.3438538, "status": "STARTED"}, "-OLAhorYe1Mgs_ipovYF": {"at": 1741805801276, "lat": 20.7396004, "lng": -103.343734, "status": "STARTED"}, "-OLAhp70NZzIBLEgaNOl": {"at": 1741805802329, "lat": 20.7394821, "lng": -103.3436148, "status": "STARTED"}, "-OLAhpMArTSubNBLfNmu": {"at": 1741805803300, "lat": 20.7393591, "lng": -103.3434961, "status": "STARTED"}, "-OLAhpba3srEGDOsBk1m": {"at": 1741805804348, "lat": 20.739237, "lng": -103.3433717, "status": "STARTED"}, "-OLAhpqSmsNZ4KsXSI5M": {"at": 1741805805301, "lat": 20.7391137, "lng": -103.3432494, "status": "STARTED"}, "-OLAhq5xwNxR0scB8zFr": {"at": 1741805806357, "lat": 20.7389856, "lng": -103.3431264, "status": "STARTED"}, "-OLAhqKoDMNKET35BehP": {"at": 1741805807309, "lat": 20.7388572, "lng": -103.3430016, "status": "STARTED"}, "-OLAhq_k410heB2opu21": {"at": 1741805808328, "lat": 20.7387258, "lng": -103.3428736, "status": "STARTED"}, "-OLAhqoEzDKG3jkPOsxu": {"at": 1741805809256, "lat": 20.7385892, "lng": -103.3427572, "status": "STARTED"}, "-OLAhr3ilvNVSvBMft6e": {"at": 1741805810311, "lat": 20.7384477, "lng": -103.3426441, "status": "STARTED"}, "-OLAhrJAHoUvMQ__JEW6": {"at": 1741805811300, "lat": 20.738302, "lng": -103.3425415, "status": "STARTED"}, "-OLAhrZ75YgzWrYlP6PM": {"at": 1741805812320, "lat": 20.7381476, "lng": -103.3424438, "status": "STARTED"}, "-OLAhrnIOds4FogojgEF": {"at": 1741805813291, "lat": 20.7379919, "lng": -103.3423568, "status": "STARTED"}, "-OLAhs2NrS4ceSBiVN4L": {"at": 1741805814320, "lat": 20.7378398, "lng": -103.3422789, "status": "STARTED"}, "-OLAhsH_Z69qDPqR85Kl": {"at": 1741805815294, "lat": 20.7376884, "lng": -103.342218, "status": "STARTED"}, "-OLAhsXCB28uhWjSgY_E": {"at": 1741805816293, "lat": 20.7375444, "lng": -103.3421624, "status": "STARTED"}, "-OLAhsmFfdrjC63_R7D9": {"at": 1741805817321, "lat": 20.7374029, "lng": -103.3421188, "status": "STARTED"}, "-OLAht1GYHxOLuInF3Y1": {"at": 1741805818346, "lat": 20.7372692, "lng": -103.3420841, "status": "STARTED"}, "-OLAhtGSr0djrubg1XE_": {"at": 1741805819317, "lat": 20.737141, "lng": -103.3420513, "status": "STARTED"}, "-OLAhtVp6SEtSdLFrKQG": {"at": 1741805820301, "lat": 20.7370114, "lng": -103.3420245, "status": "STARTED"}, "-OLAhtkX_U4Qri4yege8": {"at": 1741805821306, "lat": 20.7368774, "lng": -103.3420074, "status": "STARTED"}, "-OLAhtzkqP3RjaHSgJmn": {"at": 1741805822280, "lat": 20.736747, "lng": -103.3420003, "status": "STARTED"}, "-OLAhuEdPHO7cpj_blgi": {"at": 1741805823297, "lat": 20.7366168, "lng": -103.3419954, "status": "STARTED"}, "-OLAhuU3y0W4LzAfR5rp": {"at": 1741805824284, "lat": 20.7364823, "lng": -103.3419897, "status": "STARTED"}, "-OLAhuj9IgoNr9CrpLbo": {"at": 1741805825314, "lat": 20.7363471, "lng": -103.34199, "status": "STARTED"}, "-OLAhuyRy_BOgi37Etxx": {"at": 1741805826292, "lat": 20.7362075, "lng": -103.3419982, "status": "STARTED"}, "-OLAhvD5JbIFop3g02j_": {"at": 1741805827294, "lat": 20.7360686, "lng": -103.3420102, "status": "STARTED"}, "-OLAhvSuYiNpLlT41zMM": {"at": 1741805828306, "lat": 20.7359284, "lng": -103.3420325, "status": "STARTED"}, "-OLAhvhSsM5UL9s5TAHo": {"at": 1741805829301, "lat": 20.7357868, "lng": -103.3420571, "status": "STARTED"}, "-OLAhvxikKEn2umJ3phu": {"at": 1741805830343, "lat": 20.7356511, "lng": -103.3420968, "status": "STARTED"}, "-OLAhwBVXt7WXCOF6Y9p": {"at": 1741805831288, "lat": 20.7355193, "lng": -103.3421463, "status": "STARTED"}, "-OLAhwRcD0yH1niGpr94": {"at": 1741805832320, "lat": 20.735391, "lng": -103.3421937, "status": "STARTED"}, "-OLAhwg4kAGXHVjnsy-Z": {"at": 1741805833310, "lat": 20.7352657, "lng": -103.3422362, "status": "STARTED"}, "-OLAhwvF4ZJ903OwKTZ7": {"at": 1741805834280, "lat": 20.7351412, "lng": -103.3422769, "status": "STARTED"}, "-OLAhxAdn9JhJvQr0297": {"at": 1741805835330, "lat": 20.7350209, "lng": -103.34232, "status": "STARTED"}, "-OLAhxPebTKjOwaocS9b": {"at": 1741805836290, "lat": 20.7348969, "lng": -103.3423619, "status": "STARTED"}, "-OLAhxeDEo1V1eJGr8Zd": {"at": 1741805837286, "lat": 20.7347734, "lng": -103.342406, "status": "STARTED"}, "-OLAhxuRUBlde2bwXhW-": {"at": 1741805838324, "lat": 20.7346546, "lng": -103.3424493, "status": "STARTED"}, "-OLAhy8sPFeWlQ2dUnOE": {"at": 1741805839312, "lat": 20.7345365, "lng": -103.3424883, "status": "STARTED"}, "-OLAhyOPhXXlD5S8L8qI": {"at": 1741805840307, "lat": 20.7344213, "lng": -103.3425253, "status": "STARTED"}, "-OLAhycz7_Wut_HfCtOM": {"at": 1741805841303, "lat": 20.7343066, "lng": -103.3425661, "status": "STARTED"}, "-OLAhyrxBXHGKfFbQ48W": {"at": 1741805842262, "lat": 20.7341914, "lng": -103.342606, "status": "STARTED"}, "-OLAhz6qrfDhFAw9iMoi": {"at": 1741805843278, "lat": 20.7340779, "lng": -103.3426437, "status": "STARTED"}, "-OLAhzMjmf5llCeEmiQu": {"at": 1741805844296, "lat": 20.7339602, "lng": -103.3426775, "status": "STARTED"}, "-OLAhzbVE-_rw4WyuE0m": {"at": 1741805845304, "lat": 20.7338462, "lng": -103.3427148, "status": "STARTED"}, "-OLAhzrVRZSr-qU3uURj": {"at": 1741805846328, "lat": 20.7337288, "lng": -103.3427564, "status": "STARTED"}, "-OLAi-5goslkcO2ouDGE": {"at": 1741805847300, "lat": 20.7336107, "lng": -103.3427924, "status": "STARTED"}, "-OLAi-LFySL3Y1o3bxpS": {"at": 1741805848296, "lat": 20.7334923, "lng": -103.3428328, "status": "STARTED"}, "-OLAi-_wtJJbzZo6Pqvp": {"at": 1741805849300, "lat": 20.7333739, "lng": -103.3428715, "status": "STARTED"}, "-OLAi-pXB8WJeGbAwAnt": {"at": 1741805850299, "lat": 20.7332614, "lng": -103.3429056, "status": "STARTED"}, "-OLAi04Jv4JwMhdMyPY8": {"at": 1741805851309, "lat": 20.7331465, "lng": -103.342943, "status": "STARTED"}, "-OLAi0Jg1w5F-5qV3sgy": {"at": 1741805852292, "lat": 20.7330298, "lng": -103.3429897, "status": "STARTED"}, "-OLAi0ZoJwtThhwBN93p": {"at": 1741805853323, "lat": 20.7329117, "lng": -103.3430315, "status": "STARTED"}, "-OLAi0nwcF_sKFqdDGH7": {"at": 1741805854292, "lat": 20.7327872, "lng": -103.3430702, "status": "STARTED"}, "-OLAi129UXyY6D4aY21E": {"at": 1741805855267, "lat": 20.7326648, "lng": -103.3431111, "status": "STARTED"}, "-OLAi1IkX1xh96Xn5BgN": {"at": 1741805856328, "lat": 20.7325461, "lng": -103.3431512, "status": "STARTED"}, "-OLAi1XiNksNMQIhmxzi": {"at": 1741805857287, "lat": 20.7324343, "lng": -103.3431898, "status": "STARTED"}, "-OLAi1mr1BfQng_ecUuw": {"at": 1741805858319, "lat": 20.732329, "lng": -103.3432266, "status": "STARTED"}, "-OLAi21EsF3MEtBG-GId": {"at": 1741805859303, "lat": 20.7322305, "lng": -103.3432644, "status": "STARTED"}, "-OLAi2HnVIk7tVOJqOAQ": {"at": 1741805860363, "lat": 20.7321332, "lng": -103.3433028, "status": "STARTED"}, "-OLAi2lXwDNrSo0vVdh4": {"at": 1741805862329, "lat": 20.7319692, "lng": -103.343349, "status": "STARTED"}, "-OLAi3GNwiT8Z-0C8c5M": {"at": 1741805864369, "lat": 20.7318639, "lng": -103.3433724, "status": "STARTED"}, "-OLAi9aYefjcLi7RNRCT": {"at": 1741805890299, "lat": 20.7317617, "lng": -103.3434203, "status": "STARTED"}, "-OLAiA5DnjChZpo70lMO": {"at": 1741805892326, "lat": 20.7316749, "lng": -103.3434553, "status": "STARTED"}, "-OLAiA_IP8hRupqWo76k": {"at": 1741805894315, "lat": 20.7315468, "lng": -103.3434932, "status": "STARTED"}, "-OLAiB3HhOu4zDd01TEf": {"at": 1741805896299, "lat": 20.731398, "lng": -103.3435414, "status": "STARTED"}, "-OLAiBYMcmsr_mrFxsrO": {"at": 1741805898287, "lat": 20.7312423, "lng": -103.3435869, "status": "STARTED"}, "-OLAiC26NgZsd1SzR-OA": {"at": 1741805900319, "lat": 20.7310802, "lng": -103.3436382, "status": "STARTED"}, "-OLAiCY88Tp23qbSVj4h": {"at": 1741805902370, "lat": 20.7309299, "lng": -103.3436854, "status": "STARTED"}, "-OLAiD0mG3bnnCj3avSi": {"at": 1741805904330, "lat": 20.730813, "lng": -103.3437193, "status": "STARTED"}, "-OLAiDjl-s0bJy9Yyy5-": {"at": 1741805907273, "lat": 20.730698, "lng": -103.3437564, "status": "STARTED"}, "-OLAiETvWVuvRvSVE3lJ": {"at": 1741805910292, "lat": 20.7305984, "lng": -103.3437858, "status": "STARTED"}, "-OLAiFxDXV_mh6PwKBwC": {"at": 1741805916326, "lat": 20.7304976, "lng": -103.3438141, "status": "STARTED"}, "-OLAiN1-8vzmovIjs1tk": {"at": 1741805945304, "lat": 20.7303644, "lng": -103.3438639, "status": "STARTED"}, "-OLAiNXA7piFlUjLp5_I": {"at": 1741805947360, "lat": 20.7302693, "lng": -103.3438927, "status": "STARTED"}, "-OLAiO-Tp32py0i-R4F2": {"at": 1741805949302, "lat": 20.7301743, "lng": -103.3439275, "status": "STARTED"}, "-OLAiOV0HEPh3-_4WZXD": {"at": 1741805951321, "lat": 20.7300668, "lng": -103.343962, "status": "STARTED"}, "-OLAiOzJHXB9oh4C2oPm": {"at": 1741805953324, "lat": 20.7299531, "lng": -103.3440016, "status": "STARTED"}, "-OLAiPTdYvlz2MyYFa3z": {"at": 1741805955330, "lat": 20.7298527, "lng": -103.3440378, "status": "STARTED"}, "-OLAiQCtqwFOnTxlyiMM": {"at": 1741805958352, "lat": 20.7297451, "lng": -103.3440786, "status": "STARTED"}, "-OLAiQwQAEAWth36wJep": {"at": 1741805961331, "lat": 20.7296522, "lng": -103.3441085, "status": "STARTED"}, "-OLAiS8t0VjA5ihi9r3-": {"at": 1741805966289, "lat": 20.7295586, "lng": -103.3441439, "status": "STARTED"}, "-OLAk8OsdSCFQci7thLB": {"at": 1741806409500, "lat": 20.7181, "lng": -103.3323717, "status": "STARTED"}, "-OLAkZLE2fz0BoruZWHY": {"at": 1741806519858, "lat": 20.7179386, "lng": -103.332319, "status": "STARTED"}, "-OLAkZk4-JPsMBTZppMM": {"at": 1741806521513, "lat": 20.7178231, "lng": -103.3323078, "status": "STARTED"}, "-OLAkZw9mhYsTNLsmIuS": {"at": 1741806522287, "lat": 20.7176926, "lng": -103.3323155, "status": "STARTED"}, "-OLAk_8-cEv2VdlV9S7m": {"at": 1741806523108, "lat": 20.7175674, "lng": -103.3323226, "status": "STARTED"}, "-OLAk_CasNnpkvAsU9Kx": {"at": 1741806523403, "lat": 20.7174746, "lng": -103.3323327, "status": "STARTED"}, "-OLAk_kmm7VBSergmHYl": {"at": 1741806525651, "lat": 20.7173877, "lng": -103.3323621, "status": "STARTED"}, "-OLAkaQnJCND_7yskhoS": {"at": 1741806528394, "lat": 20.7172983, "lng": -103.3323758, "status": "STARTED"}, "-OLAkavaaSof0pfszqcG": {"at": 1741806530443, "lat": 20.7171865, "lng": -103.3323977, "status": "STARTED"}, "-OLAkbXbNAE7hLfqCTLx": {"at": 1741806532940, "lat": 20.7170185, "lng": -103.3324205, "status": "STARTED"}, "-OLAkbtM6tQVHyKEs44h": {"at": 1741806534396, "lat": 20.7168541, "lng": -103.3324408, "status": "STARTED"}, "-OLAkcSzRBnqYQ8Xmff2": {"at": 1741806536740, "lat": 20.7167251, "lng": -103.3324606, "status": "STARTED"}, "-OLAkd61tpdVc0CItcEQ": {"at": 1741806539367, "lat": 20.7166143, "lng": -103.3324614, "status": "STARTED"}, "-OLAkdfydjnb8NzTsul7": {"at": 1741806541731, "lat": 20.7165135, "lng": -103.3324446, "status": "STARTED"}, "-OLAke4vh-AkVPi9wL_z": {"at": 1741806543392, "lat": 20.7164097, "lng": -103.3324218, "status": "STARTED"}, "-OLAkensb7hczTyACL0g": {"at": 1741806546333, "lat": 20.7162894, "lng": -103.332379, "status": "STARTED"}, "-OLAkfH30ex_vNEQ0usG": {"at": 1741806548265, "lat": 20.7161908, "lng": -103.332316, "status": "STARTED"}, "-OLAkflxf3q1gT6uPEyX": {"at": 1741806550306, "lat": 20.7160652, "lng": -103.332206, "status": "STARTED"}, "-OLAkgI8pmGj3QNdBAnp": {"at": 1741806552430, "lat": 20.7159559, "lng": -103.3320842, "status": "STARTED"}, "-OLAkglYQ5msmM3YFjHI": {"at": 1741806554376, "lat": 20.7158482, "lng": -103.3319432, "status": "STARTED"}, "-OLAkhI9tR8fgBMVdCmS": {"at": 1741806556527, "lat": 20.7157499, "lng": -103.3318173, "status": "STARTED"}, "-OLAkhiuLpJUtMYrAYHk": {"at": 1741806558302, "lat": 20.7156586, "lng": -103.3316816, "status": "STARTED"}, "-OLAkiDF56Tuq2CFCC2m": {"at": 1741806560308, "lat": 20.7155331, "lng": -103.3315617, "status": "STARTED"}, "-OLAkihGpr6yS2qfnx95": {"at": 1741806562293, "lat": 20.7153925, "lng": -103.331447, "status": "STARTED"}, "-OLAkjCT_ipYqME0H4zO": {"at": 1741806564351, "lat": 20.7152637, "lng": -103.3313716, "status": "STARTED"}, "-OLAkjgLPreiiGQp5d0M": {"at": 1741806566330, "lat": 20.7151705, "lng": -103.3312881, "status": "STARTED"}, "-OLAkkQ5wBNHbCafmW9f": {"at": 1741806569323, "lat": 20.7150504, "lng": -103.3312371, "status": "STARTED"}, "-OLAkktE4z9dFc0uTptE": {"at": 1741806571251, "lat": 20.714928, "lng": -103.3312015, "status": "STARTED"}, "-OLAklO7zpi9XjwEsGxR": {"at": 1741806573293, "lat": 20.714797, "lng": -103.3311673, "status": "STARTED"}, "-OLAkls6reE93Tx-EQ4r": {"at": 1741806575276, "lat": 20.7146899, "lng": -103.3311271, "status": "STARTED"}, "-OLAkmM_YJh8ZjCOVCzl": {"at": 1741806577289, "lat": 20.7145816, "lng": -103.3310942, "status": "STARTED"}, "-OLAkmrF7xlCKCZiHzIa": {"at": 1741806579315, "lat": 20.714451, "lng": -103.3310372, "status": "STARTED"}, "-OLAknMIwCY5WfouZeaw": {"at": 1741806581367, "lat": 20.7143295, "lng": -103.3309827, "status": "STARTED"}, "-OLAknphiWGnacZAqPcm": {"at": 1741806583313, "lat": 20.7142168, "lng": -103.3309578, "status": "STARTED"}, "-OLAkoJYp29wpq9989Ef": {"at": 1741806585287, "lat": 20.714104, "lng": -103.3309208, "status": "STARTED"}, "-OLAkonjajXfKGX2FK8_": {"at": 1741806587283, "lat": 20.7139781, "lng": -103.3308902, "status": "STARTED"}, "-OLAkpI71GEdG4qVmxUl": {"at": 1741806589292, "lat": 20.7138567, "lng": -103.3308555, "status": "STARTED"}, "-OLAkpmbcPE12TDhasdN": {"at": 1741806591307, "lat": 20.7137347, "lng": -103.3307748, "status": "STARTED"}, "-OLAkqGtKLvUM6TuXV_b": {"at": 1741806593310, "lat": 20.7136107, "lng": -103.3307134, "status": "STARTED"}, "-OLAkqkga9ko7TAYwwu9": {"at": 1741806595279, "lat": 20.7134865, "lng": -103.3306719, "status": "STARTED"}, "-OLAkrFBgWynB5GMi9SL": {"at": 1741806597296, "lat": 20.7133815, "lng": -103.3306446, "status": "STARTED"}, "-OLAkrjIAlNS9IS4G1Oa": {"at": 1741806599287, "lat": 20.7132542, "lng": -103.3306109, "status": "STARTED"}, "-OLAkrymiu5FU787b7p4": {"at": 1741806600278, "lat": 20.7131567, "lng": -103.3305847, "status": "STARTED"}, "-OLAksTPRCzONBly4KRm": {"at": 1741806602303, "lat": 20.7130056, "lng": -103.330552, "status": "STARTED"}, "-OLAkswKPGkEB-LXbD4T": {"at": 1741806604217, "lat": 20.7128625, "lng": -103.330526, "status": "STARTED"}, "-OLAktSFNS7RLJrGJHD_": {"at": 1741806606325, "lat": 20.7127125, "lng": -103.3304761, "status": "STARTED"}, "-OLAktvQW-CcKQu6bLhW": {"at": 1741806608254, "lat": 20.7125638, "lng": -103.3304103, "status": "STARTED"}, "-OLAkuQrfS7DszjoxmsJ": {"at": 1741806610331, "lat": 20.7124353, "lng": -103.3303745, "status": "STARTED"}, "-OLAkuvSylsur0fFN48w": {"at": 1741806612354, "lat": 20.7123386, "lng": -103.3303758, "status": "STARTED"}, "-OLAkvdKBuJlB8gLQS1I": {"at": 1741806615289, "lat": 20.7122351, "lng": -103.3304038, "status": "STARTED"}, "-OLAkwcn9RB_PQkCP3Am": {"at": 1741806619352, "lat": 20.7121553, "lng": -103.3304853, "status": "STARTED"}, "-OLAkxLW9sg9qKrmBXzM": {"at": 1741806622277, "lat": 20.7121178, "lng": -103.3306042, "status": "STARTED"}, "-OLAky4mviAqeqmptJyt": {"at": 1741806625302, "lat": 20.7120674, "lng": -103.3306855, "status": "STARTED"}, "-OLAkz3TmRuGScfziurR": {"at": 1741806629314, "lat": 20.7120064, "lng": -103.3307965, "status": "STARTED"}, "-OLAkzmw9lvboREaoFUb": {"at": 1741806632289, "lat": 20.7119132, "lng": -103.3309127, "status": "STARTED"}, "-OLAl-I7TKcJ1EKd1sCt": {"at": 1741806634349, "lat": 20.7118125, "lng": -103.3309738, "status": "STARTED"}, "-OLAl-kwgGCnR_UOsgnU": {"at": 1741806636255, "lat": 20.7116848, "lng": -103.3310145, "status": "STARTED"}, "-OLAl0GcJa82W6HK15vi": {"at": 1741806638348, "lat": 20.7115573, "lng": -103.3310324, "status": "STARTED"}, "-OLAl0k57odUoDhlVNHU": {"at": 1741806640298, "lat": 20.711454, "lng": -103.3310544, "status": "STARTED"}, "-OLAl1Do-fcUxlxRk40E": {"at": 1741806642263, "lat": 20.7113539, "lng": -103.331078, "status": "STARTED"}, "-OLAl1jZpDMzV-bYkRBb": {"at": 1741806644360, "lat": 20.711247, "lng": -103.3310823, "status": "STARTED"}, "-OLAl2C958zAviP8pzYE": {"at": 1741806646255, "lat": 20.711155, "lng": -103.3310885, "status": "STARTED"}, "-OLAl8ItJpwKFcMQUiD-": {"at": 1741806671262, "lat": 20.7110499, "lng": -103.3311006, "status": "STARTED"}, "-OLAl9mY44FoGX3M6pMu": {"at": 1741806677320, "lat": 20.7109408, "lng": -103.3311295, "status": "STARTED"}, "-OLAlAW7_Wl6TpyAzPbS": {"at": 1741806680301, "lat": 20.7107986, "lng": -103.3311611, "status": "STARTED"}, "-OLAlB-8otd42ouSO-ma": {"at": 1741806682284, "lat": 20.7106043, "lng": -103.3311943, "status": "STARTED"}, "-OLAlBFQBv8GzZRE-tN-": {"at": 1741806683328, "lat": 20.7104959, "lng": -103.3312139, "status": "STARTED"}, "-OLAlBUgFQlYZHSAKzfF": {"at": 1741806684305, "lat": 20.7103895, "lng": -103.3312339, "status": "STARTED"}, "-OLAlBjWop2Z1svVGWf-": {"at": 1741806685318, "lat": 20.7102936, "lng": -103.3312559, "status": "STARTED"}, "-OLAlBySdXuIpJZWx_VV": {"at": 1741806686272, "lat": 20.7101961, "lng": -103.331285, "status": "STARTED"}, "-OLAlCDANvy7ISgeTvdO": {"at": 1741806687279, "lat": 20.7101023, "lng": -103.331309, "status": "STARTED"}, "-OLAlChCQwGGjtB-BUHE": {"at": 1741806689265, "lat": 20.7099373, "lng": -103.3313483, "status": "STARTED"}, "-OLAlDCGUhsMvu9Hp0PB": {"at": 1741806691317, "lat": 20.7097738, "lng": -103.3313893, "status": "STARTED"}, "-OLAlDfaJ6vVyHxN24YL": {"at": 1741806693258, "lat": 20.7096041, "lng": -103.3314127, "status": "STARTED"}, "-OLAlEANZbMD8mSKN8Qa": {"at": 1741806695292, "lat": 20.70941, "lng": -103.3314259, "status": "STARTED"}, "-OLAlEQ0sB9DHfAzQtsm": {"at": 1741806696294, "lat": 20.7093199, "lng": -103.3314422, "status": "STARTED"}, "-OLAlEeJW1rizlQI0SeK": {"at": 1741806697273, "lat": 20.7092307, "lng": -103.3314569, "status": "STARTED"}, "-OLAlF8acCrrYr7wg8JV": {"at": 1741806699275, "lat": 20.7090615, "lng": -103.331491, "status": "STARTED"}, "-OLAlFddMBULiGbBEAko": {"at": 1741806701325, "lat": 20.7088905, "lng": -103.3315164, "status": "STARTED"}, "-OLAlFsaUH8Kz5as0w6S": {"at": 1741806702283, "lat": 20.7087996, "lng": -103.3315294, "status": "STARTED"}, "-OLAlGNKOGrj0Yit5VES": {"at": 1741806704313, "lat": 20.7086199, "lng": -103.3315464, "status": "STARTED"}, "-OLAlGbSg0GGWPWnzoIY": {"at": 1741806705282, "lat": 20.7085251, "lng": -103.3315503, "status": "STARTED"}, "-OLAlGrSvEbjbJhwWLcj": {"at": 1741806706305, "lat": 20.7084308, "lng": -103.3315492, "status": "STARTED"}, "-OLAlH6YIelfUHXUULpA": {"at": 1741806707336, "lat": 20.7083403, "lng": -103.331557, "status": "STARTED"}, "-OLAlH_XzR0pfXf-zyiu": {"at": 1741806709254, "lat": 20.7081678, "lng": -103.3316211, "status": "STARTED"}, "-OLAlHpaoyV-Y_gDTPGS": {"at": 1741806710283, "lat": 20.7080703, "lng": -103.3316317, "status": "STARTED"}, "-OLAlI4Qxxmudi7FmGrt": {"at": 1741806711296, "lat": 20.7079661, "lng": -103.3316427, "status": "STARTED"}, "-OLAlIZQjaztRksQ-Mp1": {"at": 1741806713279, "lat": 20.7077989, "lng": -103.3316529, "status": "STARTED"}, "-OLAlJ2izWdGrGryB85E": {"at": 1741806715282, "lat": 20.7076733, "lng": -103.3316697, "status": "STARTED"}, "-OLAlJY1X7v_af5L-cus": {"at": 1741806717286, "lat": 20.7075409, "lng": -103.3316863, "status": "STARTED"}, "-OLAlK1PNGF1Jmsvlr71": {"at": 1741806719294, "lat": 20.7073937, "lng": -103.331705, "status": "STARTED"}, "-OLAlKWwwM8LyKxUODQM": {"at": 1741806721313, "lat": 20.7072501, "lng": -103.3317624, "status": "STARTED"}, "-OLAlKlsXyZD1oUVfhzc": {"at": 1741806722332, "lat": 20.7071619, "lng": -103.3317831, "status": "STARTED"}, "-OLAlL0XfQDJ53AEkKNM": {"at": 1741806723334, "lat": 20.70707, "lng": -103.3317949, "status": "STARTED"}, "-OLAlLFhWiNv5M_KQieg": {"at": 1741806724305, "lat": 20.7069775, "lng": -103.3318044, "status": "STARTED"}, "-OLAlLXC1PS6ksRawS0p": {"at": 1741806725425, "lat": 20.7068879, "lng": -103.3318195, "status": "STARTED"}, "-OLAlLymb8OzN3PxhQ-J": {"at": 1741806727255, "lat": 20.706721, "lng": -103.3318438, "status": "STARTED"}, "-OLAlMSwHj_ektG9ZxhR": {"at": 1741806729247, "lat": 20.706546, "lng": -103.3318695, "status": "STARTED"}, "-OLAlMxmKBMvhkF8RZUS": {"at": 1741806731285, "lat": 20.7063783, "lng": -103.3319034, "status": "STARTED"}, "-OLAlNU-ElxOTyQ2bric": {"at": 1741806733413, "lat": 20.7062324, "lng": -103.3319467, "status": "STARTED"}, "-OLAlNwBiJwAEv8UmvHI": {"at": 1741806735281, "lat": 20.7060803, "lng": -103.3319697, "status": "STARTED"}, "-OLAlOQH3EvH7I0i74wi": {"at": 1741806737270, "lat": 20.7059529, "lng": -103.3319879, "status": "STARTED"}, "-OLAlOvleLEJDI2lrxTd": {"at": 1741806739349, "lat": 20.7058582, "lng": -103.3319984, "status": "STARTED"}, "-OLAlPQZYfSJUbsmr9xE": {"at": 1741806741385, "lat": 20.7057682, "lng": -103.3320171, "status": "STARTED"}, "-OLAlPtsNVx4_tkhusWh": {"at": 1741806743324, "lat": 20.7056478, "lng": -103.3320376, "status": "STARTED"}, "-OLAlQNN-ev5Au5RipTL": {"at": 1741806745276, "lat": 20.7054935, "lng": -103.332057, "status": "STARTED"}, "-OLAlQdwAc8a_ZkQa5kL": {"at": 1741806746401, "lat": 20.7054014, "lng": -103.332063, "status": "STARTED"}, "-OLAlQu1vzwMcg9ye4Ob": {"at": 1741806747431, "lat": 20.7053105, "lng": -103.332074, "status": "STARTED"}, "-OLAlRM7rEvdmAoUYFY4": {"at": 1741806749293, "lat": 20.7051472, "lng": -103.3320965, "status": "STARTED"}, "-OLAlRqt6uiCSjvw0WjA": {"at": 1741806751325, "lat": 20.7049902, "lng": -103.3321307, "status": "STARTED"}, "-OLAlSM9h-VhtLcSe7nb": {"at": 1741806753390, "lat": 20.7048453, "lng": -103.332149, "status": "STARTED"}, "-OLAlSqso4FweXp3Ao0a": {"at": 1741806755420, "lat": 20.7047015, "lng": -103.3321609, "status": "STARTED"}, "-OLAlTJU_j1wPpCHjv0P": {"at": 1741806757315, "lat": 20.7045793, "lng": -103.33216, "status": "STARTED"}, "-OLAlTnZ27pBgAZqR_pp": {"at": 1741806759304, "lat": 20.7044737, "lng": -103.3322019, "status": "STARTED"}, "-OLAlUHOkjFDSEe-77mT": {"at": 1741806761278, "lat": 20.7043665, "lng": -103.3322222, "status": "STARTED"}, "-OLAlV-rYZpthry8Y1eH": {"at": 1741806764251, "lat": 20.7042602, "lng": -103.3322787, "status": "STARTED"}, "-OLAlVjw9ASHiQbdj3YD": {"at": 1741806767265, "lat": 20.7041579, "lng": -103.3323019, "status": "STARTED"}, "-OLAlWUL3dy5HkEaHbce": {"at": 1741806770298, "lat": 20.7040496, "lng": -103.3323082, "status": "STARTED"}, "-OLAlWyljfFwFlpODWMm": {"at": 1741806772310, "lat": 20.7039328, "lng": -103.3323197, "status": "STARTED"}, "-OLAlXTPzOl2z6m0ucES": {"at": 1741806774334, "lat": 20.7037941, "lng": -103.3323485, "status": "STARTED"}, "-OLAlXwkv8pGm3RGLz26": {"at": 1741806776277, "lat": 20.7036417, "lng": -103.3323718, "status": "STARTED"}, "-OLAlYRMk-m39_aRss6J": {"at": 1741806778298, "lat": 20.7034746, "lng": -103.3323945, "status": "STARTED"}, "-OLAlYfiWhHMm-eqGFp0": {"at": 1741806779282, "lat": 20.7033838, "lng": -103.3324078, "status": "STARTED"}, "-OLAlYwP_WQgbBQMutoJ": {"at": 1741806780350, "lat": 20.7032893, "lng": -103.3324263, "status": "STARTED"}, "-OLAlZARmHJG5NMr6yN3": {"at": 1741806781312, "lat": 20.7031948, "lng": -103.3324495, "status": "STARTED"}, "-OLAlZe6xDlo31uYY7pW": {"at": 1741806783268, "lat": 20.7030339, "lng": -103.3324935, "status": "STARTED"}, "-OLAl_93iDaOpFDKzgtW": {"at": 1741806785320, "lat": 20.7028811, "lng": -103.3325338, "status": "STARTED"}, "-OLAl_dJ4YrePzH7xF2Q": {"at": 1741806787321, "lat": 20.7027417, "lng": -103.3325677, "status": "STARTED"}, "-OLAla8nnNRhMYSMH_C5": {"at": 1741806789397, "lat": 20.7026153, "lng": -103.3325855, "status": "STARTED"}, "-OLAlabOE0t6FgLptmrh": {"at": 1741806791294, "lat": 20.7025199, "lng": -103.3325922, "status": "STARTED"}, "-OLAlbrxwuED9HWDdOhr": {"at": 1741806796449, "lat": 20.7024243, "lng": -103.3326153, "status": "STARTED"}, "-OLAlca5fS_kIU_laj7X": {"at": 1741806799403, "lat": 20.7023193, "lng": -103.3326092, "status": "STARTED"}, "-OLAld3XcVgyrxRRNmR_": {"at": 1741806801351, "lat": 20.7022135, "lng": -103.3326152, "status": "STARTED"}, "-OLAldZy-fhsG5hV0kEf": {"at": 1741806803427, "lat": 20.702049, "lng": -103.3326365, "status": "STARTED"}, "-OLAldq05i0fd4CBTK0O": {"at": 1741806804518, "lat": 20.7019579, "lng": -103.3326509, "status": "STARTED"}, "-OLAle2fvpjGyPGkwS1M": {"at": 1741806805391, "lat": 20.7018621, "lng": -103.3326615, "status": "STARTED"}, "-OLAleX0GY1DdsvKlc4p": {"at": 1741806807333, "lat": 20.7016963, "lng": -103.3326998, "status": "STARTED"}, "-OLAlf04Yc67E3ar84ud": {"at": 1741806809321, "lat": 20.7015546, "lng": -103.3327189, "status": "STARTED"}, "-OLAlfUUjykWEBh-Gp0Q": {"at": 1741806811268, "lat": 20.7013963, "lng": -103.332741, "status": "STARTED"}, "-OLAlfz0dSqIv_ojIHUq": {"at": 1741806813285, "lat": 20.7012369, "lng": -103.3327662, "status": "STARTED"}, "-OLAlgTrCENVZ6Vq9Gf1": {"at": 1741806815323, "lat": 20.7010779, "lng": -103.3327783, "status": "STARTED"}, "-OLAlgwv_p7b1svITp6-": {"at": 1741806817247, "lat": 20.7009263, "lng": -103.3328127, "status": "STARTED"}, "-OLAlhRTQ_rv7YRgaorq": {"at": 1741806819267, "lat": 20.7007703, "lng": -103.3328359, "status": "STARTED"}, "-OLAlhw-NyopEfF9tiOk": {"at": 1741806821275, "lat": 20.7006431, "lng": -103.3328583, "status": "STARTED"}, "-OLAliuZJWTJs8X9nhlA": {"at": 1741806825288, "lat": 20.7005511, "lng": -103.3328913, "status": "STARTED"}, "-OLAllpfoR-qpzXIK1-S": {"at": 1741806837263, "lat": 20.7004193, "lng": -103.3329346, "status": "STARTED"}, "-OLAlmJuEV1VhMNWV6dl": {"at": 1741806839260, "lat": 20.7002976, "lng": -103.3329541, "status": "STARTED"}, "-OLAlmpIpGEF2quv9tTh": {"at": 1741806841335, "lat": 20.7001579, "lng": -103.3329806, "status": "STARTED"}, "-OLAlnIgEoG1zcibZWKq": {"at": 1741806843281, "lat": 20.7000161, "lng": -103.3330013, "status": "STARTED"}, "-OLAlnmr9E9UWofc0n43": {"at": 1741806845270, "lat": 20.6998739, "lng": -103.3330159, "status": "STARTED"}, "-OLAloHeUBhiPrQ3k20z": {"at": 1741806847311, "lat": 20.6997297, "lng": -103.3330271, "status": "STARTED"}, "-OLAlol04R0EXfSFOujU": {"at": 1741806849254, "lat": 20.6995998, "lng": -103.3330573, "status": "STARTED"}, "-OLAlpFbTZLa2BuF91d3": {"at": 1741806851276, "lat": 20.6994884, "lng": -103.3330862, "status": "STARTED"}, "-OLAlpkGuQdottx60E_4": {"at": 1741806853302, "lat": 20.6993809, "lng": -103.3331157, "status": "STARTED"}, "-OLAlqFFryfXdgzEUER8": {"at": 1741806855349, "lat": 20.6992539, "lng": -103.3331449, "status": "STARTED"}, "-OLAlqi1Zb8hqRHR_Qcr": {"at": 1741806857254, "lat": 20.6991294, "lng": -103.3331396, "status": "STARTED"}, "-OLAlrCpZ7-9ncu12Yt5": {"at": 1741806859289, "lat": 20.6990157, "lng": -103.3331589, "status": "STARTED"}, "-OLAlrhB3nfyvhhd6KPc": {"at": 1741806861295, "lat": 20.698926, "lng": -103.333196, "status": "STARTED"}, "-OLAmCCXwz9tJRYTLG39": {"at": 1741806949381, "lat": 20.6988186, "lng": -103.333257, "status": "STARTED"}, "-OLAmCvcdxGCqmFql4k0": {"at": 1741806952332, "lat": 20.6987256, "lng": -103.3332544, "status": "STARTED"}, "-OLAmDuZjXlixlkNarQD": {"at": 1741806956360, "lat": 20.6986264, "lng": -103.3332451, "status": "STARTED"}, "-OLAmEe0QR32jdHB5XUU": {"at": 1741806959398, "lat": 20.6985479, "lng": -103.3331737, "status": "STARTED"}, "-OLAmF7rrMOyZNB8D8FK": {"at": 1741806961372, "lat": 20.6984883, "lng": -103.3330923, "status": "STARTED"}, "-OLAmFaFkBqXIbAnQoOr": {"at": 1741806963252, "lat": 20.6984319, "lng": -103.3329792, "status": "STARTED"}, "-OLAmG4qzfx8MB5NZl2Y": {"at": 1741806965274, "lat": 20.6983999, "lng": -103.3328383, "status": "STARTED"}, "-OLAmG_DSlHzqc2X229N": {"at": 1741806967282, "lat": 20.6983452, "lng": -103.33267, "status": "STARTED"}, "-OLAmGoeYcIDtfD8MO8d": {"at": 1741806968269, "lat": 20.6983099, "lng": -103.3325798, "status": "STARTED"}, "-OLAmHJmcvRehxnkwt7v": {"at": 1741806970327, "lat": 20.6982334, "lng": -103.3324201, "status": "STARTED"}, "-OLAmHnZ_1A5x57o6Xkc": {"at": 1741806972296, "lat": 20.6981582, "lng": -103.3322565, "status": "STARTED"}, "-OLAmIJ7X36iobEKkoXD": {"at": 1741806974380, "lat": 20.698084, "lng": -103.3321034, "status": "STARTED"}, "-OLAmIlj7zCm9c1e45ao": {"at": 1741806976275, "lat": 20.698005, "lng": -103.3319541, "status": "STARTED"}, "-OLAmJ-wat5WWrpIPlDF": {"at": 1741806977248, "lat": 20.6979525, "lng": -103.3318652, "status": "STARTED"}, "-OLAmJFoA3m5GXaIx7th": {"at": 1741806978265, "lat": 20.6978915, "lng": -103.3317652, "status": "STARTED"}, "-OLAmJW5aTUbObnItkE8": {"at": 1741806979306, "lat": 20.6978404, "lng": -103.331673, "status": "STARTED"}, "-OLAmJkhiAaTyF1jRIV1": {"at": 1741806980305, "lat": 20.6977936, "lng": -103.3315824, "status": "STARTED"}, "-OLAmKEro2bYi4YH6Ird": {"at": 1741806982298, "lat": 20.6977183, "lng": -103.3314269, "status": "STARTED"}, "-OLAmKjCdsTytb15-0gz": {"at": 1741806984305, "lat": 20.6976445, "lng": -103.3312798, "status": "STARTED"}, "-OLAmLESxxKfyXpkhds1": {"at": 1741806986369, "lat": 20.6975834, "lng": -103.3311346, "status": "STARTED"}, "-OLAmLhlwkVzDdvzVJI0": {"at": 1741806988307, "lat": 20.6975183, "lng": -103.3309966, "status": "STARTED"}, "-OLAmMDY--i0UEWYxM15": {"at": 1741806990407, "lat": 20.6974465, "lng": -103.3308264, "status": "STARTED"}, "-OLAmMhX6FLmxkpx_Fx0": {"at": 1741806992390, "lat": 20.6973792, "lng": -103.3306656, "status": "STARTED"}, "-OLAmNBFNU_G2DMzU4iX": {"at": 1741806994357, "lat": 20.6973283, "lng": -103.3305426, "status": "STARTED"}, "-OLAmbYU41ioSQVWRvQ1": {"at": 1741807057282, "lat": 20.6972735, "lng": -103.3304144, "status": "STARTED"}, "-OLAmc1_vB2zDg1nHcsS": {"at": 1741807059273, "lat": 20.697207, "lng": -103.3302962, "status": "STARTED"}, "-OLAmcXFBRdj7tsZS9p_": {"at": 1741807061298, "lat": 20.6971259, "lng": -103.3301098, "status": "STARTED"}, "-OLAmcl0u1NQPQ66aOOO": {"at": 1741807062245, "lat": 20.6970645, "lng": -103.329987, "status": "STARTED"}, "-OLAmd09RE2Nbsodt4Fw": {"at": 1741807063278, "lat": 20.6970217, "lng": -103.3298695, "status": "STARTED"}, "-OLAmdFfbj4YTQNUyIUn": {"at": 1741807064272, "lat": 20.6969849, "lng": -103.3297658, "status": "STARTED"}, "-OLAmdVLOUeqN85DWM0Z": {"at": 1741807065275, "lat": 20.6969482, "lng": -103.3296688, "status": "STARTED"}, "-OLAmdkhAPNwFjZMujSb": {"at": 1741807066322, "lat": 20.6969088, "lng": -103.3295778, "status": "STARTED"}, "-OLAmdzPgb85yKVyfRkp": {"at": 1741807067261, "lat": 20.6968618, "lng": -103.3294812, "status": "STARTED"}, "-OLAmeEbMF0jz2QUb-36": {"at": 1741807068299, "lat": 20.6968019, "lng": -103.3293645, "status": "STARTED"}, "-OLAmeUXO7m7c0NGrBaM": {"at": 1741807069319, "lat": 20.6967529, "lng": -103.3292604, "status": "STARTED"}, "-OLAmeiizGPPMtItqfIl": {"at": 1741807070288, "lat": 20.6966871, "lng": -103.3291596, "status": "STARTED"}, "-OLAmey1vYIL01T6XWUN": {"at": 1741807071266, "lat": 20.6966357, "lng": -103.3290678, "status": "STARTED"}, "-OLAmfD3i_yRx6ZtdMMh": {"at": 1741807072295, "lat": 20.6965863, "lng": -103.3289631, "status": "STARTED"}, "-OLAmfTYIhiz42Pvg92K": {"at": 1741807073351, "lat": 20.696535, "lng": -103.3288654, "status": "STARTED"}, "-OLAmfgfSEMaGbFmh2hN": {"at": 1741807074255, "lat": 20.6964779, "lng": -103.3287554, "status": "STARTED"}, "-OLAmfwXfa-kbnhzxMGE": {"at": 1741807075270, "lat": 20.6964245, "lng": -103.3286435, "status": "STARTED"}, "-OLAmgBQjxJv9-ASgq2r": {"at": 1741807076288, "lat": 20.6963666, "lng": -103.3285145, "status": "STARTED"}, "-OLAmgSAHGptQgK0vE-2": {"at": 1741807077359, "lat": 20.6963126, "lng": -103.3283891, "status": "STARTED"}, "-OLAmgfxq0tvLebvmBCb": {"at": 1741807078298, "lat": 20.6962635, "lng": -103.3282649, "status": "STARTED"}, "-OLAmgvGQGafo2Brh-ou": {"at": 1741807079282, "lat": 20.6962113, "lng": -103.3281404, "status": "STARTED"}, "-OLAmh9jxV72Y-oH1u50": {"at": 1741807080273, "lat": 20.6961596, "lng": -103.3280224, "status": "STARTED"}, "-OLAmhPJu_5H89QscW6H": {"at": 1741807081272, "lat": 20.6961026, "lng": -103.3279144, "status": "STARTED"}, "-OLAmheM3xNjVXbyT7vZ": {"at": 1741807082299, "lat": 20.6960411, "lng": -103.3278163, "status": "STARTED"}, "-OLAmhtPkErBCFCJVWoI": {"at": 1741807083261, "lat": 20.6959798, "lng": -103.327711, "status": "STARTED"}, "-OLAmi81UqKYvB72GjMa": {"at": 1741807084262, "lat": 20.6959255, "lng": -103.3276054, "status": "STARTED"}, "-OLAmiNt-vxNId2wa3HI": {"at": 1741807085278, "lat": 20.6958692, "lng": -103.3274989, "status": "STARTED"}, "-OLAmicDX5Rw9YkJYqDY": {"at": 1741807086258, "lat": 20.6958172, "lng": -103.3274028, "status": "STARTED"}, "-OLAmis5AJa4xM1U2f81": {"at": 1741807087274, "lat": 20.6957699, "lng": -103.3273152, "status": "STARTED"}, "-OLAmjMWZftvy9nqesav": {"at": 1741807089285, "lat": 20.6956969, "lng": -103.3271705, "status": "STARTED"}, "-OLAmjrJn0DcaowfGWqm": {"at": 1741807091321, "lat": 20.6956515, "lng": -103.3270847, "status": "STARTED"}, "-OLAmkLEZoqsklu8_z3e": {"at": 1741807093299, "lat": 20.6955786, "lng": -103.3269504, "status": "STARTED"}, "-OLAmkp5xixvTwUEh0fa": {"at": 1741807095271, "lat": 20.6955204, "lng": -103.3267911, "status": "STARTED"}, "-OLAmlJk1W2OtboDOsk9": {"at": 1741807097300, "lat": 20.6954441, "lng": -103.3266745, "status": "STARTED"}, "-OLAmlnU1SrapdHqjLsm": {"at": 1741807099267, "lat": 20.6953641, "lng": -103.3265361, "status": "STARTED"}, "-OLAmm2dtf8AFC5F_ZRZ": {"at": 1741807100298, "lat": 20.6953283, "lng": -103.3264358, "status": "STARTED"}, "-OLAmmIaKk7VG7zvBfnd": {"at": 1741807101323, "lat": 20.6952943, "lng": -103.3263344, "status": "STARTED"}, "-OLAmmY-R1MMNj0HIJhB": {"at": 1741807102307, "lat": 20.6952563, "lng": -103.3262383, "status": "STARTED"}, "-OLAmmlxrAffxf7AabKM": {"at": 1741807103266, "lat": 20.6952164, "lng": -103.3261396, "status": "STARTED"}, "-OLAmn0pa_F4fF7DZI6h": {"at": 1741807104281, "lat": 20.6951728, "lng": -103.3260452, "status": "STARTED"}, "-OLAmnGxiXZdpZbz57p5": {"at": 1741807105314, "lat": 20.6951215, "lng": -103.3259501, "status": "STARTED"}, "-OLAmnWerLbAIXRWOrdj": {"at": 1741807106318, "lat": 20.6950722, "lng": -103.3258497, "status": "STARTED"}, "-OLAmnks6nJB_Bmt375y": {"at": 1741807107293, "lat": 20.6950222, "lng": -103.3257535, "status": "STARTED"}, "-OLAmo-868neCZDTq_fD": {"at": 1741807108269, "lat": 20.6949666, "lng": -103.3256537, "status": "STARTED"}, "-OLAmoEpoamzam_MhDbE": {"at": 1741807109273, "lat": 20.6949131, "lng": -103.3255526, "status": "STARTED"}, "-OLAmoUSUBqn3i5nvdaG": {"at": 1741807110271, "lat": 20.6948603, "lng": -103.3254506, "status": "STARTED"}, "-OLAmoj8PBiqcosyXwFJ": {"at": 1741807111278, "lat": 20.6948116, "lng": -103.3253422, "status": "STARTED"}, "-OLAmp0-E-Ee9qLFVnOU": {"at": 1741807112403, "lat": 20.694762, "lng": -103.3252312, "status": "STARTED"}, "-OLAmpF9gOjsJzZj5CO6": {"at": 1741807113391, "lat": 20.6947155, "lng": -103.325121, "status": "STARTED"}, "-OLAmpU7eXnhwG6q3NA1": {"at": 1741807114349, "lat": 20.6946706, "lng": -103.3250075, "status": "STARTED"}, "-OLAmpm0KsLy9YswOvJS": {"at": 1741807115557, "lat": 20.6946151, "lng": -103.3249078, "status": "STARTED"}, "-OLAmpx-vGT8-TMBuvfA": {"at": 1741807116260, "lat": 20.6945721, "lng": -103.324816, "status": "STARTED"}, "-OLAmqRNqRnECfMmj-kH": {"at": 1741807118268, "lat": 20.6944935, "lng": -103.3246497, "status": "STARTED"}, "-OLAmqvpzjRlKVvFZJ6x": {"at": 1741807120282, "lat": 20.6944387, "lng": -103.3245201, "status": "STARTED"}, "-OLAmw7rcucGTPNTIhue": {"at": 1741807141592, "lat": 20.6943996, "lng": -103.3244264, "status": "STARTED"}, "-OLAmwozc69lGAc7HuDQ": {"at": 1741807144419, "lat": 20.6943414, "lng": -103.3242868, "status": "STARTED"}, "-OLAmxJmMY5I7-lwSNxU": {"at": 1741807146454, "lat": 20.6942735, "lng": -103.3241671, "status": "STARTED"}, "-OLAmxrP_8mzfXT_qLLA": {"at": 1741807148670, "lat": 20.6941966, "lng": -103.3240006, "status": "STARTED"}, "-OLAmy1hCT6BNrV3OTTb": {"at": 1741807149394, "lat": 20.6941472, "lng": -103.3239099, "status": "STARTED"}, "-OLAmyK4PZmKNwc7wXZX": {"at": 1741807150569, "lat": 20.6940764, "lng": -103.3238019, "status": "STARTED"}, "-OLAmyWy9IIEFQ1xEVhX": {"at": 1741807151394, "lat": 20.6940021, "lng": -103.3236808, "status": "STARTED"}, "-OLAmyphb67UvF9B5M2w": {"at": 1741807152657, "lat": 20.6939187, "lng": -103.3235599, "status": "STARTED"}, "-OLAmz1eAk6sR88HA_vI": {"at": 1741807153485, "lat": 20.6938653, "lng": -103.3234397, "status": "STARTED"}, "-OLAmzGwkuH0mh_mqbqc": {"at": 1741807154465, "lat": 20.6937896, "lng": -103.3233301, "status": "STARTED"}, "-OLAmzUlOi_lfZ2svBBC": {"at": 1741807155349, "lat": 20.693721, "lng": -103.3232238, "status": "STARTED"}, "-OLAmzj5tdI8EqDLA_y4": {"at": 1741807156331, "lat": 20.6936507, "lng": -103.323132, "status": "STARTED"}, "-OLAn-GxtDvSLMixFCT1": {"at": 1741807158562, "lat": 20.6934824, "lng": -103.3230242, "status": "STARTED"}, "-OLAn-URfKiumgkEqBTW": {"at": 1741807159425, "lat": 20.6933907, "lng": -103.3229714, "status": "STARTED"}, "-OLAn-l1GqPaVG4znB2c": {"at": 1741807160551, "lat": 20.693297, "lng": -103.3229184, "status": "STARTED"}, "-OLAn01JVazfwlFdNGdm": {"at": 1741807161656, "lat": 20.6931994, "lng": -103.3228601, "status": "STARTED"}, "-OLAn0CfXEguq49xlnQH": {"at": 1741807162384, "lat": 20.6931048, "lng": -103.3228062, "status": "STARTED"}, "-OLAn0WmI5rekqNBNA1k": {"at": 1741807163671, "lat": 20.6930202, "lng": -103.3227569, "status": "STARTED"}, "-OLAn0p_HGleMtbn_Ch3": {"at": 1741807164937, "lat": 20.6929322, "lng": -103.3227055, "status": "STARTED"}, "-OLAn0yPE8vHeyV-gETN": {"at": 1741807165503, "lat": 20.6928427, "lng": -103.3226484, "status": "STARTED"}, "-OLAn1Cy6e8ov9hcWkVe": {"at": 1741807166499, "lat": 20.6927557, "lng": -103.3225901, "status": "STARTED"}, "-OLAn1QSfIPzp5dxeipY": {"at": 1741807167362, "lat": 20.692661, "lng": -103.3225315, "status": "STARTED"}, "-OLAn1fpBNnk8FW4RBIq": {"at": 1741807168409, "lat": 20.6925661, "lng": -103.322474, "status": "STARTED"}, "-OLAn1taDk2UHwjmhJks": {"at": 1741807169291, "lat": 20.6924779, "lng": -103.322414, "status": "STARTED"}, "-OLAn2NJbD56LyRsaRlX": {"at": 1741807171257, "lat": 20.69233, "lng": -103.3223397, "status": "STARTED"}, "-OLAn2wuAZXfb61LJtlX": {"at": 1741807173599, "lat": 20.6921694, "lng": -103.3222396, "status": "STARTED"}, "-OLAn3A63B0GvOmA4Q9N": {"at": 1741807174507, "lat": 20.6920857, "lng": -103.3221869, "status": "STARTED"}, "-OLAn3O8Aqx47CH6f-k7": {"at": 1741807175406, "lat": 20.6920009, "lng": -103.3221309, "status": "STARTED"}, "-OLAn3crJNPyg81dGZXS": {"at": 1741807176411, "lat": 20.691916, "lng": -103.3220819, "status": "STARTED"}, "-OLAn3rIq4aSL3FHWoW_": {"at": 1741807177336, "lat": 20.6918278, "lng": -103.3220305, "status": "STARTED"}, "-OLAn49wyfkjfZAaLWI3": {"at": 1741807178593, "lat": 20.6917506, "lng": -103.321979, "status": "STARTED"}, "-OLAn4daEMkrRCcOnx1w": {"at": 1741807180553, "lat": 20.6915945, "lng": -103.3218892, "status": "STARTED"}, "-OLAn55AokHwvUjth6yA": {"at": 1741807182384, "lat": 20.6914441, "lng": -103.3218058, "status": "STARTED"}, "-OLAn5Yo0Cm_9jrpQNy1": {"at": 1741807184280, "lat": 20.6913016, "lng": -103.3217127, "status": "STARTED"}, "-OLAn62235i5j1OQLh9G": {"at": 1741807186279, "lat": 20.6911791, "lng": -103.3216322, "status": "STARTED"}, "-OLAn6_ZrxqWXD_76057": {"at": 1741807188489, "lat": 20.6910688, "lng": -103.3215617, "status": "STARTED"}, "-OLAn72fRcYFlkBINa_4": {"at": 1741807190416, "lat": 20.6909758, "lng": -103.3214932, "status": "STARTED"}, "-OLAn7Xo_aQVYNr9TXL2": {"at": 1741807192409, "lat": 20.6908882, "lng": -103.3214292, "status": "STARTED"}, "-OLAn8A2znuvlJLvOhM2": {"at": 1741807194984, "lat": 20.690801, "lng": -103.3213736, "status": "STARTED"}, "-OLAn8a_zCwBuoFMmIEy": {"at": 1741807196745, "lat": 20.6907123, "lng": -103.3213201, "status": "STARTED"}, "-OLAn8zzd4qKw3gAl8rY": {"at": 1741807198371, "lat": 20.6906079, "lng": -103.3212526, "status": "STARTED"}, "-OLAn9Tc6fMD5D6yiMcZ": {"at": 1741807200333, "lat": 20.6904934, "lng": -103.3211973, "status": "STARTED"}, "-OLAn9y3Dxy7M87hhZgx": {"at": 1741807202345, "lat": 20.6903796, "lng": -103.3211298, "status": "STARTED"}, "-OLAnARE5tQco9s7C7nW": {"at": 1741807204275, "lat": 20.6902701, "lng": -103.3210573, "status": "STARTED"}, "-OLAnAwztzM3MTgJ52Ov": {"at": 1741807206372, "lat": 20.6901862, "lng": -103.3209588, "status": "STARTED"}, "-OLAnBSXJSJ45V268PPz": {"at": 1741807208454, "lat": 20.6901044, "lng": -103.3208916, "status": "STARTED"}, "-OLAnBv5l52e0A4ofqma": {"at": 1741807210347, "lat": 20.6900239, "lng": -103.3208437, "status": "STARTED"}, "-OLAnCd0XVPMvyzWXWWa": {"at": 1741807213286, "lat": 20.6899225, "lng": -103.3207842, "status": "STARTED"}, "-OLAnDMYp91DRaQA5P8B": {"at": 1741807216264, "lat": 20.6898357, "lng": -103.3207393, "status": "STARTED"}, "-OLAnE7f6UWxWwk2CZFQ": {"at": 1741807219408, "lat": 20.6897534, "lng": -103.3206805, "status": "STARTED"}, "-OLAnEs4JMaXorInwH0x": {"at": 1741807222440, "lat": 20.6896473, "lng": -103.320611, "status": "STARTED"}, "-OLAnF_eK7TIyLQLwmNo": {"at": 1741807225359, "lat": 20.6895322, "lng": -103.3205285, "status": "STARTED"}, "-OLAnG2o4DEg5PiGoCvi": {"at": 1741807227288, "lat": 20.6894341, "lng": -103.3204737, "status": "STARTED"}, "-OLAnGXkmA_wTJUYwt94": {"at": 1741807229268, "lat": 20.6893346, "lng": -103.320413, "status": "STARTED"}, "-OLAnHI9e7ugxdWtV17y": {"at": 1741807232366, "lat": 20.6892512, "lng": -103.320346, "status": "STARTED"}, "-OLAnIFHuspuJDvbdZDB": {"at": 1741807236279, "lat": 20.6891552, "lng": -103.320286, "status": "STARTED"}, "-OLAnIjiQ4vdOGg0GigA": {"at": 1741807238291, "lat": 20.6890786, "lng": -103.3202072, "status": "STARTED"}, "-OLAnJDwWmpB9katGkRA": {"at": 1741807240289, "lat": 20.6889507, "lng": -103.3201403, "status": "STARTED"}, "-OLAnJiRE8QM_ktd3Dcy": {"at": 1741807242305, "lat": 20.6888064, "lng": -103.3200517, "status": "STARTED"}, "-OLAnKC-DMRHdIggbJDr": {"at": 1741807244261, "lat": 20.6886776, "lng": -103.3199772, "status": "STARTED"}, "-OLAnKg6p8WqduIPAJ2U": {"at": 1741807246252, "lat": 20.6885752, "lng": -103.319907, "status": "STARTED"}, "-OLAnLDXHjX2BDwnr5JD": {"at": 1741807248454, "lat": 20.6885048, "lng": -103.3198307, "status": "STARTED"}, "-OLAnUwZn4t0CYQQ9FSJ": {"at": 1741807288259, "lat": 20.6883937, "lng": -103.3198147, "status": "STARTED"}, "-OLAnVfNfQ1utERKysdM": {"at": 1741807291260, "lat": 20.6882846, "lng": -103.3198657, "status": "STARTED"}, "-OLAnW9BAevN49lAdLoy": {"at": 1741807293233, "lat": 20.6881901, "lng": -103.3199925, "status": "STARTED"}, "-OLAnWOkP7CaadDWbVDC": {"at": 1741807294229, "lat": 20.6881272, "lng": -103.3200792, "status": "STARTED"}, "-OLAnWe8zeA-JjxW6Zg9": {"at": 1741807295278, "lat": 20.6880482, "lng": -103.3201813, "status": "STARTED"}, "-OLAnX9nb8wOvPpIOAIQ": {"at": 1741807297368, "lat": 20.6879159, "lng": -103.3203386, "status": "STARTED"}, "-OLAnXO5gVpScYuhTjXf": {"at": 1741807298282, "lat": 20.6878495, "lng": -103.3204349, "status": "STARTED"}, "-OLAnXcV4onmYWIOUKJc": {"at": 1741807299269, "lat": 20.6877839, "lng": -103.3205299, "status": "STARTED"}, "-OLAnXrcSiIRBLEh2AqD": {"at": 1741807300237, "lat": 20.6877188, "lng": -103.3206117, "status": "STARTED"}, "-OLAnY6YbSirmdhHj-ZI": {"at": 1741807301255, "lat": 20.6876535, "lng": -103.3207061, "status": "STARTED"}, "-OLAnYMN2Ld3542JBZZt": {"at": 1741807302269, "lat": 20.6875882, "lng": -103.3207992, "status": "STARTED"}, "-OLAnYauIyWKwpSzo5as": {"at": 1741807303263, "lat": 20.6875291, "lng": -103.3208877, "status": "STARTED"}, "-OLAnYpt_4J3uvRBt6ZU": {"at": 1741807304222, "lat": 20.6874694, "lng": -103.3209717, "status": "STARTED"}, "-OLAnZ4U7JvI2uWLtw5f": {"at": 1741807305219, "lat": 20.6874123, "lng": -103.3210508, "status": "STARTED"}, "-OLAnZLHNd_CU2IEgIo7": {"at": 1741807306294, "lat": 20.6873554, "lng": -103.3211293, "status": "STARTED"}, "-OLAnZZvDonJKE2iLIk4": {"at": 1741807307232, "lat": 20.6873015, "lng": -103.3212068, "status": "STARTED"}, "-OLAn_4NJWUqD5MdgThO": {"at": 1741807309309, "lat": 20.6872005, "lng": -103.3213574, "status": "STARTED"}, "-OLAn_YasFOUj58gq9Nq": {"at": 1741807311243, "lat": 20.6870706, "lng": -103.3215062, "status": "STARTED"}, "-OLAna2GwBAMRLBa61a7": {"at": 1741807313270, "lat": 20.6869506, "lng": -103.3216274, "status": "STARTED"}, "-OLAnaX6phySnEjVMP0N": {"at": 1741807315244, "lat": 20.6868705, "lng": -103.3217449, "status": "STARTED"}, "-OLAnb0fHdS859Fky11I": {"at": 1741807317264, "lat": 20.6868005, "lng": -103.3218552, "status": "STARTED"}, "-OLAnbkW_WM62-IPo3KG": {"at": 1741807320262, "lat": 20.6867053, "lng": -103.321952, "status": "STARTED"}, "-OLAnrbd08u2hy3uNaaC": {"at": 1741807385229, "lat": 20.6865807, "lng": -103.3219678, "status": "STARTED"}, "-OLAns73ernILxAXrTdA": {"at": 1741807387303, "lat": 20.686482, "lng": -103.3219492, "status": "STARTED"}, "-OLAnsb8RQNaaAFddub-": {"at": 1741807389294, "lat": 20.686374, "lng": -103.3219015, "status": "STARTED"}, "-OLAnt4fsNJZJ00D7vTP": {"at": 1741807391248, "lat": 20.6862752, "lng": -103.3218401, "status": "STARTED"}, "-OLAntoM_4Q8XHCzAUC3": {"at": 1741807394235, "lat": 20.6861813, "lng": -103.3217983, "status": "STARTED"}, "-OLAnumugfRgUSFVp7j1": {"at": 1741807398238, "lat": 20.6860558, "lng": -103.3217869, "status": "STARTED"}, "-OLAnvH1GF8q5iV2wMy7": {"at": 1741807400231, "lat": 20.6859539, "lng": -103.3218, "status": "STARTED"}, "-OLAnvmH4BX8SntNsg7X": {"at": 1741807402294, "lat": 20.6858334, "lng": -103.3218277, "status": "STARTED"}, "-OLAnwGoc6sG9F9jX4tr": {"at": 1741807404312, "lat": 20.68568, "lng": -103.3218871, "status": "STARTED"}, "-OLAnwW7b6TZnMDe6WyF": {"at": 1741807405292, "lat": 20.6855809, "lng": -103.3219058, "status": "STARTED"}, "-OLAnwl4cq2QOcpMVmKd": {"at": 1741807406314, "lat": 20.6854835, "lng": -103.3219169, "status": "STARTED"}, "-OLAnwzpl3EX1XC6rjMj": {"at": 1741807407258, "lat": 20.6853884, "lng": -103.3219337, "status": "STARTED"}, "-OLAnxTirIkM5fdB03_Z": {"at": 1741807409235, "lat": 20.685243, "lng": -103.3220011, "status": "STARTED"}, "-OLAnxxqadbN1RSwYXuC": {"at": 1741807411227, "lat": 20.685141, "lng": -103.3220679, "status": "STARTED"}, "-OLAnyTG0PCI2U6fsiqV": {"at": 1741807413301, "lat": 20.6850417, "lng": -103.3220905, "status": "STARTED"}, "-OLAnzBjydlYtjqIkrVw": {"at": 1741807416275, "lat": 20.6849251, "lng": -103.3221286, "status": "STARTED"}, "-OLAnzvZx5jBknkVLZDj": {"at": 1741807419272, "lat": 20.6847775, "lng": -103.3221648, "status": "STARTED"}, "-OLAo-ATrSw6Ow4WOUd-": {"at": 1741807420291, "lat": 20.6846802, "lng": -103.3221787, "status": "STARTED"}, "-OLAo-PybDxwgK0YnFPs": {"at": 1741807421283, "lat": 20.6845828, "lng": -103.3221866, "status": "STARTED"}, "-OLAo-ec_k45XcqDAzsv": {"at": 1741807422285, "lat": 20.6844889, "lng": -103.3221868, "status": "STARTED"}, "-OLAo07zEpwXKTo7yJ4F": {"at": 1741807424228, "lat": 20.6843203, "lng": -103.3222089, "status": "STARTED"}, "-OLAo0dX1B3_24G1P5jF": {"at": 1741807426311, "lat": 20.6841901, "lng": -103.3222925, "status": "STARTED"}, "-OLAo16snv4dX_o-zaSs": {"at": 1741807428253, "lat": 20.6840938, "lng": -103.322374, "status": "STARTED"}, "-OLAo1rHmAediWuYNGKh": {"at": 1741807431287, "lat": 20.6840147, "lng": -103.3224681, "status": "STARTED"}, "-OLAo3Ze1ivYDCXx21eQ": {"at": 1741807438287, "lat": 20.6839208, "lng": -103.3225097, "status": "STARTED"}, "-OLAo6-KfsQNocn4wroL": {"at": 1741807448250, "lat": 20.6837955, "lng": -103.322489, "status": "STARTED"}, "-OLAo6itvwnT6L2gNzDp": {"at": 1741807451230, "lat": 20.683677, "lng": -103.3225063, "status": "STARTED"}, "-OLAo7SlEZM3B2CRVoFB": {"at": 1741807454230, "lat": 20.6835606, "lng": -103.3225355, "status": "STARTED"}, "-OLAo8BldwElrY8CyJKP": {"at": 1741807457238, "lat": 20.6834477, "lng": -103.3225509, "status": "STARTED"}, "-OLAo8vlJ2nhVFt6oBQW": {"at": 1741807460246, "lat": 20.6833257, "lng": -103.3225921, "status": "STARTED"}, "-OLAo9fi45ofdAe3sf5K": {"at": 1741807463315, "lat": 20.6832116, "lng": -103.3226323, "status": "STARTED"}, "-OLAoJVzEb4bx_0V4SrQ": {"at": 1741807503588, "lat": 20.6831917, "lng": -103.322655, "status": "REACHED"}}, "-OLk3ImdYbf9HCq2LALR": {"-OLoo6O61aWvtVex78_Q": {"at": 1742495314627, "lat": 20.6857155, "lng": -103.3130332, "status": "ACCEPTED"}, "-OLoo8kg7HaNQp4lrqBR": {"at": 1742495324328, "lat": 20.6857015, "lng": -103.3130299, "status": "STARTED"}, "-OLoo9xQe5caAuy3kJSY": {"at": 1742495329239, "lat": 20.685693, "lng": -103.3130282, "status": "REACHED"}}, "-OM8pyiEht1hZ9t3kPw-": {"-OM8qUAGM08gRUan-T69": {"at": 1742848257862, "lat": 20.6856687, "lng": -103.3129029, "status": "ACCEPTED"}, "-OM8qXAMILL5vhPM_0xR": {"at": 1742848270156, "lat": 20.685757, "lng": -103.3129581, "status": "ACCEPTED"}}}, "userNotifications": {"TEPe0cfPgBSk1OWAdW88ntTExLp1": {"-OKsd6GycVFdBt1LUXD1": {"dated": 1741485797836, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OKsdKvBSWUgvLUmbu9R": {"dated": 1741485858515, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OL-B_MOd7zKzXvvZg8B": {"dated": 1741612537312, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OL-DmXn5W1Mo_uLGTrM": {"dated": 1741613115577, "msg": "Recibiste una calificación de estrella 5", "title": "Tienes una notificación"}, "-OL-GECwOvrr1E4CVn0G": {"dated": 1741613757315, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OL02MyB4WBJ1bW180xv": {"dated": 1741626900370, "msg": "Recibiste una calificación de estrella 5", "title": "Tienes una notificación"}, "-OL06yWqW6hNZTIuOMKY": {"dated": 1741628106876, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OL0LXWSalpdLngTxpZR": {"dated": 1741631924323, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}, "-OL0Ll9BRbiNbb3hyFm3": {"dated": 1741631984274, "msg": "Conductor cerca de ti", "title": "Tienes una notificación"}, "-OL0O6H-zB_BLp1w33ZW": {"dated": 1741632599174, "msg": "Has llegado al destino. Complete el pago.", "title": "Tienes una notificación"}, "-OL0O6Kl9qjoKgIy5ZKT": {"dated": 1741632599415, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OL1asF71v5_qc7G4jjL": {"dated": 1741652984848, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OL1av_VrYhEgIWF_UYa": {"dated": 1741652998502, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}, "-OL1bI3AFomIhuXvsDB_": {"dated": 1741653094674, "msg": "Conductor cerca de ti", "title": "Tienes una notificación"}, "-OL1bIq9VLOcmJgzpGfO": {"dated": 1741653097872, "msg": "El conductor ha comenzado. Tu identificación de reserva esTJCARV", "title": "Tienes una notificación"}, "-OL536N4OMI1jv5k0pl8": {"dated": 1741710980621, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OL5UcvcSa8LQjNs3DQb": {"dated": 1741718195950, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}, "-OL5UeGvh_fwiUaXqorX": {"dated": 1741718201474, "msg": "El conductor ha comenzado. Tu identificación de reserva esDKJAWA", "title": "Tienes una notificación"}, "-OL5fokyd0o61OmeNr-d": {"dated": 1741721390148, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OL5fpDV5I4i7cKeiO53": {"dated": 1741721392041, "msg": "Has llegado al destino. Complete el pago.", "title": "Tienes una notificación"}, "-OLACrijky37EbYLSJR7": {"dated": 1741797424054, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OLAcyP5J9mj_rov1-3c": {"dated": 1741804529293, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}, "-OLAd-q3lW7R15DnvZ6n": {"dated": 1741804539276, "msg": "El conductor ha comenzado. Tu identificación de reserva esTCFHFV", "title": "Tienes una notificación"}, "-OLAoKMfW_8HS9dTuvP6": {"dated": 1741807506929, "msg": "Has llegado al destino. Complete el pago.", "title": "Tienes una notificación"}, "-OLAoNaXLYYbUVhtCu8g": {"dated": 1741807520176, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OLk3LqCf6mx3cRjGBCm": {"dated": 1742415687062, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OLooUhkVCMl7x8VJRYl": {"dated": 1742495415158, "msg": "Recibiste una calificación de estrella 5", "title": "Tienes una notificación"}, "-OLv5xl9A4ZTS-6wS11c": {"dated": 1742600920145, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OM8q-vYre8iexzR8LpX": {"dated": 1742848134889, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}}, "UlGIEKCBAaaUZLD6EdE4DRRIK6R2": {"-OL-BtvWnJlBKlmVh0u2": {"dated": 1741612621543, "msg": "Tienes una nueva solicitud de reserva", "title": "Tienes una notificación"}, "-OL-Cbg3S-vRly8Vm4za": {"dated": 1741612808970, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}, "-OL-D3ZaPv6QC9CnjZMB": {"dated": 1741612927276, "msg": "El conductor ha comenzado. Tu identificación de reserva esUGTIGK", "title": "Tienes una notificación"}, "-OL-DTA_Md0SuOupsYeN": {"dated": 1741613032171, "msg": "Has llegado al destino. Complete el pago.", "title": "Tienes una notificación"}, "-OL-Day6rOYCRG3Fw04z": {"dated": 1741613068173, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OL-Dazq4mVImNN1r_1y": {"dated": 1741613068284, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OL-Db-L6zzj3wRCsP4h": {"dated": 1741613068318, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OL-S9G3FrFl5YlVxDE4": {"dated": 1741616882761, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}, "-OL-SGDZo9lDlpj3Zvrs": {"dated": 1741616911275, "msg": "El conductor ha comenzado. Tu identificación de reserva esVPKDOX", "title": "Tienes una notificación"}, "-OL-SdhezfCgCl2o9kPz": {"dated": 1741617011569, "msg": "Has llegado al destino. Complete el pago.", "title": "Tienes una notificación"}, "-OL-SeQ-k8FxR1SjOrV2": {"dated": 1741617014471, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OL-SeTtHz3zUB86nL4x": {"dated": 1741617014720, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OL-SeVJ9WLWdSveuZsx": {"dated": 1741617014812, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OL-wtfvvqu1v2r9aoas": {"dated": 1741625203457, "msg": "Tienes una nueva solicitud de reserva", "title": "Tienes una notificación"}, "-OL00JdnWR11POL1ONb6": {"dated": 1741626362489, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OL00Jm8kDufrxU0xabp": {"dated": 1741626363024, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OL079CcF0YJHs3camNQ": {"dated": 1741628154734, "msg": "Tienes una nueva solicitud de reserva", "title": "Tienes una notificación"}, "-OL0NMxud85JQCcXvcL1": {"dated": 1741632405376, "msg": "El conductor ha comenzado. Tu identificación de reserva esKTGORQ", "title": "Tienes una notificación"}, "-OL0OGJAejwzZhu7-JV0": {"dated": 1741632640273, "msg": "Recibiste una calificación de estrella 5", "title": "Tienes una notificación"}, "-OL1dJPjnHc3h0Bpt2bl": {"dated": 1741653624502, "msg": "Has llegado al destino. Complete el pago.", "title": "Tienes una notificación"}, "-OL1dK9bqFkmW86NXF_G": {"dated": 1741653627566, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OL1dKI05abw3uY0CIWZ": {"dated": 1741653628106, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OL53M7mppcblEHOBWCV": {"dated": 1741711045175, "msg": "Tienes una nueva solicitud de reserva", "title": "Tienes una notificación"}, "-OL5fp1hzvgGmCqRLG_J": {"dated": 1741721391283, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OL5g0UKwEwnEBksWQus": {"dated": 1741721442267, "msg": "Recibiste una calificación de estrella 5", "title": "Tienes una notificación"}, "-OLAD8CToHiJ9D31b1zK": {"dated": 1741797495653, "msg": "Tienes una nueva solicitud de reserva", "title": "Tienes una notificación"}, "-OLAoMtKASGx1QathMp_": {"dated": 1741807517275, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OLAoMxjSCzPbQbZwVKw": {"dated": 1741807517559, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OLAoNohVeFiN8B4yvdW": {"dated": 1741807521075, "msg": "Recibiste una calificación de estrella 5", "title": "Tienes una notificación"}, "-OLoo6YSytSVM4wQQ3N8": {"dated": 1742495316195, "msg": "Tienes una nueva solicitud de reserva", "title": "Tienes una notificación"}, "-OLoo7OTEK4MvNXr5q2A": {"dated": 1742495319653, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}, "-OLoo8vzka-3qaCE0sFD": {"dated": 1742495325958, "msg": "El conductor ha comenzado. Tu identificación de reserva esKSXSAF", "title": "Tienes una notificación"}, "-OLooA9b1yVm3rlbL2nH": {"dated": 1742495330990, "msg": "Has llegado al destino. Complete el pago.", "title": "Tienes una notificación"}, "-OLooBi93PnfTgRkIIRD": {"dated": 1742495337361, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OLooBjhZ_quHq5mJFTg": {"dated": 1742495337460, "msg": "Pago realizado con éxito.", "title": "Tienes una notificación"}, "-OLooC9-ymorvMyPci82": {"dated": 1742495339141, "msg": "Billetera actualizada con éxito.", "title": "Tienes una notificación"}, "-OM8qTHpRVoTSPGZDEEY": {"dated": 1742848255166, "msg": "Tienes una nueva solicitud de reserva", "title": "Tienes una notificación"}, "-OM8qUCtZLI1yXTvkp9_": {"dated": 1742848258946, "msg": "Diegoaceptó su solicitud de reserva.", "title": "Tienes una notificación"}, "-OMCht-6bXK0Li69jvXt": {"dated": 1742913114125, "msg": "La reserva se cancela. IDENTIFICACIÓN :-OM8pyiEht1hZ9t3kPw-", "title": "Tienes una notificación"}, "-OMChtAzwf08uyGSiS7_": {"dated": 1742913114887, "msg": "La reserva se cancela. IDENTIFICACIÓN :-OM8pyiEht1hZ9t3kPw-", "title": "Tienes una notificación"}}}, "userRatings": {"UlGIEKCBAaaUZLD6EdE4DRRIK6R2": {"-OL-DmVboVTanmD-LZAH": {"rate": 5, "user": "TEPe0cfPgBSk1OWAdW88ntTExLp1"}, "-OL02MyjCnhgDwcAJ949": {"rate": 5, "user": "TEPe0cfPgBSk1OWAdW88ntTExLp1"}, "-OL0OGGWABQhZYrmIXmq": {"rate": 5, "user": "TEPe0cfPgBSk1OWAdW88ntTExLp1"}, "-OL51v3juyTbZjViaCbY": {"rate": 5, "user": "TEPe0cfPgBSk1OWAdW88ntTExLp1"}, "-OL5g0RUzNU0nndcnuvq": {"rate": 5, "user": "TEPe0cfPgBSk1OWAdW88ntTExLp1"}, "-OLAoNn4qlQ2bRKqGkd0": {"rate": 5, "user": "TEPe0cfPgBSk1OWAdW88ntTExLp1"}, "-OLooUd37DA6NmWofCTm": {"rate": 5, "user": "TEPe0cfPgBSk1OWAdW88ntTExLp1"}}}, "users": {"-OL4qwg5wSrYdd6IkSOa": {"approved": true, "createdAt": 1741707528942, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Ballesteros", "mobile": "+523313036516", "usertype": "admin", "walletBalance": 0}, "TEPe0cfPgBSk1OWAdW88ntTExLp1": {"approved": true, "createdAt": 1741462190708, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Ballesteros", "mobile": "+523311917230", "referralId": "KDULM", "savedAddresses": {"-OL-DT8MfCklgKtLSyKc": {"count": 1, "description": "Escuela Primaria Francisco <PERSON>, Avenida Puerto Melaque, Santa María, Guadalajara, Jalisco, Mexico", "lat": 20.6861861, "lng": -103.3184647}, "-OL-Sdeqnvpcy_NGDYeF": {"count": 1, "description": "Calle Álvarez <PERSON> Castillo 890, Santa María, Guadalajara, Jalisco, Mexico", "lat": 20.6858654, "lng": -103.3129226}, "-OL00IebULbi1tV_OolE": {"count": 1, "description": "Sta. Clem<PERSON> 1935, San Martin, 44710 Guadalajara, Jal., México", "lat": 20.6854854, "lng": -103.3066325}, "-OL1dJOppo9tVttlp6Ns": {"count": 1, "description": "Escuela Preparatoria No.2 de la Universidad de Guadalajara, Calle Álvarez del Castillo, Blanco y Cuéllar, Guadalajara, Jalisco, Mexico", "lat": 20.682959, "lng": -103.313954}, "-OLAoJamZginGhq6q7vP": {"count": 1, "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Guadalajara, Jalisco, Mexico", "lat": 20.6837515, "lng": -103.3223714}, "-OLooA0CMwU64vFizK-n": {"count": 1, "description": "Parroquia de San Juan Bosco, Calle Industria, San Juan Bosco, Guadalajara, Jalisco, Mexico", "lat": 20.6752755, "lng": -103.3143675}}, "signupViaReferral": " ", "term": true, "userPlatform": "ANDROID", "usertype": "customer", "walletBalance": 49629.85}, "UlGIEKCBAaaUZLD6EdE4DRRIK6R2": {"approved": true, "carApproved": true, "carType": "AUTO EJECUTIVO", "car_image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cars%2F-OKser6Z_1YanSXXhoAA?alt=media&token=b8c94268-c47a-40cc-91ce-baa07cc3e42f", "createdAt": 1741486082663, "driverActiveStatus": false, "email": "<EMAIL>", "firstName": "Diego", "lastName": "Ballesteros", "licenseImage": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/users%2FUlGIEKCBAaaUZLD6EdE4DRRIK6R2%2Flicense?alt=media&token=83f4f85e-d882-4e43-8154-520634a8403f", "licenseImageBack": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/users%2FUlGIEKCBAaaUZLD6EdE4DRRIK6R2%2FlicenseBack?alt=media&token=84ed8d4e-de22-416b-80d2-148d202e4be8", "mobile": "+523311917231", "other_info": "<PERSON><PERSON> os<PERSON>ro ", "pushToken": "ExponentPushToken[jfBcGOPEXbrVovGOUiidr7]", "queue": false, "rating": "5.0", "referralId": "VXOWC", "signupViaReferral": " ", "term": true, "updateAt": 1741486255786, "userPlatform": "ANDROID", "usertype": "driver", "vehicleMake": "Nissan", "vehicleModel": "Versa", "vehicleNumber": "eydg2638", "walletBalance": 303.24}, "admin0001": {"approved": true, "email": "<EMAIL>", "firstName": "Admin", "lastName": "Admin", "usertype": "admin"}, "r5Pd7zVyTLU0wxzkIPX1ASwW1qy2": {"approved": true, "carApproved": true, "carType": "AUTO EJECUTIVO", "car_image": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/cars%2F-OL2EhvjkpojiHg7rPaR?alt=media&token=a6eb45a6-bc34-414a-b0a0-77d01221be6d", "createdAt": 1741660217867, "driverActiveStatus": false, "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "De la cruz", "licenseImage": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/users%2Fr5Pd7zVyTLU0wxzkIPX1ASwW1qy2%2Flicense?alt=media&token=9e71bab7-b638-4e7b-bda3-b7841695a58c", "licenseImageBack": "https://firebasestorage.googleapis.com/v0/b/transporte-vp.firebasestorage.app/o/users%2Fr5Pd7zVyTLU0wxzkIPX1ASwW1qy2%2FlicenseBack?alt=media&token=ce4c81cb-ee83-4451-906a-f529b6cf5153", "mobile": "+523339686073", "other_info": "Color gris oscuro ", "queue": false, "referralId": "TDLPF", "signupViaReferral": " ", "term": true, "updateAt": 1741663689628, "userPlatform": "ANDROID", "usertype": "driver", "vehicleMake": "Nissan ", "vehicleModel": "Versa", "vehicleNumber": "EYS4627", "walletBalance": 0}}, "walletHistory": {"TEPe0cfPgBSk1OWAdW88ntTExLp1": {"-OKsd57sL7bEiHIH1ALb": {"amount": "50000", "date": 1741485793845, "transaction_id": "1741485787248", "txRef": "wallet-TEPe0cfPgBSk1OWAdW88ntTExLp1-GWON", "type": "Credit"}, "-OKsdKuWzqXkqcMVuO0z": {"amount": 18.82, "date": 1741485858457, "txRef": "-OKsdIhBlrI4wqWJqubG", "type": "Debit"}, "-OKxtWVNiQZYc0E5l1TZ": {"amount": 18.82, "date": 1741573986326, "transaction_id": "", "txRef": "Admin Credit", "type": "Credit"}, "-OL-B_LmJhwSiQih37K9": {"amount": 19.84, "date": 1741612537263, "txRef": "-OL-BXrKyhUfRWy3VNMb", "type": "Debit"}, "-OL-GE9mSgbmvaYjaxCD": {"amount": 34.87, "date": 1741613757106, "txRef": "-OL-GCzTM8gbJx3h-rv3", "type": "Debit"}, "-OL06yWIja7pwaq4a2SV": {"amount": 87.55, "date": 1741628106832, "txRef": "-OL06mnlviTeVjzLa7Re", "type": "Debit"}, "-OL1asEV173WgqH6pH08": {"amount": 13.47, "date": 1741652984797, "txRef": "-OL1alYJR5_c1SYYyxmx", "type": "Debit"}, "-OL536JsvC74jCV05pUM": {"amount": 87.53, "date": 1741710980330, "txRef": "-OL535EPJUf8X_wm8Jt9", "type": "Debit"}, "-OLACrhr9c-QzcrWmsY8": {"amount": 89.25, "date": 1741797423981, "txRef": "-OLACpjOSiMkgkwhUHIp", "type": "Debit"}, "-OLk3LpAPCZgb6xiPND3": {"amount": 18.82, "date": 1742415686983, "txRef": "-OLk3ImdYbf9HCq2LALR", "type": "Debit"}, "-OLv5xkMh19sf1rOUj2D": {"amount": 18.82, "date": 1742600920083, "txRef": "-OLv5vmMbeLRlxgfB_4K", "type": "Debit"}, "-OM8q-uk_jPDx16gtOGp": {"amount": 18.82, "date": 1742848134826, "txRef": "-OM8pyiEht1hZ9t3kPw-", "type": "Debit"}, "-OMChtIpXFWAgf-GlRFt": {"amount": 18.82, "date": 1742913115379, "transaction_id": "", "txRef": "Admin Credit", "type": "Credit"}}, "UlGIEKCBAaaUZLD6EdE4DRRIK6R2": {"-OL-DaygQLbOCUutzBwj": {"amount": "17.25", "date": 1741613068125, "txRef": "-OL-BXrKyhUfRWy3VNMb", "type": "Credit"}, "-OL-SeSIYFCloLtH7r-N": {"amount": "30.32", "date": 1741617014607, "txRef": "-OL-GCzTM8gbJx3h-rv3", "type": "Credit"}, "-OL00JinpgThfZearzRk": {"amount": "17.36", "date": 1741626362808, "txRef": "-OL-wqfI7mD5dVrPFnOA", "type": "Debit"}, "-OL00JkHl6Oav34jxsxX": {"amount": "15.10", "date": 1741626362821, "txRef": "-OL-wqfI7mD5dVrPFnOA", "type": "Credit"}, "-OL0O6GMQsbsLZjvZJDE": {"amount": "76.13", "date": 1741632598939, "txRef": "-OL06mnlviTeVjzLa7Re", "type": "Credit"}, "-OL1dKFCVnV5Q1FQ3gOj": {"amount": "11.71", "date": 1741653627920, "txRef": "-OL1alYJR5_c1SYYyxmx", "type": "Credit"}, "-OL5fo7JB3pmdo90Ju2f": {"amount": "76.11", "date": 1741721387479, "txRef": "-OL535EPJUf8X_wm8Jt9", "type": "Credit"}, "-OLAoMunol8fY5-v9Rhe": {"amount": "77.61", "date": 1741807517361, "txRef": "-OLACpjOSiMkgkwhUHIp", "type": "Credit"}, "-OLooC61RKYUNjmszFhS": {"amount": "16.37", "date": 1742495338944, "txRef": "-OLk3ImdYbf9HCq2LALR", "type": "Credit"}}}}