import React, { useState, useEffect, useRef } from 'react';
import {
    StyleSheet,
    View,
    Text,
    ScrollView,
    Dimensions,
    Alert,
    TouchableOpacity,
    Linking
} from 'react-native';
import MapView, { <PERSON><PERSON>, Polyline, PROVIDER_GOOGLE } from 'react-native-maps';
import { useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../common/theme';

var { width } = Dimensions.get('window');

export default function RideDetails(props) {

    const { data } = props.route.params;
    const paramData = data;
    const auth = useSelector(state => state.auth);

    return (
        <View style={styles.mainView}>
            <ScrollView>
                <View style={styles.mapView}>
                    <MapView
                        style={styles.map}
                        provider={PROVIDER_GOOGLE}
                        region={{
                            latitude: ((paramData.pickup.lat + paramData.drop.lat) / 2),
                            longitude: ((paramData.pickup.lng + paramData.drop.lng) / 2),
                            latitudeDelta: 0.3,
                            longitudeDelta: 0.3
                        }}
                    >
                        <Marker
                            coordinate={{ latitude: paramData.pickup.lat, longitude: paramData.pickup.lng }}
                            title="Inicio"
                            description={paramData.pickup.add}
                        />
                        <Marker
                            coordinate={{ latitude: paramData.drop.lat, longitude: paramData.drop.lng }}
                            title="Destino"
                            description={paramData.drop.add}
                        />
                        {paramData.coords &&
                            <Polyline
                                coordinates={paramData.coords}
                                strokeWidth={4}
                                strokeColor={colors.INDICATOR_BLUE}
                                geodesic={true}
                            />
                        }
                    </MapView>
                </View>
            </ScrollView>
        </View>
    );
}

const styles = StyleSheet.create({
    mainView: {
        flex: 1,
        backgroundColor: colors.WHITE,
    },
    mapView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 300,
        marginVertical: 10,
    },
    map: {
        flex: 1,
        width: width - 20,
        height: 300,
    },
});
