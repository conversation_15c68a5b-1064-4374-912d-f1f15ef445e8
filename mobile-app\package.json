{"name": "mobile-app", "version": "1.0.0", "main": "index.js", "private": true, "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "clear": "expo r -c", "eject": "expo eject", "update": "expo update"}, "dependencies": {"@expo/config-plugins": "~8.0.0", "@expo/prebuild-config": "~7.0.0", "@mapbox/polyline": "1.1.1", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-firebase/app": "^19.2.2", "@react-native-firebase/app-check": "^19.2.2", "@react-native-firebase/auth": "^19.2.2", "@react-native-google-signin/google-signin": "^10.0.1", "@react-native-masked-view/masked-view": "0.3.1", "@react-native-picker/picker": "2.7.5", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "aws-sdk": "^2.1692.0", "buffer": "^6.0.3", "common": "1.0.0", "expo": "~51.0.14", "expo-apple-authentication": "~6.4.1", "expo-application": "~5.9.1", "expo-asset": "~10.0.9", "expo-av": "~14.0.7", "expo-build-properties": "~0.12.3", "expo-constants": "~16.0.2", "expo-crypto": "~13.0.2", "expo-dev-client": "~4.0.18", "expo-device": "~6.0.2", "expo-document-picker": "~12.0.2", "expo-file-system": "~17.0.1", "expo-font": "~12.0.7", "expo-image-picker": "~15.0.5", "expo-intent-launcher": "~11.0.1", "expo-linking": "~6.3.1", "expo-localization": "~15.0.3", "expo-location": "~17.0.1", "expo-notifications": "~0.28.9", "expo-speech": "~12.0.2", "expo-splash-screen": "~0.27.5", "expo-status-bar": "~1.12.1", "expo-task-manager": "~11.8.2", "expo-updates": "~0.25.17", "i18n-js": "3.8.0", "moment": "2.29.1", "react": "18.2.0", "react-dom": "18.2.0", "react-native": "0.74.2", "react-native-actions-sheet": "0.8.29", "react-native-date-picker": "^4.2.13", "react-native-dialog": "9.3.0", "react-native-elements": "4.0.0-rc.2", "react-native-gesture-handler": "~2.16.1", "react-native-gifted-chat": "^1.1.0", "react-native-google-places-autocomplete": "2.4.1", "react-native-maps": "1.14.0", "react-native-onboarding-swiper": "^1.2.0", "react-native-reanimated": "~3.10.1", "react-native-safe-area-context": "4.10.1", "react-native-screens": "3.31.1", "react-native-segmented-control-tab": "4.0.0", "react-native-simple-radio-button": "2.7.4", "react-native-star-rating-widget": "1.7.1", "react-native-svg": "15.2.0", "react-native-uuid": "^2.0.1", "react-native-vector-icons": "9.0.0", "react-native-web": "~0.19.6", "react-native-webview": "13.8.6", "react-redux": "7.2.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "babel-preset-expo": "~11.0.0"}}